package com.yiwise.dialogflow.chatcontroller;

import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.dialogflow.entity.vo.LLMStepTestRequestVO;
import com.yiwise.dialogflow.entity.vo.LLMStepTestResponseVO;
import com.yiwise.dialogflow.service.LLMStepTestService;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;

import javax.annotation.Resource;

/**
 * 大模型流程测试
 */
@RestController
@RequestMapping("apiBot/v3/llmStep/")
public class LLMStepTestController {

    @Resource
    private LLMStepTestService llmStepTestService;

    @PostMapping(value = "test", produces = {MediaType.TEXT_EVENT_STREAM_VALUE})
    public Flux<ResultObject<LLMStepTestResponseVO>> test(@RequestBody LLMStepTestRequestVO request) {
        return llmStepTestService.test(request);
    }

}
