package com.yiwise.dialogflow.chatcontroller;

import com.yiwise.base.model.annotation.auth.InnerOnly;
import com.yiwise.base.model.annotation.auth.NoLogin;
import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.dialogflow.engine.share.BotMetaData;
import com.yiwise.dialogflow.engine.share.MagicActivityConfig;
import com.yiwise.dialogflow.engine.share.enums.RobotSnapshotUsageTargetEnum;
import com.yiwise.dialogflow.engine.share.model.BotAsrConfig;
import com.yiwise.dialogflow.engine.share.service.RemoteBotMetaDataService;
import com.yiwise.dialogflow.service.RobotSnapshotService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;

@RestController
@RequestMapping("apiBot/v3/botMetaData")
public class BotMetaDataController implements RemoteBotMetaDataService {

    @Resource
    private RobotSnapshotService robotSnapshotService;

    @NoLogin
    @InnerOnly
    @Override
    public ResultObject<Integer> getLastVersionByDialogFlowId(@NotNull Long dialogFlowId,
                                                             @NotNull RobotSnapshotUsageTargetEnum usageTarget) {
        return ResultObject.success(robotSnapshotService.getLastVersionByDialogFlowId(dialogFlowId, usageTarget));
    }

    @NoLogin
    @InnerOnly
    @Override
    public ResultObject<BotMetaData> getBotMetaData(Long dialogFlowId, RobotSnapshotUsageTargetEnum usageTarget, Integer version) {
        return ResultObject.success(robotSnapshotService.getBotMetaDataByDialogFlowId(dialogFlowId, usageTarget, version));
    }

    @NoLogin
    @InnerOnly
    @Override
    public ResultObject<BotAsrConfig> getRealAsrConfig(Long botId, RobotSnapshotUsageTargetEnum usageTarget, Integer version) {
        return ResultObject.success(robotSnapshotService.getRealtimeAsrConfig(botId, usageTarget, version));
    }

    @NoLogin
    @InnerOnly
    @Override
    public ResultObject<MagicActivityConfig> getMagicActivityConfig(Long botId, RobotSnapshotUsageTargetEnum usageTarget, Integer version, Long callJobId) {
        return ResultObject.success(robotSnapshotService.getMagicActivityConfig(botId, usageTarget, version, callJobId));
    }

}
