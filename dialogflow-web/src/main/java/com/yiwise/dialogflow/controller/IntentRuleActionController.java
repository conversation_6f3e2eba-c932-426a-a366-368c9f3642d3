package com.yiwise.dialogflow.controller;

import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.dialogflow.aop.TenantIsolation;
import com.yiwise.dialogflow.entity.dto.DialogFlowIntentRuleRearrangeDTO;
import com.yiwise.dialogflow.entity.enums.ActionCategoryEnum;
import com.yiwise.dialogflow.entity.po.intent.IntentRuleActionPO;
import com.yiwise.dialogflow.entity.query.IntentActionQuery;
import com.yiwise.dialogflow.entity.vo.IntentRuleActionVO;
import com.yiwise.dialogflow.entity.vo.sync.BotSyncResultVO;
import com.yiwise.dialogflow.entity.vo.sync.RuleActionSyncVO;
import com.yiwise.dialogflow.service.intent.IntentRuleActionService;
import com.yiwise.dialogflow.utils.SecurityUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> @ yiwise . com>
 * @version v0.1 2022/5/30
 * @class <code>IntentRuleActionController</code>
 * @see
 * @since JDK1.8
 */
@RestController
@RequestMapping("/apiBot/v3/intentRuleAction")
public class IntentRuleActionController {

    @Resource
    private IntentRuleActionService intentRuleActionService;


    /**
     * 保存动作配置
     *
     * @param actionPO
     * @return
     */
    @PostMapping("/save")
    @TenantIsolation("#actionPO.botId")
    public ResultObject saveRuleCondition(@RequestBody IntentRuleActionPO actionPO) {
        actionPO.setUpdateUserId(SecurityUtils.getUserId());
        if (actionPO.getId() == null) {
            return ResultObject.success(intentRuleActionService.addIntentRuleAction(actionPO, true, SecurityUtils.getTenantId()), "保存成功");
        } else {
            return ResultObject.success(intentRuleActionService.updateIntentRuleAction(actionPO, SecurityUtils.getTenantId()), "保存成功");
        }
    }

    /**
     * 获取规则动作配置列表
     *
     * @param botId
     * @param actionType
     * @return
     */
    @GetMapping("/list")
    public ResultObject<List<IntentRuleActionVO>> getIntentRuleActionList(@RequestParam Long botId,
                                                                         @RequestParam(required = false) List<ActionCategoryEnum> actionType) {
        IntentActionQuery query = new IntentActionQuery();
        query.setBotId(botId);
        query.setActionTypeList(actionType);
        query.setTenantId(SecurityUtils.getTenantId());
        query.setFilterInvalidStepNode(true);
        query.setFilterInvalidKnowledge(true);
        return ResultObject.success(intentRuleActionService.getIntentRuleActionDetailList(query));
    }

    /**
     * 获取规则动作配置列表(包含统计数据)
     */
    @PostMapping("/list")
    public ResultObject<List<IntentRuleActionVO>> listWithStats(@RequestBody @Validated IntentActionQuery query) {
        return ResultObject.success(intentRuleActionService.getIntentRuleActionDetailList(query));
    }

    /**
     * 批量删除
     *
     * @param param
     * @return
     */
    @PostMapping("/batchDelete")
    @TenantIsolation("#param.botId")
    public ResultObject batchDeleteAction(@RequestBody @Validated DialogFlowIntentRuleRearrangeDTO param) {
        return ResultObject.success(intentRuleActionService.batchDeleteAction(param.getBotId(), param.getIdList(),
                SecurityUtils.getTenantId(), SecurityUtils.getUserId()), "删除成功");
    }

    /**
     * 获取动作配置详情
     *
     * @param id
     * @return
     */
    @GetMapping("/getAction")
    public ResultObject<IntentRuleActionPO> getIntentRuleAction(@RequestParam String id) {
        return ResultObject.success(intentRuleActionService.getIntentRuleActionDetail(id).orElse(null), "操作成功");
    }

    @PostMapping("/sync")
    @TenantIsolation("#syncVO.srcBotId")
    @TenantIsolation("#syncVO.targetBotIdList")
    public ResultObject<BotSyncResultVO> sync(@RequestBody RuleActionSyncVO syncVO) {
        syncVO.setTenantId(SecurityUtils.getTenantId());
        syncVO.setCurrentUserId(SecurityUtils.getUserId());
        return ResultObject.success(intentRuleActionService.sync(syncVO));
    }
}
