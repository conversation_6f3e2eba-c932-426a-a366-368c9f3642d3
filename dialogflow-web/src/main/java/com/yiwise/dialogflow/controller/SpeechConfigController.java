package com.yiwise.dialogflow.controller;

import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.dialogflow.aop.TenantIsolation;
import com.yiwise.dialogflow.entity.po.BotSpeechConfigPO;
import com.yiwise.dialogflow.entity.vo.sync.BotSyncResultVO;
import com.yiwise.dialogflow.entity.vo.sync.SpeechConfigSyncVO;
import com.yiwise.dialogflow.service.BotConfigService;
import com.yiwise.dialogflow.utils.SecurityUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Validated
@RestController
@RequestMapping("apiBot/v3/config/speech")
public class SpeechConfigController {

    @Resource
    private BotConfigService botConfigService;

    @GetMapping("getByBotId")
    public ResultObject getByBotId(Long botId) {
        return ResultObject.success(botConfigService.getSpeechConfig(botId));
    }

    @PostMapping("update")
    @TenantIsolation("#config.botId")
    public ResultObject update(@RequestBody BotSpeechConfigPO config) {
        botConfigService.saveSpeechConfig(config.getBotId(), config, SecurityUtils.getUserId());
        return ResultObject.success();
    }

    @PostMapping(value = "/sync")
    @TenantIsolation("#syncVO.srcBotId")
    @TenantIsolation("#syncVO.targetBotIdList")
    public ResultObject<BotSyncResultVO> sync(@RequestBody SpeechConfigSyncVO syncVO) {
        syncVO.setCurrentUserId(SecurityUtils.getUserId());
        return ResultObject.success(botConfigService.sync(syncVO));
    }
}
