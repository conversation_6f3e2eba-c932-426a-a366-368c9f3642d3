package com.yiwise.dialogflow.controller.audio;

import com.yiwise.base.model.annotation.auth.NoLogin;
import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.aop.TenantIsolation;
import com.yiwise.dialogflow.entity.po.BatchSyncAudioRequestVO;
import com.yiwise.dialogflow.entity.po.TtsJobPO;
import com.yiwise.dialogflow.entity.query.AudioQuery;
import com.yiwise.dialogflow.entity.vo.SimpleUploadResultVO;
import com.yiwise.dialogflow.entity.vo.audio.AnswerAudioWrapVO;
import com.yiwise.dialogflow.entity.vo.audio.AudioCompleteProgressVO;
import com.yiwise.dialogflow.entity.vo.audio.AudioUploadResultVO;
import com.yiwise.dialogflow.entity.vo.audio.TtsComposeProgressVO;
import com.yiwise.dialogflow.entity.vo.audio.request.*;
import com.yiwise.dialogflow.service.AnswerAudioManagerService;
import com.yiwise.dialogflow.service.AudioUploadService;
import com.yiwise.dialogflow.utils.SecurityUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@Validated
@RestController
@RequestMapping("apiBot/v3/audio")
public class AudioManagerController {

    @Resource
    private AnswerAudioManagerService answerAudioManagerService;

    @Resource
    private AudioUploadService audioUploadService;

    @PostMapping("getListBySource")
    public ResultObject<PageResultObject<AnswerAudioWrapVO>> getListBySource(@RequestBody @Valid AnswerAudioRequestVO request) {
        return ResultObject.success(answerAudioManagerService.queryByCondition(request));
    }

    /**
     * 查询节点下所有答案音频列表
     * @param botId 机器人id
     * @param stepId 流程id
     * @param nodeId 节点id
     * @return 音频列表
     */
    @GetMapping("getListByNodeId")
    public ResultObject<List<AnswerAudioWrapVO>> getListByNodeId(@NotNull(message = "botId不能为空") Long botId,
                                                                @NotBlank(message = "stepId不能为空") String stepId,
                                                                @NotBlank(message = "nodeId不能为空") String nodeId) {
        return ResultObject.success(answerAudioManagerService.queryByNodeId(botId, stepId, nodeId));
    }

    /**
     * 查询知识下所有答案音频列表
     * @param botId 机器人id
     * @param knowledgeId 知识id
     * @return 音频列表
     */
    @GetMapping("getListByKnowledgeId")
    public ResultObject<List<AnswerAudioWrapVO>> getListByKnowledgeId(@NotNull(message = "botId不能为空") Long botId,
                                                                      @NotBlank(message = "knowledgeId不能为空") String knowledgeId) {
        return ResultObject.success(answerAudioManagerService.queryByKnowledgeId(botId, knowledgeId));
    }

    /**
     * 查询特殊语境下所有答案音频列表
     * @param botId 机器人id
     * @param specialAnswerId 特殊语境id
     * @return 音频列表
     */
    @GetMapping("getListBySpecialAnswerId")
    public ResultObject<List<AnswerAudioWrapVO>> getListBySpecialAnswerId(@NotNull(message = "botId不能为空") Long botId,
                                                                         @NotBlank(message = "stepId不能为空") String specialAnswerId) {
        return ResultObject.success(answerAudioManagerService.queryBySpecialAnswerId(botId, specialAnswerId));
    }

    @PostMapping("getAnswerAudioDetail")
    public ResultObject<AnswerAudioWrapVO> getAnswerAudioDetail(@RequestBody AnswerAudioDetailRequestVO request) {
        return ResultObject.success(answerAudioManagerService.wrapSingleAnswerAudio(request.getBotId(), request.getAnswerLocate()));
    }

    @PostMapping("uploadFile")
    @TenantIsolation("#botId")
    public ResultObject<AudioUploadResultVO> uploadFile(@RequestParam(value = "botId", required = false) @NotNull(message = "botId不能为空") Long botId,
                                                       @RequestParam(value = "autoCut", required = false, defaultValue = "true") @NotNull(message = "botId不能为空") Boolean autoCut,
                                                       @RequestParam(value = "file", required = false) @NotNull(message = "file不能为空") MultipartFile file) {

        return ResultObject.success(audioUploadService.uploadAudio(botId, file, autoCut));
    }

    @PostMapping("batchUpload")
    @TenantIsolation("#botId")
    public ResultObject uploadZip(@RequestParam(value = "botId", required = false) @NotNull(message = "botId不能为空") Long botId,
                                 @RequestParam(value = "file", required = false) @NotNull(message = "file不能为空") MultipartFile file) {

        return ResultObject.success(answerAudioManagerService.batchUploadAudio(botId, file, SecurityUtils.getUserId()));
    }

    @NoLogin
    @GetMapping("downloadAll")
    public ResultObject<SimpleUploadResultVO> downloadAll(@RequestParam(value = "botId", required = false) @NotNull(message = "botId不能为空") Long botId) {
        return ResultObject.success(answerAudioManagerService.downloadAudioZip(botId));
    }

    @NoLogin
    @PostMapping("batchDownload")
    public ResultObject<SimpleUploadResultVO> batchDownload(@RequestBody AudioQuery param) {
        return ResultObject.success(answerAudioManagerService.downloadZip(param.getBotId(), param.getAnswerAudioList()));
    }

    @PostMapping("updateAudio")
    @TenantIsolation("#request.botId")
    public ResultObject updateAudio(@RequestBody AnswerUpdateAudioRequestVO request) {
        answerAudioManagerService.updateAnswerAudio(request, SecurityUtils.getUserId());
        return ResultObject.success();
    }

    @PostMapping("startCompose")
    @TenantIsolation("#botId")
    public ResultObject<TtsJobPO> startCompose(@NotNull(message = "botId不能为空") Long botId) {
        return ResultObject.success(answerAudioManagerService.startCompose(botId, SecurityUtils.getUserId()));
    }

    @PostMapping("adjustVolume")
    @TenantIsolation("#request.botId")
    public ResultObject<AudioUploadResultVO> adjustVolume(@Validated @RequestBody AdjustVolumeRequestVO request) {
        return ResultObject.success(answerAudioManagerService.adjustVolume(request.getBotId(), request.getUrl(), request.getVolume()));
    }

    @NoLogin
    @PostMapping("batchAdjustVolume")
    @TenantIsolation("#request.botId")
    public ResultObject batchAdjustVolume(@Validated @RequestBody BatchAdjustVolumeRequestVO request) {
        return ResultObject.success(answerAudioManagerService.batchAdjustVolume(request, SecurityUtils.getUserId()));
    }

    @PostMapping("cutAudio")
    @TenantIsolation("#request.botId")
    public ResultObject<AudioUploadResultVO> cutAudio(@RequestBody CutAudioRequestVO request) {
        return ResultObject.success(audioUploadService.cutAudio(request.getBotId(), request.getUrl(), request.getStartTimeIndex(), request.getEndTimeIndex()));
    }

    @GetMapping("progress")
    public ResultObject<AudioCompleteProgressVO> progress(@NotNull(message = "botId不能为空") Long botId) {
        return ResultObject.success(answerAudioManagerService.getAudioCompleteProgress(botId));
    }

    @GetMapping("ttsProgress")
    public ResultObject<TtsComposeProgressVO> ttsCompleteProgress(@NotNull(message = "botId不能为空") Long botId) {
        return ResultObject.success(answerAudioManagerService.getTtsCompleteProgress(botId));
    }

    @PostMapping("copyAudio")
    @TenantIsolation("#request.fromBotId")
    @TenantIsolation("#request.toBotId")
    public ResultObject copyAudio(@RequestBody CopyAudioRequestVO request) {
        Integer syncSum = answerAudioManagerService.copyAudio(request.getFromBotId(), request.getToBotId(), SecurityUtils.getUserId());
        if (syncSum == 0) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "未找到相同的录音");
        } else {
            return ResultObject.success(String.format("成功同步%s段音频", syncSum));
        }
    }

    @PostMapping("/batchSyncAudio")
    public ResultObject<Void> batchSyncAudio(@RequestBody @Valid BatchSyncAudioRequestVO request) {
        answerAudioManagerService.batchSyncAudio(request, SecurityUtils.getUserId());
        return ResultObject.success(null);
    }

    @PostMapping("resetAll")
    @TenantIsolation("#botId")
    public ResultObject resetAll(@NotNull(message = "botId不能为空") Long botId) {
        answerAudioManagerService.resetAllManMadeAudio(botId, SecurityUtils.getUserId());
        return ResultObject.success();
    }

    @PostMapping("resetByAnswer")
    @TenantIsolation("#request.botId")
    public ResultObject resetByAnswer(@RequestBody ResetAudioRequestVO request) {
        answerAudioManagerService.resetByAnswer(request.getBotId(), request.getAnswerTextList(), SecurityUtils.getUserId());
        return ResultObject.success();
    }

    @GetMapping(value = "downloadAudio")
    public void downloadAudio(HttpServletResponse response, @NotBlank(message = "ossKey不能为空") String ossKey, String label) {
        answerAudioManagerService.downloadSingleAudio(response, ossKey, label);
    }

    /**
     * 同步音频到公共音频库
     */
    @PostMapping("/sync")
    @TenantIsolation("#request.botId")
    public ResultObject<Void> sync(@RequestBody @Valid SyncAudioRequestVO request) {
        answerAudioManagerService.sync(request, SecurityUtils.getUserId());
        return ResultObject.success(null);
    }
}
