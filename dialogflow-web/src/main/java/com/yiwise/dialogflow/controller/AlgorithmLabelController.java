package com.yiwise.dialogflow.controller;

import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.dialogflow.entity.vo.AlgorithmLabelUpdateVO;
import com.yiwise.dialogflow.service.AlgorithmLabelService;
import com.yiwise.dialogflow.utils.SecurityUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 算法标签
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/apiBot/v3/algorithmLabel")
public class AlgorithmLabelController {

    @Resource
    private AlgorithmLabelService algorithmLabelService;

    /**
     * 算法领域信息
     *
     * @return <行业id,<场景id,算法领域>>
     */
    @GetMapping("/domainInfo")
    public ResultObject<Map<String, Map<String, String>>> domainInfo() {
        return ResultObject.success(algorithmLabelService.domainInfo());
    }

    /**
     * 领域标签列表
     *
     * @param domain 领域
     * @return 标签列表
     */
    @GetMapping("/domainLabelList")
    public ResultObject<List<String>> domainLabelList(@RequestParam("domain") String domain) {
        return ResultObject.success(algorithmLabelService.domainLabelList(domain));
    }

    /**
     * 人工打标签
     *
     * @param request form
     */
    @PostMapping("/updateLabel")
    public ResultObject<Void> updateLabel(@RequestBody @Valid AlgorithmLabelUpdateVO request) {
        algorithmLabelService.updateLabel(request, SecurityUtils.getUserId());
        return ResultObject.success(null);
    }
}