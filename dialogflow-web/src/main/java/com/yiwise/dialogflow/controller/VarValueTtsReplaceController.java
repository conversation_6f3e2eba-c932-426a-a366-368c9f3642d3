package com.yiwise.dialogflow.controller;

import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.dialogflow.entity.po.VarValueTtsReplacePO;
import com.yiwise.dialogflow.entity.query.VarValueTtsReplaceDeleteVO;
import com.yiwise.dialogflow.entity.query.VarValueTtsReplaceExportRequest;
import com.yiwise.dialogflow.entity.query.VarValueTtsReplaceQueryVO;
import com.yiwise.dialogflow.entity.vo.IdNamePair;
import com.yiwise.dialogflow.entity.vo.VarValueTtsReplaceVO;
import com.yiwise.dialogflow.entity.vo.audio.TtsComposeResultVO;
import com.yiwise.dialogflow.service.VarValueTtsReplaceService;
import com.yiwise.dialogflow.utils.SecurityUtils;
import com.yiwise.middleware.tts.enums.TtsProviderEnum;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;

/**
 * 变量读音值替换
 */
@RestController
@RequestMapping("/apiBot/v3/ttsReplace")
public class VarValueTtsReplaceController {

    @Resource
    private VarValueTtsReplaceService varValueTtsReplaceService;


    @PostMapping("list")
    public ResultObject<PageResultObject<VarValueTtsReplaceVO>> list(@RequestBody VarValueTtsReplaceQueryVO condition) {
        return ResultObject.success(varValueTtsReplaceService.queryByCondition(condition));
    }

    @PostMapping("create")
    public ResultObject<VarValueTtsReplacePO> create(@RequestBody VarValueTtsReplaceVO varValueTtsReplaceVO) {
        return ResultObject.success(varValueTtsReplaceService.create(varValueTtsReplaceVO, SecurityUtils.getUserId()));
    }

    @PostMapping("update")
    public ResultObject<VarValueTtsReplacePO> update(@RequestBody VarValueTtsReplaceVO varValueTtsReplaceVO) {
        return ResultObject.success(varValueTtsReplaceService.update(varValueTtsReplaceVO, SecurityUtils.getUserId()));
    }

    @GetMapping("audition")
    public ResultObject<TtsComposeResultVO> audition(@RequestParam Long id,
                                                    Long tenantId) {
        return ResultObject.success(varValueTtsReplaceService.audition(tenantId, id));
    }

    @GetMapping("supports")
    public ResultObject<List<IdNamePair<String, String>>> supports() {
        return ResultObject.success(varValueTtsReplaceService.getSupports());
    }

    @PostMapping("auditionByConfig")
    public ResultObject<TtsComposeResultVO> auditionByConfig(@RequestBody VarValueTtsReplaceVO varValueTtsReplaceVO) {
        return ResultObject.success(varValueTtsReplaceService.audition(varValueTtsReplaceVO));
    }

    @PostMapping("delete")
    public ResultObject delete(@RequestBody VarValueTtsReplaceDeleteVO deleteRequest) {
        varValueTtsReplaceService.delete(deleteRequest, SecurityUtils.getUserId());
        return ResultObject.success();
    }

    @PostMapping("import")
    public ResultObject importFromExcel(@RequestParam Long tenantId,
                                       @RequestParam("file") MultipartFile file) {
        varValueTtsReplaceService.importFromExcel(tenantId, SecurityUtils.getUserId(), file);
        return ResultObject.success();
    }

    @PostMapping("export")
    public ResultObject<String> exportByIdList(@RequestBody VarValueTtsReplaceExportRequest request) {
        return ResultObject.success(varValueTtsReplaceService.export(request));
    }


}
