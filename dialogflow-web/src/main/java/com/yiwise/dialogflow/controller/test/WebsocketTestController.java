package com.yiwise.dialogflow.controller.test;

import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.base.model.annotation.auth.InnerOnly;
import com.yiwise.base.model.annotation.auth.NoLogin;
import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.base.model.websocket.BasicMsg;
import com.yiwise.base.model.websocket.UserIdPrincipal;
import com.yiwise.dialogflow.common.ApplicationConstant;
import com.yiwise.dialogflow.entity.po.botgenerate.BotGenerateRecordPO;
import com.yiwise.dialogflow.helper.AliMessageQueueHelper;
import com.yiwise.dialogflow.service.botgenerate.BotGenerateRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;


@Slf4j
@RestController
@RequestMapping("apiBot/v3/websocketTest")
public class WebsocketTestController {

    @Resource
    private BotGenerateRecordService botGenerateRecordService;

    @NoLogin
    @InnerOnly
    @GetMapping("websocket")
    public ResultObject sendMsg(Long userId, String recordId) {
        botGenerateRecordService.getById(recordId)
                .ifPresent(record -> {
                    BasicMsg<BotGenerateRecordPO> msg = new BasicMsg<>();
                    msg.setInfo(record);
                    msg.setMsg("bot生成完成");
                    log.info("推送bot生成完成消息, msg={}", JsonUtils.object2String(record));
                    AliMessageQueueHelper.sendWebSocketMessage(new UserIdPrincipal(userId, String.valueOf(userId)), ApplicationConstant.WEBSOCKET_BOT_GENERATE_SUBSCRIPT_URL, msg, "BOT_GENERATE");
                });
        return ResultObject.success();
    }
}
