package com.yiwise.dialogflow.controller.audio;

import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.dialogflow.entity.vo.IdNamePair;
import com.yiwise.dialogflow.entity.vo.audio.PublicAudioDuplicateCheckResultVO;
import com.yiwise.dialogflow.entity.vo.audio.PublicAudioVO;
import com.yiwise.dialogflow.entity.vo.audio.request.PublicAudioChangeGroupRequestVO;
import com.yiwise.dialogflow.entity.vo.audio.request.PublicAudioSearchRequestVO;
import com.yiwise.dialogflow.entity.vo.audio.request.PublicAudioSyncRequestVO;
import com.yiwise.dialogflow.entity.vo.audio.request.PublicAudioUpdateRequestVO;
import com.yiwise.dialogflow.service.PublicAudioService;
import com.yiwise.dialogflow.utils.SecurityUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * 公共音频库
 *
 * <AUTHOR>
 * @date 2022/12/15
 */
@RestController
@RequestMapping("/apiBot/v3/publicAudio")
public class PublicAudioController {

    @Resource
    private PublicAudioService publicAudioService;

    /**
     * 分页查询公共音频
     */
    @PostMapping("/list")
    public ResultObject<PageResultObject<PublicAudioVO>> list(@RequestBody PublicAudioSearchRequestVO request) {
        return ResultObject.success(publicAudioService.list(request));
    }

    /**
     * 公共音频库中的录音师列表
     */
    @GetMapping("/recordUserList")
    public ResultObject<List<IdNamePair<Long, String>>> recordUserList() {
        return ResultObject.success(publicAudioService.recordUserList());
    }

    /**
     * 来源bot列表
     */
    @GetMapping("/sourceBotList")
    public ResultObject<List<IdNamePair<Long, String>>> sourceBotList(String search, @RequestParam(defaultValue = "20") Integer pageSize) {
        return ResultObject.success(publicAudioService.sourceBotList(search, pageSize));
    }

    /**
     * 更新人列表
     */
    @GetMapping("/updateUserList")
    public ResultObject<List<IdNamePair<Long, String>>> updateUserList() {
        return ResultObject.success(publicAudioService.updateUserList());
    }

    /**
     * 删除公共音频
     */
    @PostMapping("/delete")
    public ResultObject<Void> delete(@RequestBody PublicAudioSearchRequestVO request) {
        publicAudioService.delete(request);
        return ResultObject.success(null);
    }

    /**
     * 检验选中的音频中是否存在重复
     */
    @PostMapping("/duplicateCheck")
    public ResultObject<PublicAudioDuplicateCheckResultVO> duplicateCheck(@RequestBody PublicAudioSearchRequestVO request) {
        return ResultObject.success(PublicAudioDuplicateCheckResultVO.builder().duplicateExists(publicAudioService.duplicateCheck(request)).build());
    }

    /**
     * 移动分组
     */
    @PostMapping("/changeGroup")
    public ResultObject<Void> changeGroup(@RequestBody @Valid PublicAudioChangeGroupRequestVO request) {
        publicAudioService.changeGroup(request, SecurityUtils.getUserId());
        return ResultObject.success(null);
    }

    /**
     * 更新音频
     */
    @PostMapping("/update")
    public ResultObject<Void> update(@RequestBody @Valid PublicAudioUpdateRequestVO request) {
        publicAudioService.update(request, SecurityUtils.getUserId());
        return ResultObject.success(null);
    }

    /**
     * 同步到bot
     */
    @PostMapping("/sync")
    public ResultObject<Void> sync(@RequestBody @Valid PublicAudioSyncRequestVO request) {
        request.setCurrentUserId(SecurityUtils.getUserId());
        publicAudioService.sync(request);
        return ResultObject.success(null);
    }

    /**
     * 下载音频
     */
    @GetMapping("/download")
    public void download(@RequestParam String id, HttpServletResponse response) {
        publicAudioService.download(id, response);
    }
}