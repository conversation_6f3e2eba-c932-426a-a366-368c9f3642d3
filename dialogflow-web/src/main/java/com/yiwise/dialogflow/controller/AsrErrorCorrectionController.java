package com.yiwise.dialogflow.controller;

import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.base.model.enums.SystemEnum;
import com.yiwise.dialogflow.entity.enums.asr.AsrErrorCorrectionTrainStatusEnum;
import com.yiwise.dialogflow.entity.po.BotPO;
import com.yiwise.dialogflow.entity.po.asrmodel.AsrErrorCorrectionDetailPO;
import com.yiwise.dialogflow.entity.vo.IdNamePair;
import com.yiwise.dialogflow.entity.vo.asrmodel.AsrErrorCorrectionDetailVO;
import com.yiwise.dialogflow.service.asrmodel.AsrErrorCorrectionService;
import com.yiwise.dialogflow.utils.SecurityUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2022/10/14 15:42:57
 */

@RestController
@RequestMapping("/apiBot/v3/asrErrorCorrection")
public class AsrErrorCorrectionController {

    @Resource
    private AsrErrorCorrectionService asrErrorCorrectionService;

    @GetMapping("list")
    public ResultObject<PageResultObject<AsrErrorCorrectionDetailVO>> list(@RequestParam(value = "name", required = false) String name,
                                                                        @RequestParam(value = "status", required = false) AsrErrorCorrectionTrainStatusEnum status,
                                                                        @RequestParam(value = "botInfo", required = false) String botInfo,
                                                                        @RequestParam(defaultValue = "1") Integer pageNum,
                                                                        @RequestParam(defaultValue = "20") Integer pageSize) {
        return ResultObject.success(asrErrorCorrectionService.list(name, status, botInfo, pageNum, pageSize));
    }

    @PostMapping("/save")
    public ResultObject<String> save(@RequestBody AsrErrorCorrectionDetailVO asrErrorCorrectionDetailVO) {
        asrErrorCorrectionDetailVO.setUpdateUserId(SecurityUtils.getUserId());
        asrErrorCorrectionService.save(asrErrorCorrectionDetailVO);
        return ResultObject.success("请求成功");
    }

    @DeleteMapping("/delete")
    public ResultObject<String> delete(String asrErrorCorrectionDetailId) {
        asrErrorCorrectionService.delete(asrErrorCorrectionDetailId);
        return ResultObject.success("请求成功");
    }

    @PostMapping("unbindBot")
    public ResultObject<String> unbindBot(@RequestBody AsrErrorCorrectionDetailVO asrErrorCorrectionDetailVO) {
        asrErrorCorrectionDetailVO.setUpdateUserId(SecurityUtils.getUserId());
        asrErrorCorrectionService.unbindBot(asrErrorCorrectionDetailVO);
        return ResultObject.success("请求成功");
    }

    @PostMapping("bindBot")
    public ResultObject<String> bindBot(@RequestBody AsrErrorCorrectionDetailVO asrErrorCorrectionDetailVO) {
        asrErrorCorrectionDetailVO.setUpdateUserId(SecurityUtils.getUserId());
        asrErrorCorrectionService.bindBot(asrErrorCorrectionDetailVO);
        return ResultObject.success("请求成功");
    }

    @GetMapping("getBotList")
    public ResultObject<PageResultObject<BotPO>> getBotList(@RequestParam("asrErrorCorrectionDetailId") String asrErrorCorrectionDetailId,
                                                         @RequestParam(value = "botInfo", required = false) String botInfo,
                                                         @RequestParam(defaultValue = "1") Integer pageNum,
                                                         @RequestParam(defaultValue = "20") Integer pageSize) {
        return ResultObject.success(asrErrorCorrectionService.getBotList(asrErrorCorrectionDetailId, botInfo, pageNum, pageSize));
    }

    @PostMapping("train")
    public ResultObject<String> train(@RequestParam("asrErrorCorrectionDetailId") String asrErrorCorrectionDetailId, @RequestParam("systemType") SystemEnum systemType) {
        asrErrorCorrectionService.startTrain(asrErrorCorrectionDetailId, SecurityUtils.getUserId(), systemType);
        return ResultObject.success("请求成功");
    }

    @GetMapping("getById")
    public ResultObject<AsrErrorCorrectionDetailPO> getById(@RequestParam("asrErrorCorrectionDetailId") String asrErrorCorrectionDetailId) {
        return ResultObject.success(asrErrorCorrectionService.get(asrErrorCorrectionDetailId));
    }

    @GetMapping("idNamePairList")
    public ResultObject<PageResultObject<IdNamePair<String, String>>> idNamePairList(@RequestParam(value = "name", required = false) String name,
                                                                                  @RequestParam(defaultValue = "1") Integer pageNum,
                                                                                  @RequestParam(defaultValue = "20") Integer pageSize) {
        return ResultObject.success(asrErrorCorrectionService.idNamePairList(name, pageNum, pageSize));
    }
}