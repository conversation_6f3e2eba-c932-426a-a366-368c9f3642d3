package com.yiwise.dialogflow.controller.audio;

import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.dialogflow.aop.TenantIsolation;
import com.yiwise.dialogflow.entity.po.BotAudioConfigPO;
import com.yiwise.dialogflow.entity.vo.audio.BotAudioConfigVO;
import com.yiwise.dialogflow.service.BotConfigService;
import com.yiwise.dialogflow.utils.SecurityUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;

@Validated
@RestController
@RequestMapping("apiBot/v3/audio/config")
public class AudioConfigController {
    @Resource
    private BotConfigService botConfigService;

    @GetMapping("getByBotId")
    public ResultObject<BotAudioConfigVO> getByBotId(@NotNull(message = "botId不能为空") Long botId) {
        return ResultObject.success(botConfigService.getAudioConfigVO(botId));
    }

    @PostMapping("update")
    @TenantIsolation("#config.botId")
    public ResultObject<BotAudioConfigPO> update(@RequestBody BotAudioConfigPO config) {
        return ResultObject.success(botConfigService.saveAudioConfig(config.getBotId(), config, getUserId()));
    }

    private Long getUserId() {
        return SecurityUtils.getUserId();
    }

    @PostMapping("batchUpdateRecordUserId")
    public ResultObject<Void> batchUpdateRecordUserId() {
        botConfigService.batchUpdateRecordUserId();
        return ResultObject.success(null);
    }
}
