package com.yiwise.dialogflow.controller;

import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.dialogflow.service.NlsService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/apiBot/v3/nls")
public class NlsController {

    @Resource
    private NlsService nlsService;

    /**
     * 录音文件识别极速版
     *
     * @param audioAddress 音频文件完整地址
     * @return 识别的文本
     */
    @PostMapping("/flashRecognizer")
    public ResultObject<String> flashRecognizer(@RequestParam("audioAddress") String audioAddress) {
        return ResultObject.success(nlsService.flashRecognizer(audioAddress));
    }

    /**
     * 异步录音文件识别
     *
     * @param botId  话术id
     * @param ossKey ossKey
     */
    @PostMapping("/asyncFlashRecognizer")
    public ResultObject<Void> asyncFlashRecognizer(@RequestParam("botId") Long botId, @RequestParam("ossKey") String ossKey) {
        nlsService.asyncFlashRecognizer(botId, ossKey);
        return ResultObject.success(null);
    }
}
