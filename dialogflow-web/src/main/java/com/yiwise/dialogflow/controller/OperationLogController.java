package com.yiwise.dialogflow.controller;

import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.dialogflow.entity.vo.IdNamePair;
import com.yiwise.dialogflow.entity.vo.OperationLogQueryVO;
import com.yiwise.dialogflow.entity.vo.OperationLogVO;
import com.yiwise.dialogflow.service.OperationLogService;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 操作日志
 *
 * <AUTHOR>
 * @date 2022/9/13
 */
@Validated
@RestController
@RequestMapping("/apiBot/v3/operationLog")
public class OperationLogController {

    @Resource
    private OperationLogService operationLogService;

    /**
     * 操作对象枚举列表
     *
     * @return 操作对象枚举列表
     */
    @GetMapping("/resourceTypeList")
    public ResultObject<List<IdNamePair<String, String>>> resourceTypeList() {
        return ResultObject.success(operationLogService.resourceTypeList());
    }

    /**
     * 操作日志类型枚举列表
     *
     * @return 操作日志类型枚举列表
     */
    @GetMapping("/logTypeList")
    public ResultObject<List<IdNamePair<String, String>>> logTypeList() {
        return ResultObject.success(operationLogService.logTypeList());
    }

    /**
     * 查询bot操作日志的操作人列表
     *
     * @param botId botId
     * @return 操作人列表
     */
    @GetMapping("/operatorList")
    public ResultObject<List<IdNamePair<Long, String>>> operatorList(Long botId) {
        return ResultObject.success(operationLogService.operatorList(botId));
    }

    /**
     * 查询操作日志列表
     *
     * @param queryVO 查询表单
     * @return 操作日志列表
     */
    @PostMapping("/list")
    public ResultObject<PageResultObject<OperationLogVO>> list(@RequestBody OperationLogQueryVO queryVO) {
        return ResultObject.success(operationLogService.list(queryVO));
    }

    /**
     * 操作日志导出
     *
     * @param queryVO 查询表单
     * @return Excel文件链接
     */
    @PostMapping("/export")
    public ResultObject<String> export(@RequestBody OperationLogQueryVO queryVO) {
        return ResultObject.success(operationLogService.export(queryVO));
    }

    /**
     * 查询时间范围内有操作日志的日期列表
     *
     * @param botId     botId
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 日期列表[2022-01-01,2022-01-02]
     */
    @GetMapping("/operationDateList")
    public ResultObject<List<String>> operationDateList(@RequestParam Long botId,
                                                       @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startTime,
                                                       @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endTime) {
        LocalDateTime from = LocalDateTime.of(startTime.getYear(), startTime.getMonth(), startTime.getDayOfMonth(), 0, 0, 0);
        LocalDateTime to = LocalDateTime.of(endTime.getYear(), endTime.getMonth(), endTime.getDayOfMonth(), 23, 59, 59);
        return ResultObject.success(operationLogService.operationDateList(botId, from, to));
    }
}
