package com.yiwise.dialogflow.controller.llm;

import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.dialogflow.entity.vo.llm.RagDocumentSegmentVO;
import com.yiwise.dialogflow.service.llm.RagDocumentSegmentService;
import com.yiwise.dialogflow.utils.SecurityUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 文档分片
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/apiBot/v3/ragDocumentSegment")
public class RagDocumentSegmentController {

    @Resource
    private RagDocumentSegmentService ragDocumentSegmentService;

    /**
     * 根据文档 id, 查询列表
     *
     * @param botId 话术id
     * @param docId 文档id
     * @return 文档分片列表
     */
    @GetMapping("/getByDocId")
    public ResultObject<List<RagDocumentSegmentVO>> getByDocId(@RequestParam Long botId, @RequestParam String docId) {
        return ResultObject.success(ragDocumentSegmentService.getByDocId(botId, docId));
    }

    /**
     * 修改文档分片
     *
     * @param request form
     */
    @PostMapping("/update")
    public ResultObject<Void> update(@RequestBody RagDocumentSegmentVO request) {
        ragDocumentSegmentService.update(request, SecurityUtils.getUserId());
        return ResultObject.success(null);
    }

    /**
     * 删除文档分片
     *
     * @param request form
     */
    @PostMapping("/delete")
    public ResultObject<Void> delete(@RequestBody RagDocumentSegmentVO request) {
        ragDocumentSegmentService.delete(request.getRagDocumentId(), request.getId(), SecurityUtils.getUserId());
        return ResultObject.success(null);
    }
}