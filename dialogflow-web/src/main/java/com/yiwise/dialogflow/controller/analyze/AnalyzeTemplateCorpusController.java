package com.yiwise.dialogflow.controller.analyze;

import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.dialogflow.entity.po.analyze.AnalyzeTemplateCorpusPO;
import com.yiwise.dialogflow.entity.vo.analyze.AnalyzeTemplateCorpusAddVO;
import com.yiwise.dialogflow.entity.vo.analyze.AnalyzeTemplateCorpusQueryVO;
import com.yiwise.dialogflow.entity.vo.analyze.AnalyzeTemplateCorpusUpdateVO;
import com.yiwise.dialogflow.service.analyze.AnalyzeTemplateCorpusService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 会话分析语料
 *
 * <AUTHOR>
 */
@Validated
@RestController
@RequestMapping("/apiBot/v3/analyzeTemplateCorpus")
public class AnalyzeTemplateCorpusController {

    @Resource
    private AnalyzeTemplateCorpusService analyzeTemplateCorpusService;

    /**
     * 分页查询语料
     *
     * @param request form
     * @return 语料列表
     */
    @PostMapping("/list")
    public ResultObject<PageResultObject<AnalyzeTemplateCorpusPO>> list(@RequestBody @Valid AnalyzeTemplateCorpusQueryVO request) {
        return ResultObject.success(analyzeTemplateCorpusService.list(request));
    }

    /**
     * 添加语料
     *
     * @param request form
     */
    @PostMapping("/add")
    public ResultObject<Void> add(@RequestBody @Valid AnalyzeTemplateCorpusAddVO request) {
        analyzeTemplateCorpusService.add(request);
        return ResultObject.success(null);
    }

    /**
     * 修改语料
     *
     * @param request form
     */
    @PostMapping("/update")
    public ResultObject<Void> update(@RequestBody @Valid AnalyzeTemplateCorpusUpdateVO request) {
        analyzeTemplateCorpusService.update(request);
        return ResultObject.success(null);
    }

    /**
     * 删除语料
     *
     * @param request form
     */
    @PostMapping("/delete")
    public ResultObject<Void> delete(@RequestBody @Valid AnalyzeTemplateCorpusQueryVO request) {
        analyzeTemplateCorpusService.delete(request);
        return ResultObject.success(null);
    }
}
