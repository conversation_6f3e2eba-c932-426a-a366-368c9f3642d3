package com.yiwise.dialogflow.controller;

import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.dialogflow.entity.po.SmsGenerateResultPO;
import com.yiwise.dialogflow.entity.vo.smsGenerate.*;
import com.yiwise.dialogflow.service.SmsGenerateService;
import com.yiwise.dialogflow.utils.SecurityUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 短信生成
 *
 * <AUTHOR>
 */
@Validated
@RestController
@RequestMapping("/apiBot/v3/smsGenerate")
public class SmsGenerateController {

    @Resource
    private SmsGenerateService smsGenerateService;

    /**
     * 生成短信
     *
     * @param request form
     */
    @PostMapping("/generate")
    public ResultObject<List<SmsGenerateResultPO>> generate(@RequestBody @Valid SmsGenerateCreateVO request) {
        return ResultObject.success(smsGenerateService.generate(request, SecurityUtils.getUserId()));
    }

    /**
     * 列表查询
     *
     * @param botId    话术id
     * @param pageNum  页码
     * @param pageSize 页数
     * @return 短信生成结果列表
     */
    @GetMapping("/list")
    public ResultObject<PageResultObject<SmsGenerateResultPO>> list(@NotNull(message = "botId不能为空") Long botId,
                                                                 @RequestParam(value = "pageNum", defaultValue = "1", required = false) Integer pageNum,
                                                                 @RequestParam(value = "pageSize", defaultValue = "20", required = false) Integer pageSize) {
        return ResultObject.success(smsGenerateService.list(botId, pageNum, pageSize));
    }

    /**
     * 删除短信生成结果
     *
     * @param request form
     */
    @PostMapping("/delete")
    public ResultObject<Void> delete(@RequestBody @Valid SmsGenerateDeleteVO request) {
        smsGenerateService.delete(request, SecurityUtils.getUserId());
        return ResultObject.success(null);
    }

    /**
     * 修改短信内容
     *
     * @param request form
     */
    @PostMapping("/update")
    public ResultObject<Void> update(@RequestBody @Valid SmsGenerateUpdateVO request) {
        smsGenerateService.update(request, SecurityUtils.getUserId());
        return ResultObject.success(null);
    }

    /**
     * 置顶
     *
     * @param request form
     */
    @PostMapping("/pin")
    public ResultObject<Void> pin(@RequestBody @Valid SmsGeneratePinVO request) {
        smsGenerateService.pin(request, SecurityUtils.getUserId());
        return ResultObject.success(null);
    }

    /**
     * 取消置顶
     *
     * @param request form
     */
    @PostMapping("/unPin")
    public ResultObject<Void> unPin(@RequestBody @Valid SmsGeneratePinVO request) {
        smsGenerateService.unPin(request, SecurityUtils.getUserId());
        return ResultObject.success(null);
    }
}
