package com.yiwise.dialogflow.controller.botgenerate;

import com.yiwise.base.model.annotation.auth.NoLogin;
import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.dialogflow.entity.po.BotAnswerRewriteTaskPO;
import com.yiwise.dialogflow.entity.query.AnswerQuery;
import com.yiwise.dialogflow.entity.vo.BotAnswerEditRequest;
import com.yiwise.dialogflow.entity.vo.RewriteAnswerDetailVO;
import com.yiwise.dialogflow.entity.vo.botgenerate.BotCopyAndRewriteRequestVO;
import com.yiwise.dialogflow.entity.vo.botgenerate.BotRewritePromptInfoVO;
import com.yiwise.dialogflow.entity.vo.botgenerate.BotRewriteRequestVO;
import com.yiwise.dialogflow.service.AnswerManagerService;
import com.yiwise.dialogflow.service.botgenerate.BotAnswerRewriteTaskService;
import com.yiwise.dialogflow.service.botgenerate.BotRewriteService;
import com.yiwise.dialogflow.utils.SecurityUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.*;

/**
 * bot生成
 */
@Validated
@RestController
@RequestMapping("apiBot/v3/botRewrite")
public class AnswerRewriteController {

    @Resource
    private BotRewriteService botRewriteService;

    @Resource
    private BotAnswerRewriteTaskService botAnswerRewriteTaskService;

    @Resource
    private AnswerManagerService answerManagerService;

    /**
     * bot改写
     */
    @PostMapping("rewrite")
    public ResultObject<BotAnswerRewriteTaskPO> rewrite(@RequestBody BotRewriteRequestVO request) {
        return ResultObject.success(botRewriteService.rewrite(request, SecurityUtils.getUserId()));
    }

    /**
     * 获取或生成改写信息
     * @param botId botId
     * @return 改写offer信息
     */
    @GetMapping("getOrGenOfferMap")
    public ResultObject<Map<String, List<String>>> getOrGenOfferMap(@NotNull(message = "botId不能为空") Long botId) {
        return ResultObject.success(botRewriteService.getOrGenPromptInfo(botId));
    }

    /**
     * bot生成
     */
    @PostMapping("copyAndRewrite")
    public ResultObject<List<BotAnswerRewriteTaskPO>> copyAndRewrite(@RequestBody BotCopyAndRewriteRequestVO request) {
        request.setTenantId(0L);
        return ResultObject.success(botRewriteService.copyAndRewrite(request, SecurityUtils.getUserId()));
    }

    /**
     * 上传并解析excel文件中的offer信息
     * @param file excel文件
     * @return 解析结果
     */
    @PostMapping("uploadOfferExcel")
    public ResultObject<List<BotRewritePromptInfoVO>> uploadOfferExcel(@RequestParam(value = "file", required = false) @NotNull(message = "file不能为空")  MultipartFile file) {
        return ResultObject.success(botRewriteService.uploadAndParsePromptInfo(file));
    }

    @GetMapping("getTask")
    public ResultObject<BotAnswerRewriteTaskPO> getTask(String taskId) {
        return ResultObject.success(botAnswerRewriteTaskService.getById(taskId));
    }

    /**
     * 查询最后一次成功改写/生成详情
     * @param request 请求参数
     */
    @PostMapping("getLastRewriteDetail")
    public ResultObject<PageResultObject<RewriteAnswerDetailVO>> getLastRewriteDetail(@RequestBody AnswerQuery request) {
        return ResultObject.success(botRewriteService.queryLastRewriteDetailByCondition(request));
    }

    /**
     * 编辑答案
     * @param request 请求参数
     * @return 是否成功
     */
    @PostMapping("editAnswer")
    public ResultObject<Boolean> editAnswer(@RequestBody BotAnswerEditRequest request) {
        answerManagerService.updateAnswerAndBotStatus(request.getBotId(), request.getAnswerList(), SecurityUtils.getUserId());
        return ResultObject.success(true);
    }

}
