package com.yiwise.dialogflow.controller;

import com.yiwise.base.model.annotation.auth.NoLogin;
import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.dialogflow.aop.TenantIsolation;
import com.yiwise.dialogflow.entity.dto.DialogFlowIntentRuleRearrangeDTO;
import com.yiwise.dialogflow.entity.po.intent.IntentRulePO;
import com.yiwise.dialogflow.entity.query.IntentRuleQueryVO;
import com.yiwise.dialogflow.entity.vo.IntentRuleVO;
import com.yiwise.dialogflow.entity.vo.sync.BotSyncResultVO;
import com.yiwise.dialogflow.entity.vo.sync.RuleSyncVO;
import com.yiwise.dialogflow.service.intent.IntentRuleService;
import com.yiwise.dialogflow.utils.SecurityUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> @ yiwise . com>
 * @version v0.1 2022/3/30
 * @class <code>IntentRuleController</code>
 * @see
 * @since JDK1.8
 */
@Validated
@RestController
@RequestMapping("/apiBot/v3/intentRule")
public class IntentRuleController {

    @Resource
    private IntentRuleService intentRuleService;

    @GetMapping("/findIntentRuleList")
    public ResultObject<List<IntentRuleVO>> findIntentRuleListPost(@RequestParam Long botId) {
        IntentRuleQueryVO intentRuleQueryVO = new IntentRuleQueryVO();
        intentRuleQueryVO.setBotId(botId);
        intentRuleQueryVO.setTenantId(SecurityUtils.getTenantId());
        return ResultObject.success(intentRuleService.getIntentRuleListDetail(intentRuleQueryVO));
    }

    /**
     * 意向等级规则列表(包含统计信息)
     */
    @PostMapping("/list")
    public ResultObject<List<IntentRuleVO>> findIntentRuleListPost(@RequestBody IntentRuleQueryVO condition) {
        condition.setFilterInvalidKnowledge(true);
        condition.setFilterInvalidStepNode(true);
        return ResultObject.success(intentRuleService.getIntentRuleListDetail(condition));
    }

    @GetMapping("/getIntentRule")
    public ResultObject<IntentRuleVO> getIntentRule(@RequestParam Long botId, @RequestParam String id) {
        return ResultObject.success(intentRuleService.getDialogFlowIntentRuleDetail(id), "查询成功");
    }

    @PostMapping("/saveIntentRule")
    @TenantIsolation("#intentRulePO.botId")
    public ResultObject<IntentRulePO> saveIntentRule(@RequestBody @Validated IntentRulePO intentRulePO) {
        intentRulePO.setUpdateUserId(SecurityUtils.getUserId());
        if (intentRulePO.getId() == null) {
            return ResultObject.success(intentRuleService.addIntentRule(intentRulePO, true), "保存成功");
        } else {
            return ResultObject.success(intentRuleService.updateIntentRule(intentRulePO), "保存成功");
        }
    }

    @TenantIsolation("#param.botId")
    @PostMapping("/rearrangeIntentRule")
    public ResultObject rearrangeIntentRule(@RequestBody @Validated DialogFlowIntentRuleRearrangeDTO param) {
        intentRuleService.rearrangeMatchOrder(param.getIdList(), param.getBotId(), SecurityUtils.getUserId());
        return ResultObject.success(null, "保存成功");
    }

    @PostMapping("/batchDeleteIntentRule")
    @TenantIsolation("#param.botId")
    public ResultObject batchDeleteIntentRule(@RequestBody @Validated DialogFlowIntentRuleRearrangeDTO param) {
        intentRuleService.deleteIntentRuleByIdList(param.getBotId(), param.getIdList(), SecurityUtils.getUserId());
        return ResultObject.success(null, "删除成功");
    }

    @PostMapping("/sync")
    @TenantIsolation("#syncVO.srcBotId")
    @TenantIsolation("#syncVO.targetBotIdList")
    public ResultObject<BotSyncResultVO> sync(@RequestBody RuleSyncVO syncVO) {
        syncVO.setCurrentUserId(SecurityUtils.getUserId());
        return ResultObject.success(intentRuleService.sync(syncVO));
    }

    @NoLogin
    @PostMapping("/fixData")
    public ResultObject fixData() {
        intentRuleService.fixData();
        return ResultObject.success(null, "操作成功");
    }

}
