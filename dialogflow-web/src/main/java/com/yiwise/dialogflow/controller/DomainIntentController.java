package com.yiwise.dialogflow.controller;

import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.dialogflow.aop.TenantIsolation;
import com.yiwise.dialogflow.entity.query.DomainIntentQuery;
import com.yiwise.dialogflow.entity.vo.DomainIntentVO;
import com.yiwise.dialogflow.entity.vo.SyncResultVO;
import com.yiwise.dialogflow.service.DomainIntentService;
import com.yiwise.dialogflow.utils.SecurityUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

/**
 * 公共意图库
 *
 * <AUTHOR>
 * @date 2022/2/16
 */
@RestController
@RequestMapping("/apiBot/v3/domainIntent")
public class DomainIntentController {

    @Resource
    private DomainIntentService domainIntentService;

    /**
     * 同步意图
     */
    @PostMapping(value = "/sync")
    @TenantIsolation("#intentQuery.botIdList")
    public ResultObject<SyncResultVO> sync(@RequestBody DomainIntentQuery intentQuery) {
        intentQuery.setCurrentUserId(SecurityUtils.getUserId());
        return ResultObject.success(domainIntentService.sync(intentQuery));
    }

    @PostMapping(value = "/list")
    public ResultObject<PageResultObject<DomainIntentVO>> list(@RequestBody DomainIntentQuery intentQuery) {
        return ResultObject.success(domainIntentService.list(intentQuery));
    }

    @PostMapping(value = "/importFromFile")
    public ResultObject<String> importFromFile(@RequestParam String domainName, @RequestPart MultipartFile file,
                                              @RequestParam(defaultValue = "false") Boolean isCover) {
        domainIntentService.importFromFile(domainName, file, isCover);
        return ResultObject.success("请求成功");
    }
}
