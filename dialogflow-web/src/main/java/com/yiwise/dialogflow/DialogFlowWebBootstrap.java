package com.yiwise.dialogflow;


import com.yiwise.base.common.application.StartFailedEvent;
import com.yiwise.base.common.context.AppContextUtils;
import com.yiwise.batch.thread.BatchJobScheduler;
import com.yiwise.dialogflow.config.MqConsumerConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.mongo.MongoReactiveAutoConfiguration;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Import;


@Slf4j
@EnableFeignClients(basePackages = "com.yiwise")
@EnableDiscoveryClient
@SpringBootApplication(exclude = {
        MongoReactiveAutoConfiguration.class
})
@Import(MqConsumerConfig.class)
public class DialogFlowWebBootstrap {

    public static void main(String[] args) {
        SpringApplication springApplication = new SpringApplicationBuilder(DialogFlowWebBootstrap.class)
                .build();
        springApplication.addListeners(new StartFailedEvent());
        springApplication.run(args);
        // 启动批量任务
        BatchJobScheduler batchJobScheduler = AppContextUtils.getBean(BatchJobScheduler.class);
        batchJobScheduler.startBatchJobScheduler();
        log.info("=============================spring boot start successful !=============================");
    }

}
