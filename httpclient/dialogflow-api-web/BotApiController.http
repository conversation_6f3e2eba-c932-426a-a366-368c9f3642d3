### 1. 根据话术id列表查询话术信息
POST {{HOST}}/apiBot/v3/bot/queryBotList
Accept: application/json
Content-Type: application/json
Cookie: {{COOKIE}}

{
  "botIdList": [1, 2, 3],
  "containsDeleted": false
}

### 2. 发布并审批话术
POST {{HOST}}/apiBot/v3/bot/publishAndApproval
Accept: application/json
Content-Type: application/json
Cookie: {{COOKIE}}

{
  "dialogFlowId": 123,
  "botId": 456,
  "userId": 789,
  "tenantId": 101112
}

### 3. 复制机器人话术
POST {{HOST}}/apiBot/v3/bot/copyBot
Accept: application/json
Content-Type: application/json
Cookie: {{COOKIE}}

{
  "sourceBotId": 123,
  "targetName": "复制的机器人话术",
  "tenantId": 456
}

### 4. 检查是否有话术权限
POST {{HOST}}/apiBot/v3/bot/checkHasPermissionByDialogFlowId?dialogFlowId=123&tenantId=456
Accept: application/json
Content-Type: application/json
Cookie: {{COOKIE}}

### 5. 绑定话术
POST {{HOST}}/apiBot/v3/bot/bind
Accept: application/json
Content-Type: application/json
Cookie: {{COOKIE}}

{
  "dialogFlowIds": [123, 456, 789],
  "tenantId": 101112,
  "userId": 131415
}

### 6. 解绑话术
POST {{HOST}}/apiBot/v3/bot/unbind
Accept: application/json
Content-Type: application/json
Cookie: {{COOKIE}}

{
  "dialogFlowIds": [123, 456, 789],
  "userId": 101112
}

### 7. 查询话术使用的短信模板ID集合
POST {{HOST}}/apiBot/v3/bot/queryCallOutSmsTemplateIdSetByDialogFlowIdList
Accept: application/json
Content-Type: application/json
Cookie: {{COOKIE}}

{
  "dialogFlowIdList": [123, 456, 789]
}

### 8. 检查话术和租户绑定状态
GET {{HOST}}/apiBot/v3/bot/checkBindStatusByDialogFlowId?tenantId=123&dialogFlowId=456
Accept: application/json
Cookie: {{COOKIE}}

### 9. 查询租户下所有已发布的话术列表
GET {{HOST}}/apiBot/v3/bot/queryPublishedBotListByTenantId?tenantId=123&searchWord=测试
Accept: application/json
Cookie: {{COOKIE}}

### 10. 获取指定话术信息
GET {{HOST}}/apiBot/v3/bot/getBotInfoByDialogFlowId?tenantId=123&dialogFlowId=456
Accept: application/json
Cookie: {{COOKIE}}

### 11. 导出话术信息
GET {{HOST}}/apiBot/v3/bot/exportBotInfo?tenantId=123&dialogFlowId=456
Accept: application/json
Cookie: {{COOKIE}}

### 12. 获取租户下所有话术列表
GET {{HOST}}/apiBot/v3/bot/getAllListByTenantId?tenantId=123
Accept: application/json
Cookie: {{COOKIE}}

### 13. 获取租户下所有通用话术列表
GET {{HOST}}/apiBot/v3/bot/getAllCommonBotListByTenantId?tenantId=123
Accept: application/json
Cookie: {{COOKIE}}

### 14. 获取指定话术ID列表的详细信息
POST {{HOST}}/apiBot/v3/bot/getDetailListByIdList
Accept: application/json
Content-Type: application/json
Cookie: {{COOKIE}}

{
  "botIdList": [123, 456, 789],
  "containsDeleted": false
}

### 15. 获取轻量bot模板列表
POST {{HOST}}/apiBot/v3/bot/getMagicBotTemplateByIdList
Accept: application/json
Content-Type: application/json
Cookie: {{COOKIE}}

{
  "botIdList": [123, 456, 789],
  "containsDeleted": false
}

### 16. 创建轻量版Bot
POST {{HOST}}/apiBot/v3/bot/createMagicBot
Accept: application/json
Content-Type: application/json
Cookie: {{COOKIE}}

{
  "templateBotId": 123,
  "name": "新建轻量版机器人",
  "tenantId": 456,
  "userId": 789
}

### 17. 轻量版Bot创建前检查
POST {{HOST}}/apiBot/v3/bot/preCreateMagicBot
Accept: application/json
Content-Type: application/json
Cookie: {{COOKIE}}

{
  "templateBotId": 123,
  "name": "新建轻量版机器人",
  "tenantId": 456,
  "userId": 789
}

### 18. 根据Bot ID获取对话流ID
GET {{HOST}}/apiBot/v3/bot/getDialogFlowIdByBotId?botId=123
Accept: application/json
Cookie: {{COOKIE}}

### 19. 获取话术属性
GET {{HOST}}/apiBot/v3/bot/getAttributeByDialogFlowId?dialogFlowId=123
Accept: application/json
Cookie: {{COOKIE}}

### 20. 搜索Bot列表
POST {{HOST}}/apiBot/v3/bot/searchBot
Accept: application/json
Content-Type: application/json
Cookie: {{COOKIE}}

{
  "searchWord": "测试机器人",
  "pageNo": 1,
  "pageSize": 10,
  "tenantId": 123
}

### 21. 预创建轻量版活动配置
POST {{HOST}}/apiBot/v3/bot/preCreateMagicActivityConfig
Accept: application/json
Content-Type: application/json
Cookie: {{COOKIE}}

{
  "templateId": "template123",
  "name": "活动配置名称",
  "tenantId": 456,
  "userId": 789
}

### 22. 创建轻量版活动配置
POST {{HOST}}/apiBot/v3/bot/createMagicActivityConfig
Accept: application/json
Content-Type: application/json
Cookie: {{COOKIE}}

{
  "templateId": "template123",
  "name": "活动配置名称",
  "tenantId": 456,
  "userId": 789
} 