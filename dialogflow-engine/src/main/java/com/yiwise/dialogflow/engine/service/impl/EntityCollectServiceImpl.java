package com.yiwise.dialogflow.engine.service.impl;

import com.google.common.collect.ImmutableMap;
import com.yiwise.base.common.utils.bean.MyBeanUtils;
import com.yiwise.base.common.utils.collection.MyCollectionUtils;
import com.yiwise.dialogflow.engine.service.EntityCollectService;
import com.yiwise.dialogflow.engine.service.entitycollect.*;
import com.yiwise.dialogflow.entity.bo.EntityValueBO;
import com.yiwise.dialogflow.entity.bo.entity.*;
import com.yiwise.dialogflow.engine.domain.EntityCollectContext;
import com.yiwise.dialogflow.entity.enums.EntityTypeEnum;
import com.yiwise.dialogflow.entity.enums.SourceTypeEnum;
import com.yiwise.dialogflow.entity.enums.SystemEntityCategoryEnum;
import com.yiwise.dialogflow.entity.po.BaseEntityPO;
import com.yiwise.dialogflow.entity.po.SourceRefPO;
import com.yiwise.dialogflow.pattern.PatternEnhance;
import com.yiwise.dialogflow.service.SourceRefService;
import com.yiwise.dialogflow.service.entitycollect.EntityPreprocessService;
import com.yiwise.dialogflow.service.entitycollect.EntityService;
import com.yiwise.dialogflow.engine.utils.LargeModelEntityCollectUtils;
import com.yiwise.dialogflow.utils.PinyinUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.util.*;
import java.util.regex.Matcher;
import java.util.stream.Collectors;

@Slf4j
@Service
public class EntityCollectServiceImpl implements EntityCollectService {

    @Resource
    private EntityService entityService;

    @Resource
    private SourceRefService sourceRefService;

    @Resource
    private EntityPreprocessService entityPreprocessService;

    private static final ImmutableMap<SystemEntityCategoryEnum, SystemEntityCollector> SYSTEM_ENTITY_COLLECTOR_MAP;

    static {
        Map<SystemEntityCategoryEnum, SystemEntityCollector> map = new HashMap<>();
        new AgeGradleSystemEntityCollector().register(map);
        new CitySystemEntityCollector().register(map);
        new DatetimeSystemEntityCollector().register(map);
        new IdCardSystemEntityCollector().register(map);
        new AddressSystemEntityCollector().register(map);
        new NumberEntityCollector().register(map);
        new CurrencyEntityCollector().register(map);
        new PhoneEntityCollector().register(map);
        new OriginInputEntityCollector().register(map);
        new AddressAdvancedCollector().register(map);
        SYSTEM_ENTITY_COLLECTOR_MAP = ImmutableMap.copyOf(map);
    }

    @Override
    public List<EntityValueBO> test(Long botId, String userInput) {
        List<BaseEntityPO> entityList = new ArrayList<>(entityService.getAllByBotId(botId));
        List<SourceRefPO> entitySourceRefList = sourceRefService.getListBySourceType(botId, SourceTypeEnum.ENTITY);
        Set<String> usedEntityIdSet = entitySourceRefList.stream()
                .map(SourceRefPO::getSourceId)
                .collect(Collectors.toSet());
        return collectAsync(userInput, entityPreprocessService.preprocess(entityList))
                .block().stream()
                .sorted(Comparator.comparingInt(item -> {
                    if (EntityTypeEnum.STANDARD.equals(item.getEntityType())) {
                        return 0;
                    } else if (EntityTypeEnum.REGEX.equals(item.getEntityType())) {
                        return 1;
                    } else {
                        return usedEntityIdSet.contains(item.getEntityId()) ? 2 : 3;
                    }
                }))
                .collect(Collectors.toList());
    }

    @Override
    public Mono<List<EntityValueBO>> collectAsync(String inputText, List<RuntimeEntityBO> entityList) {
        return collectAsync(inputText, entityList, null);
    }

    @Override
    public Mono<List<EntityValueBO>> collectAsync(final String inputText, List<RuntimeEntityBO> runtimeEntityList, EntityCollectContext entityCollectContext) {
        if (CollectionUtils.isEmpty(runtimeEntityList) || StringUtils.isBlank(inputText)) {
            log.info("实体列表或输入内容为空，不进行实体提取");
            return Mono.empty();
        }

        final String userInputPinyin = PinyinUtils.toPinyin(inputText.replace(" ", "_"));

        log.info("userInputPinYin={}", userInputPinyin);
        // 对实体进行预处理(如果需要的话)
        runtimeEntityList = runtimeEntityList
                .stream()
                .filter(entity -> !isMatchPreSkipRegex(inputText, entity))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(runtimeEntityList)) {
            log.info("反例过滤后实体列表为空，不进行实体提取");
            return Mono.empty();
        }

        // 将实体分为本地正则提取和远程算法提取两类
        List<RuntimeStandardEntityBO> standardEntityList = runtimeEntityList.stream()
                .filter(item -> (item instanceof RuntimeStandardEntityBO))
                .map(item -> (RuntimeStandardEntityBO) item)
                .collect(Collectors.toList());

        // 标准实体直接本地正则计算就可以了
        List<EntityValueBO> result = new ArrayList<>();
        for (RuntimeStandardEntityBO entity : standardEntityList) {
            List<EntityValueBO> tmpValueList = collectStandardEntity(inputText, userInputPinyin, entity);
            if (CollectionUtils.isNotEmpty(tmpValueList) && notMatchPostSkipPattern(inputText, entity.getPostSkipPatternList(), tmpValueList)) {
                result.addAll(tmpValueList);
            }
        }

        // 正则实体提取
        List<RuntimeRegexEntityBO> regexEntityList = runtimeEntityList.stream()
                .filter(item -> (item instanceof RuntimeRegexEntityBO))
                .map(item -> (RuntimeRegexEntityBO) item)
                .collect(Collectors.toList());
        for (RuntimeRegexEntityBO entity : regexEntityList) {
            List<EntityValueBO> tmpValueList = collectRegexEntity(inputText, userInputPinyin, entity);
            if (CollectionUtils.isNotEmpty(tmpValueList) && notMatchPostSkipPattern(inputText, entity.getPostSkipPatternList(), tmpValueList)) {
                result.addAll(tmpValueList);
            }
        }

        // 大模型实体
        List<RuntimeLargeModelEntityBO> largeModelEntityList = runtimeEntityList.stream()
                .filter(item -> (item instanceof RuntimeLargeModelEntityBO))
                .map(item -> (RuntimeLargeModelEntityBO) item)
                .collect(Collectors.toList());
        Flux<EntityValueBO> largeModelCollectValueList = LargeModelEntityCollectUtils.collect(inputText, largeModelEntityList, entityCollectContext);

        // 反例的判断, 比如: 不是${水果} , 用户输入: 不是大苹果, 假如用户配置的会提取出来[苹果][大苹果],
        // 那么对于[大苹果]这个值, 是可以命中这个反例的, 那么这个时候, 是算成功提取了苹果, 还是[水果]这个实体就提取失败了?
        // 老版的提取是整个实体就提取失败了

        // 扩展系统实体提取(仅处理扩展部分正则)
        List<RuntimeSystemEntityBO> systemEntityList = runtimeEntityList.stream()
                .filter(item -> (item instanceof RuntimeSystemEntityBO))
                .map(item -> (RuntimeSystemEntityBO) item)
                .collect(Collectors.toList());

        Set<SystemEntityCategoryEnum> systemEntityCategorySet = systemEntityList.stream()
                .map(RuntimeSystemEntityBO::getEntityCategory)
                .collect(Collectors.toSet());

        Flux<EntityValueBO> systemCollectValueList = collectSystemEntity(inputText, userInputPinyin, systemEntityCategorySet, entityCollectContext);
        // 对于扩展实体, 需要再执行一遍过滤

        return Flux.merge(
                largeModelCollectValueList,
                        systemCollectValueList.collectList().map(systemValueList -> {
                            // 这里需要把扩展系统实体提取失败的结果再处理一遍
                            return collectExtraValueUseSystemEntityValueList(inputText, userInputPinyin, systemEntityList, systemValueList);
                        }).flatMapMany(Flux::fromIterable)
                )
                .concatWith(Flux.fromIterable(result))
                .collectList()
                .map(this::filterConflict);
    }


    private List<EntityValueBO> filterConflict(List<EntityValueBO> entityValueList) {
        if (CollectionUtils.isEmpty(entityValueList) ) {
            return Collections.emptyList();
        }
        // 同一个实体的结果过滤冲突, 不同实体间不进行冲突处理
        List<EntityValueBO> result = new ArrayList<>();

        Map<String, List<EntityValueBO>> entityValueMap = MyCollectionUtils.listToMapList(entityValueList, EntityValueBO::getEntityId);
        entityValueMap.forEach((entityId, valueList) -> {
            // 对多个实体值进行下标冲突判断, 按照长度从大到小排序
            List<EntityValueBO> sortedList = valueList.stream()
                    .sorted(Comparator.comparingInt(item -> item.getStartOffset() - item.getEndOffset()))
                    .collect(Collectors.toList());
            List<EntityValueBO> tmpResultList = new ArrayList<>();

            for (EntityValueBO entityValue : sortedList) {
                if (EntityTypeEnum.LARGE_MODEL.equals(entityValue.getEntityType())) {
                    // 大模型实体提取的内容不处理冲突
                    tmpResultList.add(entityValue);
                } else if (EntityTypeEnum.SYSTEM.equals(entityValue.getEntityType())
                        && StringUtils.isBlank(entityValue.getMatchInfo())) {
                    // 系统实体算法提取的内容不处理冲突
                    tmpResultList.add(entityValue);
                } else if (!isConflict(entityValue, tmpResultList)) {
                    tmpResultList.add(entityValue);
                }
            }
            result.addAll(tmpResultList);
        });

        return result;
    }

    /**
     * value是否和list中的值冲突
     */
    private boolean isConflict(EntityValueBO value, List<EntityValueBO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return false;
        }
        return list.stream().anyMatch(item -> isConflict(value, item));
    }

    // 判断两个值是否冲突
    // 下标重叠就冲突
    private boolean isConflict(EntityValueBO one, EntityValueBO other) {
        return one.getStartOffset() <= other.getEndOffset() && one.getEndOffset() >= other.getStartOffset();
    }

    private List<EntityValueBO> collectExtraValueUseSystemEntityValueList(String inputText,
                                                                          String userInputPinyin,
                                                                          List<RuntimeSystemEntityBO> systemEntityList,
                                                                          List<EntityValueBO> systemEntityValueList) {
        if (CollectionUtils.isEmpty(systemEntityList)) {
            return Collections.emptyList();
        }
        List<EntityValueBO> finalSystemValueList = new ArrayList<>();
        Map<SystemEntityCategoryEnum, List<EntityValueBO>> valueMap =
                MyCollectionUtils.listToMapList(systemEntityValueList, EntityValueBO::getSystemEntityCategory);
        for (RuntimeSystemEntityBO systemEntity : systemEntityList) {
            List<EntityValueBO> tmpValueList = collectExtraSystemEntity(inputText, userInputPinyin, systemEntity);
            if (CollectionUtils.isNotEmpty(tmpValueList) && notMatchPostSkipPattern(inputText, systemEntity.getPostSkipPatternList(), tmpValueList)) {
                finalSystemValueList.addAll(tmpValueList);
            } else {
                List<EntityValueBO> valueList = valueMap.get(systemEntity.getEntityCategory());
                if (CollectionUtils.isNotEmpty(valueList)) {
                    List<EntityValueBO> copyValueList = valueList.stream()
                            .map(item -> {
                                EntityValueBO v = MyBeanUtils.copy(item, EntityValueBO.class);
                                v.setEntityId(systemEntity.getId());
                                v.setEntityName(systemEntity.getName());
                                v.setEntityType(systemEntity.getType());
                                return v;
                            }).collect(Collectors.toList());
                    if (notMatchPostSkipPattern(inputText, systemEntity.getPostSkipPatternList(), copyValueList)) {
                        finalSystemValueList.addAll(copyValueList);
                    }
                }
            }
        }
        return finalSystemValueList;
    }

    private Flux<EntityValueBO> collectSystemEntity(String inputText, String inputTextPinYin, Set<SystemEntityCategoryEnum> systemEntityCategorySet, EntityCollectContext entityCollectContext) {
        // 一个collector可能注册了多个类型, 这里按照collector进行分组后批量提取
        Map<SystemEntityCollector, Set<SystemEntityCategoryEnum>> collectorMap = new HashMap<>();
        for (SystemEntityCategoryEnum category : systemEntityCategorySet) {
            SystemEntityCollector collector = SYSTEM_ENTITY_COLLECTOR_MAP.get(category);
            if (Objects.isNull(collector)) {
                log.warn("system entity category {} collector not found", category);
                continue;
            }
            Set<SystemEntityCategoryEnum> categorySet = collectorMap.computeIfAbsent(collector, k -> new HashSet<>());
            categorySet.add(category);
        }
        return Flux.fromIterable(collectorMap.keySet())
                .flatMap(collector -> collector.asyncCollect(collectorMap.get(collector), inputText, inputTextPinYin, entityCollectContext));
    }

    private boolean notMatchPostSkipPattern(String inputText, List<PatternEnhance> postSkipPatternList, List<EntityValueBO> entityValueList) {
        if (CollectionUtils.isEmpty(entityValueList)) {
            return false;
        }
        if (CollectionUtils.isEmpty(postSkipPatternList)) {
            return true;
        }
        // 将每个实体结果的原始值替换为实体名称, 比如 不是苹果 -> 不是水果
        return entityValueList.stream()
                .map(item -> replaceEntityValue2EntityName(inputText, item.getStartOffset(), item.getEndOffset(), item.getEntityName()))
                .noneMatch(item -> postSkipPatternList.stream().anyMatch(pattern -> {
                    boolean find = pattern.getPattern().matcher(item).find();
                    if (find) {
                        log.debug("命中反例, 实体值:{}, 正则:{}", item, pattern.getOriginalRegex());
                    }
                    return find;
                }));
    }

    private String replaceEntityValue2EntityName(String originInput, int startOffset, int endOffset, String replaceText) {
        return String.format("%s%s%s", originInput.substring(0, startOffset), replaceText, originInput.substring(endOffset + 1));
    }

    private List<EntityValueBO> collectStandardEntity(String inputText, String userInputPinyin, RuntimeStandardEntityBO entity) {
        if (CollectionUtils.isEmpty(entity.getRuntimeMemberList())) {
            return Collections.emptyList();
        }

        return entity.getRuntimeMemberList()
                .stream()
                .map(synonym -> collectByEntitySynonym(inputText, userInputPinyin, synonym))
                .flatMap(Collection::stream)
                .peek(value -> {
                    value.setEntityId(entity.getId());
                    value.setEntityName(entity.getName());
                    value.setEntityType(entity.getType());
                })
                .collect(Collectors.toList());
    }

    private List<EntityValueBO> collectByEntitySynonym(String inputText, String userInputPinyin, EntitySynonymBO entitySynonym) {
        List<EntityValueBO> valueList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(entitySynonym.getSortedSynonymList())) {
            for (String s : entitySynonym.getSortedSynonymList()) {
                valueList.addAll(extractByIndexOf(inputText, s));
            }
        }
        if (CollectionUtils.isNotEmpty(entitySynonym.getRegexSynonymPatternList())) {
            for (PatternEnhance patternEnhance : entitySynonym.getRegexSynonymPatternList()) {
                valueList.addAll(extractByPatternEnhance(inputText, patternEnhance));
            }
        }
        if (CollectionUtils.isNotEmpty(entitySynonym.getSortedPinyinSynonymList())) {
            for (String pinyin : entitySynonym.getSortedPinyinSynonymList()) {
                valueList.addAll(extractPinyinByIndexOf(inputText, userInputPinyin, pinyin));
            }
        }
        valueList.forEach(value -> value.setValue(entitySynonym.getName()));
        return valueList;
    }

    private static List<EntityValueBO> extractByIndexOf(String inputText, String synonym) {
        List<EntityValueBO> result = new ArrayList<>();
        if (inputText.length() >= synonym.length()) {
            int indexOf = 0;
            while ((indexOf = inputText.indexOf(synonym, indexOf)) > -1) {
                EntityValueBO value = new EntityValueBO();
                value.setStartOffset(indexOf);
                value.setEndOffset(indexOf + synonym.length() - 1);
                value.setOriginValue(synonym);
                value.setInputText(inputText);
                value.setMatchInfo(synonym);
                result.add(value);
                indexOf += synonym.length();
            }
        }
        return result;
    }
    private static List<EntityValueBO> extractPinyinByIndexOf(String input, String inputPinyin, String pinyinSynonym) {
        List<EntityValueBO> result = new ArrayList<>();
        if (inputPinyin.length() >= pinyinSynonym.length()) {
            int indexOf = 0;
            while ((indexOf = inputPinyin.indexOf(pinyinSynonym, indexOf)) > -1) {
                EntityValueBO value = new EntityValueBO();
                int realStart = indexOf == 0 ? 0 : inputPinyin.substring(0, indexOf).split(" ").length;
                String realOriginValue = input.substring(realStart, realStart + pinyinSynonym.split(" ").length);
                value.setStartOffset(realStart);
                value.setEndOffset(realStart + realOriginValue.length() - 1);
                value.setOriginValue(realOriginValue);
                value.setInputText(input);
                value.setMatchInfo(pinyinSynonym);
                result.add(value);
                indexOf += pinyinSynonym.length();
            }
        }
        return result;
    }

    private List<EntityValueBO> extractByPatternEnhance(String input, PatternEnhance patternEnhance) {
        if (input.length() < patternEnhance.getRequireInputLength()) {
            return Collections.emptyList();
        }
        List<EntityValueBO> result = new ArrayList<>();
        if (patternEnhance.isCanUseContainsReplaceMatch()) {
            result.addAll(extractByIndexOf(input, patternEnhance.getOriginalRegex()));
        } else {
            Matcher matcher = patternEnhance.getPattern().matcher(input);
            while (matcher.find()) {
                EntityValueBO value = new EntityValueBO();
                value.setStartOffset(matcher.start());
                value.setEndOffset(matcher.end() - 1);
                value.setOriginValue(matcher.group());
                value.setInputText(input);
                value.setMatchInfo(patternEnhance.getOriginalRegex());
                result.add(value);
            }
        }
        return result;
    }


    private List<EntityValueBO> collectRegexEntity(String inputText, String userInputPinyin, RuntimeRegexEntityBO entity) {
        if (CollectionUtils.isEmpty(entity.getPatternList())) {
            return Collections.emptyList();
        }
        List<EntityValueBO> result = new ArrayList<>();
        for (PatternEnhance patternEnhance : entity.getPatternList()) {
            result.addAll(extractByPatternEnhance(inputText, patternEnhance));
        }
        for (EntityValueBO value : result) {
            value.setValue(value.getOriginValue());
            value.setEntityId(entity.getId());
            value.setEntityName(entity.getName());
            value.setEntityType(entity.getType());
            value.setInputText(inputText);
        }
        return result;
    }

    private List<EntityValueBO> collectExtraSystemEntity(String inputText, String userInputPinyin, RuntimeSystemEntityBO entity) {
        if (CollectionUtils.isEmpty(entity.getExtraPatternList())) {
            return Collections.emptyList();
        }
        List<EntityValueBO> result = new ArrayList<>();
        for (PatternEnhance patternEnhance : entity.getExtraPatternList()) {
            result.addAll(extractByPatternEnhance(inputText, patternEnhance));
        }
        for (EntityValueBO value : result) {
            value.setValue(value.getOriginValue());
            value.setEntityId(entity.getId());
            value.setEntityName(entity.getName());
            value.setEntityType(entity.getType());
            value.setInputText(inputText);
        }
        return result;
    }

    /**
     * 判断用户输入是否命中反例表达式
     */
    private boolean isMatchPreSkipRegex(String input, RuntimeEntityBO entity) {
        if (CollectionUtils.isEmpty(entity.getPreSkipPatternList())) {
            return false;
        }
        try {
            return entity.getPreSkipPatternList().stream().anyMatch(pattern -> {
                boolean match = pattern.getPattern().matcher(input).find();
                if (match) {
                    log.debug("命中反例, 正则:{}", pattern.getOriginalRegex());
                }
                return match;
            });
        } catch (Exception e) {
            log.warn("判断用户输入是否命中反例表达式异常, entity:{}", entity, e);
        }
        return false;
    }
}
