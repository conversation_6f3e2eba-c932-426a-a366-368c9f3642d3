package com.yiwise.dialogflow.engine.chatmanager.specialanswer;

import com.google.common.collect.ImmutableList;
import com.yiwise.base.model.enums.EnabledStatusEnum;
import com.yiwise.dialogflow.engine.AnswerPredicate;
import com.yiwise.dialogflow.engine.chatmanager.AbstractTwoPhaseChatManager;
import com.yiwise.dialogflow.engine.chatmanager.ChatManager;
import com.yiwise.dialogflow.engine.chatmanager.ChatManagerPriorityEnum;
import com.yiwise.dialogflow.engine.chatmanager.ChatManagerTriggerCondition;
import com.yiwise.dialogflow.engine.context.*;
import com.yiwise.dialogflow.engine.domain.CandidateAnswer;
import com.yiwise.dialogflow.engine.enums.SpecialChatModeEnum;
import com.yiwise.dialogflow.engine.helper.SessionContextHelper;
import com.yiwise.dialogflow.engine.resource.KnowledgeAnswerRuntime;
import com.yiwise.dialogflow.engine.resource.RobotRuntimeResource;
import com.yiwise.dialogflow.engine.resource.SpecialAnswerRuntime;
import com.yiwise.dialogflow.engine.share.enums.ActiveTypeEnum;
import com.yiwise.dialogflow.engine.share.enums.ChatEventTypeEnum;
import com.yiwise.dialogflow.engine.share.enums.RepeatAnswerPlayStrategyEnum;
import com.yiwise.dialogflow.engine.share.response.ActiveManagerInfo;
import com.yiwise.dialogflow.engine.share.response.AnswerLocateBO;
import com.yiwise.dialogflow.engine.share.response.ChatResponse;
import com.yiwise.dialogflow.engine.utils.AnswerSelectUtils;
import com.yiwise.dialogflow.engine.utils.DebugLogUtils;
import com.yiwise.dialogflow.entity.enums.IntentRefTypeEnum;
import com.yiwise.dialogflow.entity.enums.PostActionTypeEnum;
import com.yiwise.dialogflow.entity.po.SpecialAnswerConfigPO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import reactor.core.publisher.Flux;

import java.util.*;

/**
 * 听不清特殊语境
 * <AUTHOR>
 */
@Slf4j
public class InaudibleChatManager extends AbstractTwoPhaseChatManager {

    private final boolean enable;

    private final SpecialAnswerRuntime inaudibleAnswerConfig;

    private final ChatManager stepFlowDispatchManager;

    private final ChatManager knowledgeChatManager;

    private final ImmutableList<KnowledgeAnswerRuntime> answerRuntimeList;
    private final Map<String, KnowledgeAnswerRuntime> answerMap;

    private final ActiveManagerInfo activeManagerInfo;

    public InaudibleChatManager(RobotRuntimeResource resource,
                                ChatManager stepFlowDispatchManager,
                                ChatManager knowledgeChatManager) {
        super(resource);
        this.stepFlowDispatchManager = stepFlowDispatchManager;
        this.knowledgeChatManager = knowledgeChatManager;
        inaudibleAnswerConfig = resource.getSpecialAnswerNameMap().get(SpecialAnswerConfigPO.INAUDIBLE);
        enable = Objects.nonNull(inaudibleAnswerConfig)
                && EnabledStatusEnum.ENABLE.equals(inaudibleAnswerConfig.getEnabledStatus())
                && CollectionUtils.isNotEmpty(inaudibleAnswerConfig.getAnswerList());
        if (enable) {
            this.answerRuntimeList = inaudibleAnswerConfig.getAnswerRuntimeList();
            this.answerMap = inaudibleAnswerConfig.getAnswerMap();
            ActiveManagerInfo info = new ActiveManagerInfo();
            info.setChatManagerName(getName());
            info.setActiveType(ActiveTypeEnum.SPECIAL_ANSWER);
            info.setOriginId(inaudibleAnswerConfig.getId());
            info.setOriginName(inaudibleAnswerConfig.getName());
            info.setOriginLabel(inaudibleAnswerConfig.getLabel());
            info.setLLM(false);
            this.activeManagerInfo = info;
        } else {
            this.answerRuntimeList = ImmutableList.of();
            this.answerMap = Collections.emptyMap();
            this.activeManagerInfo = null;
        }
    }

    @Override
    public void initContext(SessionContext sessionContext) {
        sessionContext.setInaudibleContext(new InaudibleContext());
    }

    @Override
    public ActiveManagerInfo getChatManagerInfo(SessionContext sessionContext) {
        return sessionContext.getActiveManagerInfo();
    }

    /**
     * 这种是两个阶段的流程, 第一个阶段是播放特殊配置的话术, 第二个阶段是重复上一轮的话术
     */
    @Override
    public Flux<ChatResponse> doProcess(SessionContext sessionContext, EventContext context) {
        if (!enable) {
            return Flux.empty();
        }
        return super.doProcess(sessionContext, context);
    }

    @Override
    protected boolean isFirstPhase(SessionContext sessionContext, EventContext eventContext) {
        return triggerByIntent(sessionContext, eventContext).test(sessionContext, eventContext , resource).isMatch() ||
                (BooleanUtils.isNotTrue(sessionContext.getInaudibleContext().getRepeatedAnswer()) && !ChatEventTypeEnum.USER_SAY_FINISH.equals(eventContext.getEvent()));
    }

    @Override
    protected Flux<ChatResponse> processFirstPhase(SessionContext sessionContext, EventContext eventContext) {
        sessionContext.getInaudibleContext().setRepeatedAnswer(false);
        Flux<ChatResponse> response = super.processFirstPhase(sessionContext, eventContext);
        // todo 需要检查一下这个代码
        return response.doOnTerminate(() -> {
            getCurrentAnswer(sessionContext, eventContext).ifPresent(answer -> {
                if (PostActionTypeEnum.WAIT.equals(answer.origin.getPostAction())) {
                    log.info("答案[{}]回答后操作为等待用户, 进入听不清模式", answer.getText());
                    sessionContext.getSpecialChatModes().add(SpecialChatModeEnum.INAUDIBLE_REPEAT);
                }
            });
        }).doOnNext(r -> {
            eventContext.setActiveManagerInfo(activeManagerInfo);
            sessionContext.setActiveManagerInfo(activeManagerInfo);
        });
    }

    @Override
    protected Flux<ChatResponse> processSecondPhase(SessionContext sessionContext, EventContext eventContext) {
        sessionContext.getInaudibleContext().setRepeatedAnswer(true);
        return repeatAnswer(sessionContext, eventContext);
    }

    @Override
    protected Optional<KnowledgeAnswerRuntime> getCurrentAnswer(SessionContext sessionContext, EventContext eventContext) {
        if (!enable) {
            return Optional.empty();
        }
        InaudibleContext context = sessionContext.getInaudibleContext();
        return Optional.ofNullable(answerMap.get(context.getPreAnswerId()));
    }

    @Override
    protected Optional<CandidateAnswer<KnowledgeAnswerRuntime>> getAvailableAnswer(SessionContext sessionContext, EventContext eventContext) {
        InaudibleContext context = sessionContext.getInaudibleContext();

        // 判断是否是重复命中特殊语境
        if (isRepeatMatch(sessionContext, eventContext, inaudibleAnswerConfig.getId())) {
            log.info("重复命中当前特殊语境");
            // 判断录音是否播放完成
            if (!currentSpecialAnswerLastAnswerPlayFinish(sessionContext, inaudibleAnswerConfig)) {
                // 重新生成上一轮的答案
                Optional<KnowledgeAnswerRuntime> currentAnswer = getCurrentAnswer(sessionContext, eventContext);
                if (currentAnswer.isPresent()) {
                    return Optional.of(CandidateAnswer.of(currentAnswer.get(), RepeatAnswerPlayStrategyEnum.RESUME));
                }
            }
        }

        Optional<KnowledgeAnswerRuntime> answerOpt = AnswerSelectUtils.getNextAvailableAnswer(context.getPreAnswerId(), answerRuntimeList, new AnswerPredicate(resource, sessionContext));
        answerOpt.ifPresent(answer -> context.setPreAnswerId(answer.getUniqueId()));
        return answerOpt.map(answer -> CandidateAnswer.of(answer, RepeatAnswerPlayStrategyEnum.REPLAY));
    }

    // todo 代码重复
    protected boolean currentSpecialAnswerLastAnswerPlayFinish(SessionContext sessionContext, SpecialAnswerConfigPO specialAnswer) {
        String lastAnswerId = sessionContext.getLastAnswerId();
        if (StringUtils.isBlank(lastAnswerId)) {
            return false;
        }
        double progress = sessionContext.getAnswerProgressMap().getOrDefault(lastAnswerId, 0.0);
        log.info("当前特殊语境{}, 当前答案Id{}, 当前答案播放进度{}", specialAnswer.getName(), lastAnswerId, progress);
        return progress >= 100;
    }

    /**
     * 重复命中, 仅和上轮比较
     */
    protected boolean isRepeatMatch(SessionContext sessionContext, EventContext eventContext, String newSpecialAnswerId) {
        ActiveManagerInfo lastManager = sessionContext.getActiveManagerInfo();
        return ActiveTypeEnum.SPECIAL_ANSWER.equals(lastManager.getActiveType()) && newSpecialAnswerId.equals(lastManager.getOriginId());
    }

    private Flux<ChatResponse> repeatAnswer(SessionContext sessionContext, EventContext eventContext) {
        if (!enable) {
            return Flux.empty();
        }
        switch (eventContext.getEvent()) {
            case ENTER:
            case USER_SILENCE:
            case USER_SAY_FINISH:
            case FAST_HANGUP:
            case AI_SAY_FINISH:
                return doJumpToOriginManager(sessionContext, eventContext);
            default:
                break;
        }
        return Flux.empty();
    }

    private Flux<ChatResponse> doJumpToOriginManager(SessionContext sessionContext, EventContext eventContext) {
        InaudibleContext context = sessionContext.getInaudibleContext();
        if (StringUtils.isBlank(context.getLastRepeatableAnswerId())) {
            log.info("未记录可重复的答案来源信息");
            return Flux.empty();
        }
        AnswerLocateBO locate = resource.getAnswerId2LocateMap().get(context.getLastRepeatableAnswerId());
        if (Objects.isNull(locate)) {
            log.info("未找到可重复的答案来源信息");
            return Flux.empty();
        }
        DebugLogUtils.inaudibleRepeatAnswer(eventContext);
        switch (locate.getAnswerSource()) {
            case LLM_STEP:
            case STEP:
                eventContext.setRepeatAnswer(true);
                return stepFlowDispatchManager.process(sessionContext, eventContext);
            case KNOWLEDGE:
                eventContext.setRepeatAnswer(true);
                eventContext.setRepeatKnowledgeId(locate.getKnowledgeId());
                return knowledgeChatManager.process(sessionContext, eventContext);
            case SPECIAL_ANSWER:
            default:
                break;
        }
        return Flux.empty();
    }

    @Override
    public List<ChatManagerTriggerCondition> getTriggerConditions(SessionContext sessionContext, EventContext context) {
        if (!enable) {
            return Collections.emptyList();
        }
        List<ChatManagerTriggerCondition> result = new ArrayList<>();
        result.add(triggerByIntent(sessionContext, context));
        result.add(triggerByInaudibleMode(sessionContext, context));
        return result;
    }

    /**
     * 通过意图触发
     */
    private ChatManagerTriggerCondition triggerByIntent(SessionContext sessionContext, EventContext context) {
        ChatManagerTriggerCondition condition = new ChatManagerTriggerCondition(ChatManagerPriorityEnum.INTENT_TRIGGER_SPECIAL_ANSWER);
        condition.setChatManagerName(getName());
        condition.setChatEventSet(Collections.singleton(ChatEventTypeEnum.USER_SAY_FINISH));
        condition.setIntentIdSet(new HashSet<>(inaudibleAnswerConfig.getTriggerIntentIdList()));
        condition.setIntentRefType(IntentRefTypeEnum.SPECIAL_ANSWER);
        condition.getMustNotMatchModes().add(SpecialChatModeEnum.DELAY_HANGUP);
        if (context.getCanInterruptSpecialAnswerIdSet().contains(inaudibleAnswerConfig.getId())) {
            // 当前处于不可打断, 判断当前节点是否允许响应该问答知识
            log.info("当前处于不可打断, 但当前节点允许响应特殊语境:{}", inaudibleAnswerConfig.getName());
        } else {
            condition.getMustNotMatchModes().add(SpecialChatModeEnum.UNINTERRUPTED);
        }
        condition.getMustNotMatchModes().add(SpecialChatModeEnum.KEY_CAPTURE);
        condition.setDynamicPredicate((sc, ec)
                -> !SessionContextHelper.getCurrentStepExcludeSpecialAnswerConfigNameList(sc, resource).contains(SpecialAnswerConfigPO.INAUDIBLE));
        return condition;
    }

    /**
     * 处于听不清重复状态时, 且未命中问答知识和特殊语境时触发
     */
    private ChatManagerTriggerCondition triggerByInaudibleMode(SessionContext sessionContext, EventContext context) {
        ChatManagerTriggerCondition condition = new ChatManagerTriggerCondition(ChatManagerPriorityEnum.INAUDIBLE_REPEAT);
        condition.setChatManagerName(getName());
        condition.setChatEventSet(Collections.singleton(ChatEventTypeEnum.USER_SAY_FINISH));
        condition.setDynamicPredicate((sc, ec) -> {
            // 判断当前处于听不清状态
            return SessionContextHelper.isInaudibleRepeatMode(sessionContext);
        });
        if (context.getCanInterruptSpecialAnswerIdSet().contains(inaudibleAnswerConfig.getId())) {
            // 当前处于不可打断, 判断当前节点是否允许响应该问答知识
            log.info("当前处于不可打断, 但当前节点允许响应特殊语境:{}", inaudibleAnswerConfig.getName());
        } else {
            condition.getMustNotMatchModes().add(SpecialChatModeEnum.UNINTERRUPTED);
        }
        return condition;
    }

}
