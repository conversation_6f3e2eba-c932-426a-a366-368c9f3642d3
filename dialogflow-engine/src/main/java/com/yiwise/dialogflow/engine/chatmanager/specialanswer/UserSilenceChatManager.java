package com.yiwise.dialogflow.engine.chatmanager.specialanswer;

import com.yiwise.dialogflow.engine.chatmanager.ChatManagerPriorityEnum;
import com.yiwise.dialogflow.engine.chatmanager.ChatManagerTriggerCondition;
import com.yiwise.dialogflow.engine.context.*;
import com.yiwise.dialogflow.engine.resource.RobotRuntimeResource;
import com.yiwise.dialogflow.engine.share.enums.ChatEventTypeEnum;
import com.yiwise.dialogflow.engine.share.response.ChatResponse;
import com.yiwise.dialogflow.entity.po.SpecialAnswerConfigPO;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
public class UserSilenceChatManager extends AbstractCommonSpecialAnswerChatManager {

    public UserSilenceChatManager(RobotRuntimeResource resource) {
        super(resource, resource.getSpecialAnswerNameMap().get(SpecialAnswerConfigPO.USER_SILENCE));
    }

    @Override
    public void initContext(SessionContext sessionContext) {
        sessionContext.setUserSilenceContext(new UserSilenceContext());
    }

    @Override
    public String getName() {
        return "用户无应答处理";
    }

    @Override
    protected Flux<ChatResponse> userSilence(SessionContext sessionContext, EventContext eventContext) {
        return userSayFinish(sessionContext, eventContext);
    }

    @Override
    protected CommonSpecialAnswerContext getSpecialAnswerContext(SessionContext sessionContext) {
        UserSilenceContext userSilenceContext = sessionContext.getUserSilenceContext();
        // 判断用户是否已经说过话了, 如果说过了, 则重置 preAnswerId 为 null, 重头开始播放答案列表
        boolean isRepeat = isRepeatMatch(sessionContext);
        if (!isRepeat) {
            log.info("非重复命中用户无应答处理, 清空 preAnswerId");
            userSilenceContext.setPreAnswerId(null);
        }
        return sessionContext.getUserSilenceContext();
    }


    @Override
    public List<ChatManagerTriggerCondition> getTriggerConditions(SessionContext sessionContext, EventContext context) {
        if (!enable) {
            return Collections.emptyList();
        }
        ChatManagerTriggerCondition condition = new ChatManagerTriggerCondition(ChatManagerPriorityEnum.USER_SILENCE_TRIGGER_SPECIAL_ANSWER);
        condition.setChatEventSet(Collections.singleton(ChatEventTypeEnum.USER_SILENCE));
        return Collections.singletonList(condition);
    }
}
