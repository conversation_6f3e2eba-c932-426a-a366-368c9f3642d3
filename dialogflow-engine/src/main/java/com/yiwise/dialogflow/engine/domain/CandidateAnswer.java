package com.yiwise.dialogflow.engine.domain;

import com.yiwise.dialogflow.engine.resource.BaseAnswerRuntime;
import com.yiwise.dialogflow.engine.share.enums.RepeatAnswerPlayStrategyEnum;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class CandidateAnswer<K extends BaseAnswerRuntime<?>> {

    K answer;

    RepeatAnswerPlayStrategyEnum playStrategy;

    public CandidateAnswer(K answer, RepeatAnswerPlayStrategyEnum playStrategy) {
        this.answer = answer;
        this.playStrategy = playStrategy;
    }

    public CandidateAnswer(K answer) {
        this.answer = answer;
        this.playStrategy = RepeatAnswerPlayStrategyEnum.REPLAY;
    }

    public CandidateAnswer() {
        this.playStrategy = RepeatAnswerPlayStrategyEnum.REPLAY;
    }

    public static <K extends BaseAnswerRuntime<?>> CandidateAnswer<K> of(K answer) {
        return new CandidateAnswer<K>(answer);
    }

    public static <K extends BaseAnswerRuntime<?>> CandidateAnswer<K> of(K answer, RepeatAnswerPlayStrategyEnum playStrategy) {
        return new CandidateAnswer<K>(answer, playStrategy);
    }
}
