package com.yiwise.dialogflow.engine.resource;

import com.yiwise.base.common.context.AppContextUtils;
import com.yiwise.dialogflow.engine.share.QueryNodeApiTestReq;
import com.yiwise.dialogflow.entity.po.DialogQueryNodePO;
import com.yiwise.dialogflow.entity.po.StepPO;
import com.yiwise.dialogflow.service.SystemBuiltInApiService;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public class QueryNodeRuntime extends ChatNodeRuntime {
    private static final SystemBuiltInApiService systemBuiltInApiService = AppContextUtils.getBean(SystemBuiltInApiService.class);
    private final QueryNodeApiTestReq httpRequestInfo;

    public QueryNodeRuntime(DialogQueryNodePO node, StepPO step, RobotRuntimeResource resource) {
        super(node, step, resource);
        this.httpRequestInfo = node.toHttpReq(systemBuiltInApiService.queryAllEnabledListWithCache());
    }
}