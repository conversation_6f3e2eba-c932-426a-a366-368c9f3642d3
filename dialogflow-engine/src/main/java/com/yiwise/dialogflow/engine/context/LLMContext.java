package com.yiwise.dialogflow.engine.context;

import lombok.Data;

import java.util.List;

@Data
public class LLMContext extends CommonSpecialAnswerContext {
    private String spacialAnswerId;

    /**
     * 是否开始生成
     */
    private Boolean isBeginGenerate;

    /**
     * 是否生成完成
     */
    private Boolean isGenerateComplete;

    /**
     * 是否是重复上一句, 如果是重复上一句, 则不需要请求之前的接口, 直接按顺序返回算法生成的结果
     */
    private Boolean isRepeatMode;

    /**
     * 算法生成的答案内容
     */
    private List<String> lastGenerateAnswerContent;
}
