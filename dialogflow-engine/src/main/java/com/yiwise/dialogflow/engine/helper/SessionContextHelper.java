package com.yiwise.dialogflow.engine.helper;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableSet;
import com.yiwise.dialogflow.engine.context.EventContext;
import com.yiwise.dialogflow.engine.context.SessionContext;
import com.yiwise.dialogflow.engine.context.StepFlowContext;
import com.yiwise.dialogflow.engine.enums.SpecialChatModeEnum;
import com.yiwise.dialogflow.engine.resource.*;
import com.yiwise.dialogflow.engine.share.DialogQueryNodeHttpParamInfo;
import com.yiwise.dialogflow.engine.share.QueryNodeApiTestReq;
import com.yiwise.dialogflow.engine.share.action.HttpRequestAction;
import com.yiwise.dialogflow.engine.share.common.AnswerPlaceholderSplitter;
import com.yiwise.dialogflow.engine.share.enums.QueryNodeHttpVarTypeEnum;
import com.yiwise.dialogflow.engine.share.request.UserSayFinishEvent;
import com.yiwise.dialogflow.engine.utils.AnswerRenderUtils;
import com.yiwise.dialogflow.engine.utils.DebugLogUtils;
import com.yiwise.dialogflow.entity.bo.entity.RuntimeEntityBO;
import com.yiwise.dialogflow.entity.bo.entity.RuntimeSystemEntityBO;
import com.yiwise.dialogflow.entity.enums.SystemEntityCategoryEnum;
import com.yiwise.dialogflow.entity.enums.VariableTypeEnum;
import com.yiwise.dialogflow.entity.po.BaseAnswerContent;
import com.yiwise.dialogflow.entity.po.VariablePO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 从SessionContext中获取数据一些辅助方法
 */
@Slf4j
public class SessionContextHelper {


    /**
     * 获取当前流程节点中下一轮输入期望的意图列表
     */
    public static Set<String> currentNodeExpectIntentIdSet(SessionContext sessionContext, RobotRuntimeResource resource) {
        if (Objects.isNull(sessionContext)
                || MapUtils.isEmpty(sessionContext.getStepFlowContextMap())) {
            log.warn("对话状态异常, session为空或者stepFlowContextMap为空");
            return Collections.emptySet();
        }
        if (StringUtils.isBlank(sessionContext.getLastActiveStepId())) {
            log.warn("对话状态异常, 最后活跃的流程为空");
            return Collections.emptySet();
        }
        StepFlowContext stepFlowContext = sessionContext.getStepFlowContextMap().get(sessionContext.getLastActiveStepId());
        if (Objects.isNull(stepFlowContext)) {
            log.warn("对话状态异常, 流程:{}对应的上下文状态不存在", sessionContext.getLastActiveStepId());
            return Collections.emptySet();
        }
        NodeRuntime<?> nodeRuntime = resource.getNodeIdMap().get(stepFlowContext.getCurrentNodeId());
        if (Objects.isNull(nodeRuntime)) {
            log.warn("对话状态异常, 流程[{}]上下文中期望的意图为空", stepFlowContext.getStepName());
        }
        return nodeRuntime.getNextExpectIntentIdNameMap().keySet();
    }

    /**
     * 获取最后播放的答案的录音播放进度
     */
    public static double getLastAnswerPlayProgress(SessionContext sessionContext) {
        String lastAnswerId = sessionContext.getLastAnswerId();
        if (StringUtils.isBlank(lastAnswerId)) {
            log.warn("对话状态错误, 最后答案为空");
            return 0d;
        }
        return getAnswerLastPlayProgress(sessionContext, lastAnswerId);
    }

    /**
     * 获取指定答案最后一次播放时的录音播放进度
     */
    public static double getAnswerLastPlayProgress(SessionContext sessionContext, String answerId) {
        return sessionContext.getAnswerProgressMap().getOrDefault(answerId, 0d);
    }

    public static boolean isHangupDelayMode(SessionContext sessionContext) {
        return sessionContext.getSpecialChatModes().contains(SpecialChatModeEnum.DELAY_HANGUP);
    }

    public static boolean isInaudibleRepeatMode(SessionContext sessionContext) {
        return sessionContext.getSpecialChatModes().contains(SpecialChatModeEnum.INAUDIBLE_REPEAT);
    }

    /**
     * 获取当前节点(或大模型流程)不关联的问答知识依赖的意图 id 列表
     */
    public static Set<String> getCurrentStepExcludeKnowledgeIntentIdSet(RobotRuntimeResource resource, SessionContext sessionContext) {
        Optional<StepFlowContext> currentStepFlowOpt = getCurrentActiveStepFlowContext(sessionContext);
        if (!currentStepFlowOpt.isPresent()) {
            return Collections.emptySet();
        }
        return currentStepFlowOpt
                .map(ctx -> {
                    if (ctx.isLLMStep()) {
                        return resource.getStepIdMap().get(ctx.getStepId());
                    } else {
                        return resource.getNodeIdMap().get(ctx.getCurrentNodeId());
                    }
                })
                .map(item -> (MismatchRuntime) item)
                .map(mismatchRuntime -> {
                    if (BooleanUtils.isTrue(mismatchRuntime.isMismatchKnowledgeAndStep())) {
                        if (mismatchRuntime.isMismatchAllKnowledge()) {
                            return resource.getIntent2KnowledgeMap().keySet();
                        } else {
                            return mismatchRuntime.getExcludeKnowledgeIntentIdNameMap().keySet();
                        }
                    } else {
                        return new HashSet<String>();
                    }
                }).orElse(ImmutableSet.of());
    }

    private static Set<String> getCurrentKeyCaptureNodeExpectIntentIdSet(RobotRuntimeResource resource, SessionContext sessionContext) {
        return getCurrentActiveStepFlowContext(sessionContext)
                .map(ctx -> resource.getNodeIdMap().get(ctx.getCurrentNodeId()))
                .map(nodeRuntime -> {
                    if (nodeRuntime instanceof KeyCaptureNodeRuntime) {
                        return ((KeyCaptureNodeRuntime) nodeRuntime).getCanTriggerIntentIdSet();
                    }
                    return new HashSet<String>();
                }).orElse(ImmutableSet.of());
    }

    /**
     * 获取当前流程(节点) 不关联的流程意图 id
     */
    public static Set<String> getCurrentStepExcludeStepIntentIdSet(RobotRuntimeResource resource, SessionContext sessionContext) {
        return getCurrentActiveStepFlowContext(sessionContext)
                .map(ctx -> {
                    if (ctx.isLLMStep()) {
                        return resource.getStepIdMap().get(ctx.getStepId());
                    } else {
                        return resource.getNodeIdMap().get(ctx.getCurrentNodeId());
                    }
                })
                .map((item) -> (MismatchRuntime)item)
                .map(mismatchRuntime -> {
                    if (mismatchRuntime.isMismatchKnowledgeAndStep()) {
                        if (mismatchRuntime.isMismatchAllStep()) {
                            return resource.getIntent2StepMap().keySet();
                        } else {
                            return mismatchRuntime.getExcludeStepIntentIdNameMap().keySet();
                        }
                    } else {
                        return new HashSet<String>();
                    }
                }).orElse(ImmutableSet.of());
    }

    /**
     * 获取当前节点期望的关联问答知识的意图
     */
    public static Set<String> getCurrentNodeExpectKnowledgeIntentIdSet(RobotRuntimeResource resource,
                                                                       SessionContext sessionContext,
                                                                       EventContext eventContext) {
        Collection<String> expectIntentIdList = resource.getIntent2KnowledgeMap().keySet();
        if (sessionContext.getSpecialChatModes().contains(SpecialChatModeEnum.KEY_CAPTURE)) {
            expectIntentIdList = ListUtils.retainAll(expectIntentIdList, getCurrentKeyCaptureNodeExpectIntentIdSet(resource, sessionContext));
        }
        if (eventContext.getSpecialChatModes().contains(SpecialChatModeEnum.UNINTERRUPTED)) {
            expectIntentIdList = ListUtils.retainAll(expectIntentIdList, eventContext.getCanInterruptIntentIdSet());
        }
        return new HashSet<>(ListUtils.removeAll(expectIntentIdList, getCurrentStepExcludeKnowledgeIntentIdSet(resource, sessionContext)));
    }

    public static Set<String> getCurrentNodeExpectStepIntentIdSet(RobotRuntimeResource resource,
                                                                  SessionContext sessionContext,
                                                                  EventContext eventContext) {
        Collection<String> expectIntentIdList = resource.getIntent2StepMap().keySet();
        if (sessionContext.getSpecialChatModes().contains(SpecialChatModeEnum.KEY_CAPTURE)) {
            expectIntentIdList = ListUtils.retainAll(expectIntentIdList, getCurrentKeyCaptureNodeExpectIntentIdSet(resource, sessionContext));
        }
        if (eventContext.getSpecialChatModes().contains(SpecialChatModeEnum.UNINTERRUPTED)) {
            log.info("当前处于不可打断模式, 仅可以跳转到节点指定跳转的流程");
            expectIntentIdList = ListUtils.retainAll(expectIntentIdList, eventContext.getCanInterruptIntentIdSet());
        }
        return new HashSet<>(ListUtils.removeAll(expectIntentIdList, getCurrentStepExcludeStepIntentIdSet(resource, sessionContext)));
    }

    public static List<String> getCurrentStepExcludeSpecialAnswerConfigNameList(SessionContext sessionContext, RobotRuntimeResource resource) {
        return getCurrentActiveStepFlowContext(sessionContext)
                .map(sfc -> {
                    if (sfc.isLLMStep()) {
                        return resource.getStepIdMap().get(sfc.getStepId());
                    } else {
                        return resource.getNodeIdMap().get(sfc.getCurrentNodeId());
                    }
                })
                .map(item -> (MismatchRuntime) item)
                .filter(mismatchRuntime -> BooleanUtils.isTrue(mismatchRuntime.isMismatchKnowledgeAndStep()))
                .map(MismatchRuntime::getExcludeSpecialAnswerConfigNameList)
                .orElse(ImmutableList.of());
    }

    public static Optional<StepFlowContext> getCurrentActiveStepFlowContext(SessionContext sessionContext) {
        if (Objects.isNull(sessionContext)
                || MapUtils.isEmpty(sessionContext.getStepFlowContextMap())) {
            log.warn("对话状态异常, session为空或者stepFlowContextMap为空");
            return Optional.empty();
        }
        if (StringUtils.isBlank(sessionContext.getLastActiveStepId())) {
            log.warn("对话状态异常, 最后活跃的流程为空");
            return Optional.empty();
        }
        StepFlowContext stepFlowContext = sessionContext.getStepFlowContextMap().get(sessionContext.getLastActiveStepId());
        return Optional.ofNullable(stepFlowContext);
    }

    /**
     * 获取当前主流程的StepFlowContext(可能和当前活跃的StepFlowContext不一致)
     */
    public static Optional<StepFlowContext> getCurrentMainStepFlowContext(SessionContext sessionContext) {
        if (Objects.isNull(sessionContext)
                || MapUtils.isEmpty(sessionContext.getStepFlowContextMap())) {
            log.warn("对话状态异常, sessionContext为空或者stepFlowContextMap为空");
            return Optional.empty();
        }
        if (StringUtils.isBlank(sessionContext.getLastActiveMainStepId())) {
            log.warn("对话状态异常, 最后活跃的主流程为空");
            return Optional.empty();
        }
        String mainStepId = sessionContext.getLastActiveMainStepId();
        return Optional.ofNullable(sessionContext.getStepFlowContextMap().get(mainStepId));
    }

    public static boolean getCurrentNodeEnableIntentFirst(RobotRuntimeResource resource, SessionContext sessionContext) {
        return SessionContextHelper.getCurrentActiveStepFlowContext(sessionContext)
                .map(ctx -> resource.getNodeIdMap().get(ctx.getCurrentNodeId()))
                .map(NodeRuntime::isEnableNodeIntentFirst)
                .orElse(false);
    }

    public static void variableAssign(RobotRuntimeResource resource,
                   SessionContext sessionContext,
                   EventContext eventContext,
                   String variableId,
                   String value) {
        variableAssign(resource, sessionContext, eventContext, variableId, value, null, false);
    }

    /**
     * 大模型执行的变量赋值
     */
    public static void llmVariableAssign(RobotRuntimeResource resource,
                   SessionContext sessionContext,
                   EventContext eventContext,
                   String variableId,
                   String value) {
        variableAssign(resource, sessionContext, eventContext, variableId, value, null, false, true);
    }

    public static void variableAssign(RobotRuntimeResource resource,
                                      SessionContext sessionContext,
                                      EventContext eventContext,
                                      String variableId,
                                      String value,
                                      String entityId) {
        variableAssign(resource, sessionContext, eventContext, variableId, value, entityId, false);
    }

    public static void variableAssign(RobotRuntimeResource resource,
                                      SessionContext sessionContext,
                                      EventContext eventContext,
                                      String variableId,
                                      String value,
                                      String entityId,
                                      boolean clearVariableAssignHistory) {
        variableAssign(resource, sessionContext, eventContext,variableId, value, entityId, clearVariableAssignHistory, false);
    }
    /**
     * 变量赋值, 并将结果收集到实体结果中
     * @param resource 对话中资源
     * @param sessionContext 会话上下文
     * @param eventContext 事件上下文
     * @param variableId 变量 id
     * @param value 变量值
     * @param entityId 实体 id
     */
    private static void variableAssign(RobotRuntimeResource resource,
                                       SessionContext sessionContext,
                                       EventContext eventContext,
                                       String variableId,
                                       String value,
                                       String entityId,
                                       boolean clearVariableAssignHistory,
                                       boolean isLLMAssign) {
        VariablePO variable = resource.getVariableIdMap().get(variableId);
        if (Objects.isNull(variable)) {
            log.warn("变量[{}]不存在", variableId);
            return;
        }
        if (VariableTypeEnum.isNotDynamicVariable(variable.getType())) {
            log.warn("[LogHub_Warn]变量赋值配置错误, 变量[{}]不是动态变量, 不能赋值, botId:{}", variable.getName(), variable.getBotId());
            return;
        }
        String oldValue = sessionContext.getGlobalVariableValueMap().get(variable.getName());
        sessionContext.getGlobalVariableValueMap().put(variable.getName(), value);
        log.info("变量[{}]赋值, 原值[{}], 新值[{}]", variable.getName(), oldValue, value);

        String debugLogContent = String.format("对变量[%s]赋值[%s]", variable.getName(), value);
        DebugLogUtils.commonDebugLog(eventContext, debugLogContent);
        if (isLLMAssign) {
            eventContext.getAiRecordDebugLog().add(debugLogContent);
        }
        if (resource.getPlayableDynamicVariableNameSet().contains(variable.getName())) {
            eventContext.getPlayableVariableValueMap().put(variable.getName(), value);
        }
        // 赋值的时候, 需要针对等待用户应答 + 原话采集这种特殊结果进行处理
        // 这种情况下, 用户多次输入:
        // 1
        // 1, 2
        // 1, 2, 3
        // 最终提取到的结果(联系历史中的动态变量)为: 1; 1, 2; 1,2,3
        // 期望的结果应该是 1, 2, 3
        boolean removePreValueWithSamePrefix = false;
        RuntimeEntityBO entity = resource.getEntityIdMap().get(entityId);

        if (entity instanceof RuntimeSystemEntityBO) {
            RuntimeSystemEntityBO runtimeSystemEntity = (RuntimeSystemEntityBO) entity;
            if (SystemEntityCategoryEnum.ORIGIN_INPUT.equals(runtimeSystemEntity.getEntityCategory())) {
                if (eventContext.getOriginEventParam() instanceof UserSayFinishEvent) {
                    removePreValueWithSamePrefix = true;
                }
            }
        }

        // 记录变量赋值历史
        List<String> varAssighHistoryList = sessionContext.getVariableAssignHistoryMap()
                .computeIfAbsent(variable.getName(), k -> new ArrayList<>());
        if (removePreValueWithSamePrefix) {
            doRemovePreValueWithSamePrefix(varAssighHistoryList, value);
        }
        varAssighHistoryList.add(value);

        if (clearVariableAssignHistory) {
            log.info("清空变量【{}】赋值历史", variable.getName());
            varAssighHistoryList.clear();
        }

        if (StringUtils.isNotBlank(entityId)) {
            List<String> entityValueList = sessionContext.getEntityCollectMap().computeIfAbsent(entityId, k -> new ArrayList<>());
            if (removePreValueWithSamePrefix) {
                doRemovePreValueWithSamePrefix(entityValueList, value);
            }
            entityValueList.add(value);
        }
    }

    private static void doRemovePreValueWithSamePrefix(List<String> valueList, String value) {
        String print = String.join(";", valueList);
        log.info("doRemovePreValueWithSamePrefix, valueList:[{}], value:[{}]", print, value);
        if (CollectionUtils.isEmpty(valueList) || StringUtils.isBlank(value)) {
            return;
        }
        List<String> newValueList = new ArrayList<>();
        // 反向遍历
        for (int i = valueList.size() - 1; i >= 0; i--) {
            String preValue = valueList.get(i);
            if (!value.startsWith(preValue)) {
                newValueList.addAll(valueList.subList(0, i + 1));
                break;
            }
        }
        log.info("移除相同前缀的值, 结果:{}", newValueList);
        valueList.clear();
        valueList.addAll(newValueList);
    }

    private static Set<String> getQueryHeaderUsedVarIdSet(List<DialogQueryNodeHttpParamInfo> queryList, List<DialogQueryNodeHttpParamInfo> headerList) {
        List<DialogQueryNodeHttpParamInfo> paramInfoList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(queryList)) {
            paramInfoList.addAll(queryList);
        }
        if (CollectionUtils.isNotEmpty(headerList)) {
            paramInfoList.addAll(headerList);
        }
        return paramInfoList.stream().filter(p -> QueryNodeHttpVarTypeEnum.isVariable(p.getVariableType())).map(DialogQueryNodeHttpParamInfo::getValue)
                .filter(StringUtils::isNotBlank).collect(Collectors.toSet());
    }

    private static Set<String> getBodyUsedVarNameSet(String body) {
        if (StringUtils.isNotBlank(body)) {
            return new AnswerPlaceholderSplitter(body, false).getVariableSet();
        }
        return Collections.emptySet();
    }

    public static HttpRequestAction generateHttpRequestAction(QueryNodeApiTestReq req, SessionContext sessionContext, String httpRequestId, RobotRuntimeResource resource) {
        Map<String, String> globalVariableValueMap = sessionContext.getGlobalVariableValueMap();
        if (MapUtils.isEmpty(globalVariableValueMap)) {
            return new HttpRequestAction(httpRequestId, req, null, null);
        }
        Map<String, String> varNameIdMap = resource.getVariableIdMap().values().stream().collect(Collectors.toMap(VariablePO::getName, VariablePO::getId));

        // body中使用的变量名
        Set<String> bodyUsedVarNameSet = getBodyUsedVarNameSet(req.getBody());
        // query header中使用的变量id
        Set<String> queryHeaderUsedVarIdSet = getQueryHeaderUsedVarIdSet(req.getQueryList(), req.getHeaderList());

        Map<String, String> varIdValueMap = new HashMap<>();
        Map<String, String> varNameValueMap = new HashMap<>();

        for (Map.Entry<String, String> entry : globalVariableValueMap.entrySet()) {
            String varName = entry.getKey();
            String varValue = entry.getValue();
            if (bodyUsedVarNameSet.contains(varName)) {
                varNameValueMap.put(varName, varValue);
            }
            String varId = varNameIdMap.get(varName);
            if (StringUtils.isNotBlank(varId) && queryHeaderUsedVarIdSet.contains(varId)) {
                varIdValueMap.put(varId, varValue);
            }
        }
        return new HttpRequestAction(httpRequestId, req, varIdValueMap, varNameValueMap);
    }

    public static void enterWaitingList(SessionContext sessionContext, String nodeId) {
        sessionContext.getWaitingResNodeIdSet().add(nodeId);
    }

    public static void leaveWaitingList(SessionContext sessionContext, String nodeId) {
        sessionContext.getWaitingResNodeIdSet().remove(nodeId);
    }

    public static boolean isWaitingResponse(SessionContext sessionContext, String nodeId) {
        return sessionContext.getWaitingResNodeIdSet().contains(nodeId);
    }

    public static String getLastAiSay(SessionContext sessionContext, RobotRuntimeResource resource) {
        String lastAnswerId = sessionContext.getLastAnswerId();
        if (StringUtils.isNotBlank(lastAnswerId)) {
            BaseAnswerContent baseAnswerContent = resource.getAnswerMap().get(lastAnswerId);
            if (Objects.nonNull(baseAnswerContent)) {
                return AnswerRenderUtils.replacePlaceholder(baseAnswerContent.getText(), sessionContext.getGlobalVariableValueMap());
            }
        }
        return null;
    }
}
