package com.yiwise.dialogflow.engine.dispatcher;

import com.google.common.collect.ImmutableMap;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.common.ApplicationConstant;
import com.yiwise.dialogflow.engine.chatmanager.*;
import com.yiwise.dialogflow.engine.chatmanager.specialanswer.*;
import com.yiwise.dialogflow.engine.context.EventContext;
import com.yiwise.dialogflow.engine.context.SessionContext;
import com.yiwise.dialogflow.engine.enums.SpecialChatModeEnum;
import com.yiwise.dialogflow.engine.helper.ActionHelper;
import com.yiwise.dialogflow.engine.helper.SessionContextHelper;
import com.yiwise.dialogflow.engine.resource.RobotRuntimeResource;
import com.yiwise.dialogflow.engine.share.enums.ActiveTypeEnum;
import com.yiwise.dialogflow.engine.share.enums.AnswerSourceEnum;
import com.yiwise.dialogflow.engine.share.enums.ChatEventTypeEnum;
import com.yiwise.dialogflow.engine.share.enums.RepeatAnswerPlayStrategyEnum;
import com.yiwise.dialogflow.engine.share.request.UserSayFinishEvent;
import com.yiwise.dialogflow.engine.share.response.ActiveManagerInfo;
import com.yiwise.dialogflow.engine.share.response.AnswerAudioPlayConfig;
import com.yiwise.dialogflow.engine.share.response.AnswerLocateBO;
import com.yiwise.dialogflow.engine.share.response.ChatResponse;
import com.yiwise.dialogflow.engine.utils.AnswerRenderUtils;
import com.yiwise.dialogflow.engine.utils.DebugLogUtils;
import com.yiwise.dialogflow.entity.bo.algorithm.PredictResult;
import com.yiwise.dialogflow.entity.po.SpecialAnswerConfigPO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.jetbrains.annotations.NotNull;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
public class DynamicDispatcher extends AbstractChatDispatcher {

    private final StepFlowDispatchManager stepDispatchManager;

    private final ChatManager knowledgeChatManager;

    private final ChatManager repeatAnswerChatManager;

    private final ChatManager aiUnknownChatManager;

    private final ChatManager userSilenceChatManager;

    private final HangupDelayChatManager hangupDelayChatManager;

    private final RobotRuntimeResource resource;

    private final LLMSpecialAnswerChatManager llmSpecialAnswerChatManager;

    private final ImmutableMap<String, ChatManager> chatManagerContainer;

    public DynamicDispatcher(RobotRuntimeResource resource) {
        this.resource = resource;
        Map<String, ChatManager> container = new HashMap<>();

        llmSpecialAnswerChatManager = new LLMSpecialAnswerChatManager(resource);
        registerChatManager(container, llmSpecialAnswerChatManager);

        stepDispatchManager = new StepFlowDispatchManager(resource);
        registerChatManager(container, stepDispatchManager);

        knowledgeChatManager = new KnowledgeChatManager(resource);
        registerChatManager(container, knowledgeChatManager);

        repeatAnswerChatManager = new RepeatAnswerChatManager(resource, stepDispatchManager, knowledgeChatManager, llmSpecialAnswerChatManager);
        registerChatManager(container, repeatAnswerChatManager);

        userSilenceChatManager = new UserSilenceChatManager(resource);
        registerChatManager(container, userSilenceChatManager);

        aiUnknownChatManager = new AiUnknownChatManager(resource);
        registerChatManager(container, aiUnknownChatManager);

        hangupDelayChatManager = new HangupDelayChatManager(resource);
//        hangupDelayChatManager.init(sessionContext, resource);
        registerChatManager(container, hangupDelayChatManager);

        registerChatManager(container, new ResumePlayChatManager(resource));

        InaudibleChatManager inaudibleChatManager = new InaudibleChatManager(resource, stepDispatchManager, knowledgeChatManager);
        registerChatManager(container, inaudibleChatManager);

        AssistantChatManager assistantChatManager = new AssistantChatManager(resource);
        registerChatManager(container, assistantChatManager);

        chatManagerContainer = ImmutableMap.copyOf(container);
    }

    @Override
    public void initContext(SessionContext sessionContext) {
        chatManagerContainer.forEach((k, chatManager) -> chatManager.initContext(sessionContext));
    }

    private void registerChatManager(Map<String, ChatManager> container, ChatManager chatManager) {
        container.put(chatManager.getName(), chatManager);
    }

    @Override
    public String getName() {
        return "DynamicDispatcher";
    }

    @Override
    public Flux<ChatResponse> doDispatch(SessionContext sessionContext, EventContext eventContext) {
        switch (eventContext.getEvent()) {
            case ENTER:
                return enter(sessionContext, eventContext);
            case FAST_HANGUP:
            case USER_SAY_FINISH:
            case KEY_CAPTURE_FAILED:
            case KEY_CAPTURE_SUCCESS:
                return dispatchByCondition(sessionContext, eventContext);
            case USER_SILENCE:
                DebugLogUtils.userSilence(eventContext);
                return dispatchByCondition(sessionContext, eventContext);
            case AI_SAY_FINISH:
                return aiSayFinish(sessionContext, eventContext);
            case LLM_REQUEST:
            default:
                throw new ComException(ComErrorCode.UNKNOWN_ERROR, "未处理的新事件:" + eventContext.getEvent());
        }
    }

    @Override
    public Flux<ChatResponse> dispatchLLMEventAsync(SessionContext sessionContext, EventContext eventContext) {
        switch (eventContext.getEvent()) {
            case LLM_REQUEST:
            case ENTER:
            case FAST_HANGUP:
            case USER_SAY_FINISH:
            case KEY_CAPTURE_FAILED:
            case KEY_CAPTURE_SUCCESS:
            case USER_SILENCE:
            case AI_SAY_FINISH:
            default:
                throw new ComException(ComErrorCode.UNKNOWN_ERROR, "分发错误:" + eventContext.getEvent());
        }
    }

    /**
     * ai 音频播放完成
     */
    private Flux<ChatResponse> aiSayFinish(SessionContext sessionContext, EventContext eventContext) {
        // 判断最后回复的来源, 交由其处理
        // todo 通过条件判断调度
        Flux<ChatResponse> responseMono = getCurrentActiveManager(sessionContext).process(sessionContext, eventContext);
        return responseMono.flatMap(response -> {
            if (SessionContextHelper.isHangupDelayMode(sessionContext)
                    && isSubChatManagerFinish(response)
                    && !getCurrentActiveManager(sessionContext).equals(hangupDelayChatManager)) {
                log.info("当前处于延迟挂机模式, 在处理完其他子流程时, 响应等待用户无应答");
                // 生成
                return hangupDelayChatManager.generateWaitUserSay(sessionContext, eventContext);
            } else {
                return stepDispatchManager.execJumpToStepAction(sessionContext, eventContext, response);
            }
        });
    }

    /**
     * 子模块的对话管理执行完成
     * 一个流程执行完成, 或者问答知识/特殊语境执行一轮即完成
     */
    private boolean isSubChatManagerFinish(ChatResponse chatResponse) {
        return needJumpStep(chatResponse) || (isSingleRoundManger(chatResponse.getAnswerLocate()) && isWaitUserSay(chatResponse));
    }

    private boolean isWaitUserSay(ChatResponse chatResponse) {
        return CollectionUtils.isNotEmpty(ActionHelper.getWaitActionList(chatResponse.getActionList()));
    }

    private boolean isSingleRoundManger(AnswerLocateBO answerLocate) {
        return Objects.nonNull(answerLocate)
                && (AnswerSourceEnum.KNOWLEDGE.equals(answerLocate.getAnswerSource()) || AnswerSourceEnum.SPECIAL_ANSWER.equals(answerLocate.getAnswerSource()));
    }

    private ChatManager getCurrentActiveManager(SessionContext sessionContext) {
        ActiveManagerInfo active = sessionContext.getActiveManagerInfo();
        return chatManagerContainer.getOrDefault(active.getChatManagerName(), aiUnknownChatManager);
    }

    private boolean needJumpStep(ChatResponse chatResponse) {
        return CollectionUtils.isNotEmpty(ActionHelper.getJumpActionList(chatResponse.getActionList()));
    }

    /**
     * 异步分发
     * 主要是触发大模型的判断, 需要请求接口, 且如果提前判断的话, 响应延迟和增加不必要的请求
     */
    private Flux<ChatResponse> dispatchByCondition(SessionContext sessionContext, EventContext eventContext) {
        // 从 chat manager 中获取到所有的判断条件
        List<ChatManagerTriggerCondition> conditionList = getChatManagerTriggerConditions(sessionContext, eventContext);

        List<ConditionMatchResult> matchedList = conditionList.stream()
                .map(condition -> condition.test(sessionContext, eventContext, resource))
                .filter(ConditionMatchResult::isMatch)
                .collect(Collectors.toList());
        return dispatchByMatchResult(sessionContext, eventContext, matchedList);
    }

    @NotNull
    private List<ChatManagerTriggerCondition> getChatManagerTriggerConditions(SessionContext sessionContext, EventContext eventContext) {
        List<ChatManagerTriggerCondition> conditionList = new ArrayList<>();
        chatManagerContainer.forEach((k, chatManager) -> {
            List<ChatManagerTriggerCondition> conditions = chatManager.getTriggerConditions(sessionContext, eventContext);
            if (CollectionUtils.isNotEmpty(conditions)) {
                for (ChatManagerTriggerCondition condition : conditions) {
                    condition.setChatManagerName(k);
                }
                conditionList.addAll(conditions);
            }
        });
        return conditionList;
    }

    private Flux<ChatResponse> dispatchByMatchResult(SessionContext sessionContext,
                                                         EventContext eventContext,
                                                         List<ConditionMatchResult> matchResultList) {
        List<ConditionMatchResult> sortedMatchResultList = matchResultList.stream()
                .sorted(new ConditionPriorityComparator(eventContext, resource, sessionContext))
                .collect(Collectors.toList());
        printConditionList("sortedMatchResultList", sortedMatchResultList);
        Optional<ConditionMatchResult> finalMatchResult = sortedMatchResultList.stream().findFirst();
        // 更新意图最终匹配结果
        if (finalMatchResult.isPresent()) {
            ConditionMatchResult matchResult = finalMatchResult.get();
            PredictResult predictResult = matchResult.getPredictResult();
            if (Objects.nonNull(predictResult)) {
                eventContext.setMatchIntentId(predictResult.getIntentId());
                predictResult.setIntentRefType(matchResult.getIntentRefType());
                eventContext.setPredictResult(predictResult);
            }
        }

        Optional<ChatManagerTriggerCondition> triggerCondition = finalMatchResult.map(ConditionMatchResult::getCondition);
        printCondition("firstMatchCondition", triggerCondition.orElse(null));

        triggerCondition.ifPresent(condition -> {
            if (Objects.nonNull(condition.getOnSelected())) {
                condition.getOnSelected().accept(sessionContext, eventContext);
            }
            eventContext.setSelectedConditionType(condition.getType());
        });
        Optional<ChatManager> chatManagerOptional = triggerCondition
                .map(ChatManagerTriggerCondition::getChatManagerName)
                .map(chatManagerContainer::get);
        if (chatManagerOptional.isPresent()) {
            return chatManagerOptional.get().process(sessionContext, eventContext)
                    .switchIfEmpty(Flux.defer(() -> aiUnknownChatManager.process(sessionContext, eventContext)));
        }
        return aiUnknownChatManager.process(sessionContext, eventContext);
    }

    private void printConditionList(String logPrefix, List<ConditionMatchResult> conditionList) {
        if (CollectionUtils.isEmpty(conditionList)) {
            log.info("{}: empty", logPrefix);
        } else {
            log.info("{}: [{}]", logPrefix, conditionList.stream()
                    .map(ConditionMatchResult::getCondition)
                    .map(ChatManagerTriggerCondition::toPrintLogString)
                    .collect(Collectors.joining(", ")));
        }
    }

    private void printCondition(String logPrefix, ChatManagerTriggerCondition condition) {
        if (Objects.isNull(condition)) {
            log.info("{}: null", logPrefix);
        } else {
            log.info("{}: {}", logPrefix, condition.toPrintLogString());
        }
    }

    private ConditionMatchResult calculateCondition(SessionContext sessionContext,
                                                    EventContext eventContext,
                                                    ChatManagerTriggerCondition condition) {
        return condition.test(sessionContext, eventContext, resource);
    }

    private Optional<ChatResponse> generateResumePlayResponse(SessionContext sessionContext) {
        ChatResponse response = new ChatResponse(null);
        String lastAnswerId = sessionContext.getLastAnswerId();
        response.setAnswer(AnswerRenderUtils.render(lastAnswerId, sessionContext, resource));
        AnswerAudioPlayConfig playConfig = new AnswerAudioPlayConfig();
        playConfig.setRepeatPlayStrategy(RepeatAnswerPlayStrategyEnum.RESUME);
        response.setAnswerAudioPlayConfig(playConfig);

        // 继续播放录音要
        response.setActionList(Collections.emptyList());

        return Optional.of(response);
    }

    private boolean lastAnswerIsInterrupted(SessionContext sessionContext, EventContext eventContext) {
        // todo 大模型生成的答案获取进度有问题, 先通过客户端传过来的进度判断
        if (Objects.nonNull(sessionContext.getActiveManagerInfo())
                && ActiveTypeEnum.SPECIAL_ANSWER.equals(sessionContext.getActiveManagerInfo().getActiveType())
                && SpecialAnswerConfigPO.LLM.equals(sessionContext.getActiveManagerInfo().getOriginName())) {
            if (eventContext.getOriginEventParam() instanceof UserSayFinishEvent) {
                UserSayFinishEvent userSayFinishEvent = (UserSayFinishEvent) eventContext.getOriginEventParam();
                if (Objects.nonNull(userSayFinishEvent.getPlayProgress())) {
                    return userSayFinishEvent.getPlayProgress() < ApplicationConstant.AUDIO_PLAY_FINISH_PROGRESS;
                }
            }
        }

        return lastAnswerPlayProgress(sessionContext) < ApplicationConstant.AUDIO_PLAY_FINISH_PROGRESS;
    }

    private double lastAnswerPlayProgress(SessionContext sessionContext) {
        return SessionContextHelper.getLastAnswerPlayProgress(sessionContext);
    }

    /**
     * 开始对话, 目前开场白都是从对话流开始的, 所以直接调用到对话流的逻辑
     * @return 对话响应
     */
    private Flux<ChatResponse> enter(SessionContext sessionContext, EventContext eventContext) {
        return stepDispatchManager.process(sessionContext, eventContext);
    }

    class ResumePlayChatManager extends AbstractChatManager {

        public  ResumePlayChatManager(RobotRuntimeResource resource) {
            super(resource);
        }
        @Override
        public void initContext(SessionContext sessionContext) {
        }

        @Override
        public ActiveManagerInfo getChatManagerInfo(SessionContext sessionContext) {
            return sessionContext.getActiveManagerInfo();
        }

        @Override
        public Flux<ChatResponse> doProcess(SessionContext sessionContext, EventContext context) {
            Optional<ChatResponse> response = Optional.empty();
            if (lastAnswerIsInterrupted(sessionContext, context)) {
                // 如果是录音打断了, 且没有命中任何意图
                DebugLogUtils.interruptAndAiUnknown(context);
                response =  generateResumePlayResponse(sessionContext);
                if (BooleanUtils.isTrue(resource.getContainsIntentHitRule())) {
                    List<PredictResult> candidatePredictResultList = context.getCandidatePredictResultList();
                    if (CollectionUtils.isNotEmpty(candidatePredictResultList)) {
                        candidatePredictResultList.stream()
                                .filter(pr -> !Objects.equals(pr.getIntentId(), ApplicationConstant.COLLECT_SUCCESS_INTENT_ID))
                                .min(new PredictResultComparator())
                                .ifPresent(predictResult -> {
                                    context.setUninterruptedPredictResult(predictResult);
                                    DebugLogUtils.uninterruptedPredictDetail(context, predictResult);
                                });
                    }
                }
            } else if (nonMatchIntentInKeyCaptureMode().test(sessionContext, context, resource).isMatch()) {
                DebugLogUtils.commonDebugLog(context, "按键采集期间，未命中意图");
                response = generateResumePlayResponse(sessionContext);
            }
            response.ifPresent(res -> res.setNoNeedResetKeyCaptureConfig(true));
            return Mono.justOrEmpty(response).flux();
        }

        @Override
        public List<ChatManagerTriggerCondition> getTriggerConditions(SessionContext sessionContext, EventContext context) {
            List<ChatManagerTriggerCondition> result = new ArrayList<>();

//            result.add(noise());

            result.add(interrupt());
            result.add(nonMatchIntentInKeyCaptureMode());

            return result;
        }

        private ChatManagerTriggerCondition interrupt() {
            ChatManagerTriggerCondition condition = new ChatManagerTriggerCondition(ChatManagerPriorityEnum.AI_UNKNOWN_AND_INTERRUPT);
            condition.setChatEventSet(Collections.singleton(ChatEventTypeEnum.USER_SAY_FINISH));
            condition.setDynamicPredicate(DynamicDispatcher.this::lastAnswerIsInterrupted);
            return condition;
        }

        private ChatManagerTriggerCondition nonMatchIntentInKeyCaptureMode() {
            ChatManagerTriggerCondition condition = new ChatManagerTriggerCondition(ChatManagerPriorityEnum.KEY_CAPTURE_NO_MATCH_INTENT);
            condition.setChatEventSet(Collections.singleton(ChatEventTypeEnum.USER_SAY_FINISH));
            condition.setMustMatchModes(Collections.singleton(SpecialChatModeEnum.KEY_CAPTURE));
            return condition;
        }
    }
}
