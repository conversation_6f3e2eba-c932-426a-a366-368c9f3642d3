package com.yiwise.dialogflow.engine.utils;

import com.yiwise.dialogflow.engine.share.action.ChatAction;
import com.yiwise.dialogflow.engine.share.action.PausePlayAction;
import com.yiwise.dialogflow.engine.share.action.UninterruptedAction;
import com.yiwise.dialogflow.engine.share.response.ChatResponse;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class ChatResponseGenerateUtils {

    public static ChatResponse generateUninterruptedResponse() {
        ChatResponse chatResponse = new ChatResponse(null);
        List<ChatAction> actionList = new ArrayList<>();
        actionList.add(new UninterruptedAction());
        chatResponse.setActionList(actionList);
        return chatResponse;
    }

    public static ChatResponse generatePauseAudioResponse() {
        ChatResponse chatResponse = new ChatResponse(null);
        List<ChatAction> actionList = new ArrayList<>();
        actionList.add(new PausePlayAction());
        chatResponse.setActionList(actionList);
        return chatResponse;
    }
}
