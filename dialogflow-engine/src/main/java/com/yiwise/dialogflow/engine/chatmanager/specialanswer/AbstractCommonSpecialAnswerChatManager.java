package com.yiwise.dialogflow.engine.chatmanager.specialanswer;

import com.google.common.collect.ImmutableList;
import com.yiwise.base.model.enums.EnabledStatusEnum;
import com.yiwise.dialogflow.engine.AnswerPredicate;
import com.yiwise.dialogflow.engine.chatmanager.AbstractOneRoundChatManager;
import com.yiwise.dialogflow.engine.context.*;
import com.yiwise.dialogflow.engine.domain.CandidateAnswer;
import com.yiwise.dialogflow.engine.resource.KnowledgeAnswerRuntime;
import com.yiwise.dialogflow.engine.resource.RobotRuntimeResource;
import com.yiwise.dialogflow.engine.resource.SpecialAnswerRuntime;
import com.yiwise.dialogflow.engine.share.enums.ActiveTypeEnum;
import com.yiwise.dialogflow.engine.share.enums.RepeatAnswerPlayStrategyEnum;
import com.yiwise.dialogflow.engine.share.response.ActiveManagerInfo;
import com.yiwise.dialogflow.engine.utils.AnswerSelectUtils;
import com.yiwise.dialogflow.entity.po.SpecialAnswerConfigPO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Slf4j
public abstract class AbstractCommonSpecialAnswerChatManager extends AbstractOneRoundChatManager {

    protected final ImmutableList<KnowledgeAnswerRuntime> knowledgeAnswerRuntimeList;
    protected final SpecialAnswerRuntime specialAnswerConfig;
    protected final boolean enable;
    protected final ActiveManagerInfo activeManagerInfo;
    public AbstractCommonSpecialAnswerChatManager(RobotRuntimeResource resource,
                                                  SpecialAnswerRuntime specialAnswerConfig) {
        super(resource);
        this.specialAnswerConfig = specialAnswerConfig;
        this.enable = isEnable(specialAnswerConfig);
        if (enable) {
            ActiveManagerInfo info = new ActiveManagerInfo();
            info.setActiveType(ActiveTypeEnum.SPECIAL_ANSWER);
            info.setChatManagerName(getName());
            info.setOriginId(specialAnswerConfig.getId());
            info.setOriginName(specialAnswerConfig.getName());
            info.setOriginLabel(specialAnswerConfig.getLabel());
            info.setLLM(SpecialAnswerConfigPO.LLM.equals(specialAnswerConfig.getName()));
            this.activeManagerInfo = info;
            this.knowledgeAnswerRuntimeList = specialAnswerConfig.getAnswerRuntimeList();
        } else {
            this.knowledgeAnswerRuntimeList = ImmutableList.of();
            this.activeManagerInfo = null;
        }
    }

    protected boolean isEnable(SpecialAnswerRuntime specialAnswerConfig) {
        return Objects.nonNull(specialAnswerConfig)
                && EnabledStatusEnum.ENABLE.equals(specialAnswerConfig.getEnabledStatus())
                && CollectionUtils.isNotEmpty(specialAnswerConfig.getAnswerList());
    }

    @Override
    public ActiveManagerInfo getChatManagerInfo(SessionContext sessionContext) {
        return activeManagerInfo;
    }

    @Override
    protected Optional<KnowledgeAnswerRuntime> getCurrentAnswer(SessionContext sessionContext, EventContext eventContext) {
        return getAllAnswerList()
                .stream()
                .filter(answer -> answer.getUniqueId().equals(getSpecialAnswerContext(sessionContext).getPreAnswerId()))
                .findFirst();
    }

    @Override
    protected Optional<CandidateAnswer<KnowledgeAnswerRuntime>> getAvailableAnswer(SessionContext sessionContext,
                                                                                   EventContext eventContext) {
        if (!enable) {
            return Optional.empty();
        }

        // 判断是否是重复命中特殊语境
        if (isRepeatMatch(sessionContext, eventContext, specialAnswerConfig.getId())) {
            log.info("重复命中当前特殊语境");
            // 判断录音是否播放完成
            if (!currentSpecialAnswerLastAnswerPlayFinish(sessionContext, specialAnswerConfig)) {
                // 重新生成上一轮的答案
                Optional<KnowledgeAnswerRuntime> currentAnswer = getCurrentAnswer(sessionContext, eventContext);
                if (currentAnswer.isPresent()) {
                    return Optional.of(CandidateAnswer.of(currentAnswer.get(), RepeatAnswerPlayStrategyEnum.RESUME));
                }
            }
        }

        CommonSpecialAnswerContext context = getSpecialAnswerContext(sessionContext);
        Optional<KnowledgeAnswerRuntime> answerOpt = AnswerSelectUtils.getNextAvailableAnswer(context.getPreAnswerId(), getAllAnswerList(), new AnswerPredicate(resource, sessionContext));
        answerOpt.ifPresent(answer -> context.setPreAnswerId(answer.getUniqueId()));
        return answerOpt.map(answer -> CandidateAnswer.of(answer, RepeatAnswerPlayStrategyEnum.REPLAY));
    }

    protected boolean currentSpecialAnswerLastAnswerPlayFinish(SessionContext sessionContext, SpecialAnswerConfigPO specialAnswer) {
        String lastAnswerId = sessionContext.getLastAnswerId();
        if (StringUtils.isBlank(lastAnswerId)) {
            return false;
        }
        double progress = sessionContext.getAnswerProgressMap().getOrDefault(lastAnswerId, 0.0);
        log.info("当前特殊语境{}, 当前答案Id{}, 当前答案播放进度{}", specialAnswer.getName(), lastAnswerId, progress);
        return progress >= 100;
    }

    /**
     * 重复命中, 仅和上轮比较
     */
    protected boolean isRepeatMatch(SessionContext sessionContext, EventContext eventContext, String newSpecialAnswerId) {
        ActiveManagerInfo lastManager = sessionContext.getActiveManagerInfo();
        return ActiveTypeEnum.SPECIAL_ANSWER.equals(lastManager.getActiveType()) && newSpecialAnswerId.equals(lastManager.getOriginId());
    }

    protected boolean isRepeatMatch(SessionContext sessionContext) {
        ActiveManagerInfo lastManager = sessionContext.getActiveManagerInfo();
        return ActiveTypeEnum.SPECIAL_ANSWER.equals(lastManager.getActiveType()) && specialAnswerConfig.getId().equals(lastManager.getOriginId());
    }

    protected List<KnowledgeAnswerRuntime> getAllAnswerList() {
        return knowledgeAnswerRuntimeList;
    }

    protected abstract CommonSpecialAnswerContext getSpecialAnswerContext(SessionContext sessionContext);
}
