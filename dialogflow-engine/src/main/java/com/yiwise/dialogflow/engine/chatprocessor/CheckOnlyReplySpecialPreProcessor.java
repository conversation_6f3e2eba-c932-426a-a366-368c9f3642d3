package com.yiwise.dialogflow.engine.chatprocessor;

import com.yiwise.dialogflow.common.ApplicationConstant;
import com.yiwise.dialogflow.engine.context.*;
import com.yiwise.dialogflow.engine.enums.SpecialChatModeEnum;
import com.yiwise.dialogflow.engine.helper.SessionContextHelper;
import com.yiwise.dialogflow.engine.resource.*;
import com.yiwise.dialogflow.engine.share.enums.ActiveTypeEnum;
import com.yiwise.dialogflow.engine.share.request.UserSayFinishEvent;
import com.yiwise.dialogflow.engine.share.response.ActiveManagerInfo;
import com.yiwise.dialogflow.entity.po.SpecialAnswerConfigPO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

/**
 * 判断是否处于不可打断, 但是可以响应部分指定的问答知识/流程的状态
 */
@Slf4j
public class CheckOnlyReplySpecialPreProcessor extends AbstractChatPreProcessor {

    private final RobotRuntimeResource resource;

    public CheckOnlyReplySpecialPreProcessor(RobotRuntimeResource resource) {
        this.resource = resource;
    }

    @Override
    public void initContext(SessionContext sessionContext) {

    }

    @Override
    public String getName() {
        return "CheckOnlyReplySpecialPreProcessor";
    }

    @Override
    public void doProcess(SessionContext sessionContext, EventContext eventContext) {
        eventContext.setCanInterruptIntentIdSet(Collections.emptySet());
        eventContext.setCanInterruptStepIdSet(Collections.emptySet());
        eventContext.setCanInterruptKnowledgeIdSet(Collections.emptySet());
        eventContext.setCanInterruptSpecialAnswerIdSet(Collections.emptySet());
        eventContext.setCanInterruptBranchIntentIdSet(Collections.emptySet());

        ActiveManagerInfo activeManagerInfo = sessionContext.getActiveManagerInfo();

        if (activeManagerInfo != null
                && ActiveTypeEnum.KNOWLEDGE.equals(activeManagerInfo.getActiveType())) {
            processKnowledge(sessionContext, eventContext);
        } if (activeManagerInfo != null
                && ActiveTypeEnum.SPECIAL_ANSWER.equals(activeManagerInfo.getActiveType())
                && SpecialAnswerConfigPO.LLM.equals(activeManagerInfo.getOriginName())) {
          // 大模型对话特殊语境
            LLMContext llmContext = sessionContext.getLlmContext();
            processLLMSpecialAnswer(sessionContext, eventContext, llmContext);
        } else {
            SessionContextHelper.getCurrentActiveStepFlowContext(sessionContext)
                    .ifPresent(stepContext -> {
                        if (stepContext.isLLMStep()) {
                            processLLMStep(sessionContext, eventContext, stepContext);
                        } else {
                            processNormalStep(sessionContext, eventContext, stepContext);
                        }
                    });
        }
    }

    private void processLLMSpecialAnswer(SessionContext sessionContext, EventContext eventContext, LLMContext llmContext) {
        double stepLastAnswerProgress = sessionContext.getAnswerProgressMap().getOrDefault(llmContext.getPreAnswerId(), 0.0);
        boolean uninterrupted = false;
        SpecialAnswerRuntime specialAnswerRuntime = resource.getSpecialAnswerIdMap().get(llmContext.getSpacialAnswerId());
        if (BooleanUtils.isTrue(specialAnswerRuntime.getEnableUninterrupted())) {
            double canInterruptProgress = 100;
            uninterrupted = stepLastAnswerProgress < canInterruptProgress;
        }
        resetCanInterrupted(eventContext, stepLastAnswerProgress, uninterrupted, specialAnswerRuntime);
    }

    private void processKnowledge(SessionContext sessionContext, EventContext eventContext) {
        KnowledgeContext knowledgeFlowContext = sessionContext.getKnowledgeContext();
        String knowledgeId = knowledgeFlowContext.getCurrentKnowledgeId();
        KnowledgeRuntime knowledgeRuntime = resource.getKnowledgeIdMap().get(knowledgeId);
        String preAnswerId = knowledgeFlowContext.getKnowledgeAnswerIdMap().get(knowledgeId);
        if (knowledgeRuntime != null && StringUtils.isNotBlank(preAnswerId)) {
            knowledgeRuntime.getAnswerRuntimeList().stream().filter(answer -> preAnswerId.equals(answer.getUniqueId()))
                    .findFirst().ifPresent(answer -> {
                String robotLastAnswerId = sessionContext.getLastAnswerId();
                if (answer.getUniqueId().equals(robotLastAnswerId)) {
                    // 获取答案最后播放进度
                    double stepLastAnswerProgress = sessionContext.getAnswerProgressMap().getOrDefault(robotLastAnswerId, 0.0);

                    boolean uninterrupted = false;
                    if (BooleanUtils.isTrue(answer.origin.getEnableUninterrupted())) {
                        double canInterruptProgress = 100;
                        if (Objects.nonNull(answer.origin.getCustomInterruptThreshold())) {
                            canInterruptProgress = answer.origin.getCustomInterruptThreshold();
                        }
                        uninterrupted = stepLastAnswerProgress < canInterruptProgress;
                    }
                    resetCanInterrupted(eventContext, stepLastAnswerProgress, uninterrupted, answer);
                }
            });
        }
    }

    private void processLLMStep(SessionContext sessionContext, EventContext eventContext, StepFlowContext stepContext) {
        double stepLastAnswerProgress = sessionContext.getAnswerProgressMap().getOrDefault(stepContext.getPreLLMGuideAnswerId(), 0.0);

        boolean uninterrupted = false;
        LLMStepRuntime stepRuntime = (LLMStepRuntime) resource.getStepIdMap().get(stepContext.getStepId());
        if (BooleanUtils.isTrue(stepRuntime.getOriginStepConfig().getEnableUninterrupted())) {
            double canInterruptProgress = 100;
            uninterrupted = stepLastAnswerProgress < canInterruptProgress;
        }
        resetCanInterrupted(eventContext, stepLastAnswerProgress, uninterrupted, stepRuntime);
    }

    private void processNormalStep(SessionContext sessionContext,
                                   EventContext eventContext,
                                   StepFlowContext stepContext) {
        // 如果已经打断跳转到问答知识xxx了, 还要判断吗?
        // 最后播放的答案
        NodeRuntime<?> node = resource.getNodeIdMap().get(stepContext.getCurrentNodeId());
        if (Objects.nonNull(node)) {
            String stepLastAnswerId = sessionContext.getNodeLastReplyAnswerIdMap().getOrDefault(node.origin.getId(), "-");
            String robotLastAnswerId = sessionContext.getLastAnswerId();
            if (!StringUtils.equals(stepLastAnswerId, robotLastAnswerId)) {
                log.info("当前流程最后播放的答案id: {}, 机器人最后播放的答案id: {}", stepLastAnswerId, robotLastAnswerId);
                return;
            }
            // 获取答案最后播放进度
            double stepLastAnswerProgress = sessionContext.getAnswerProgressMap().getOrDefault(stepLastAnswerId, 0.0);

            boolean uninterrupted = false;
            if (BooleanUtils.isTrue(node.origin.getEnableUninterrupted())) {
                double canInterruptProgress = 100;
                if (Objects.nonNull(node.origin.getCustomInterruptThreshold())) {
                    canInterruptProgress = node.origin.getCustomInterruptThreshold();
                }
                uninterrupted = stepLastAnswerProgress < canInterruptProgress;
            }

            resetCanInterrupted(eventContext, stepLastAnswerProgress, uninterrupted, node);
        } else {
            log.warn("对话数据错误, 当前节点不存在, nodeId: {}", stepContext.getCurrentNodeId());
        }
    }

    private void resetCanInterrupted(EventContext eventContext,
                                     double stepLastAnswerProgress,
                                     boolean uninterrupted,
                                     UninterruptedRuntime uninterruptedConf) {

        // 是否处于不可打断进度?
        boolean audioPlayFinish = stepLastAnswerProgress >= 100;
        uninterrupted = !audioPlayFinish && uninterrupted;
        if (ApplicationConstant.enableDebug) {
            log.debug("当前答案是否处于不可打断进度: {}, 当前进度: {}", uninterrupted, stepLastAnswerProgress);
        }
        boolean enableInterrupted = false;
        if (eventContext.getOriginEventParam() instanceof UserSayFinishEvent) {
            UserSayFinishEvent userSayFinishEvent = (UserSayFinishEvent) eventContext.getOriginEventParam();
            enableInterrupted = BooleanUtils.isTrue(userSayFinishEvent.getEnableInterrupt());
            if (BooleanUtils.isTrue(enableInterrupted)) {
                log.info("交互层通知当前允许打断");
            }
        }
        if (uninterrupted && BooleanUtils.isNotTrue(enableInterrupted)) {
            Set<String> replySpecialIntentIds = new HashSet<>();
            // 当前处于尽可响应指定的流程/问答知识状态, 需要设置仅可运行的意图id
            if (CollectionUtils.isNotEmpty(uninterruptedConf.getUninterruptedOnlyReplyKnowledgeIdSet())) {
                uninterruptedConf.getUninterruptedOnlyReplyKnowledgeIdSet().stream()
                        .map(resource.getKnowledgeTriggerIntentIdSetMap()::get)
                        .filter(Objects::nonNull)
                        .flatMap(Set::stream)
                        .forEach(replySpecialIntentIds::add);
                eventContext.setCanInterruptKnowledgeIdSet(uninterruptedConf.getUninterruptedOnlyReplyKnowledgeIdSet());
            }
            if (CollectionUtils.isNotEmpty(uninterruptedConf.getUninterruptedOnlyReplyStepIdSet())) {
                uninterruptedConf.getUninterruptedOnlyReplyStepIdSet().stream()
                        .map(resource.getStepTriggerIntentIdSetMap()::get)
                        .filter(Objects::nonNull)
                        .flatMap(Set::stream)
                        .forEach(replySpecialIntentIds::add);
                eventContext.setCanInterruptStepIdSet(uninterruptedConf.getUninterruptedOnlyReplyStepIdSet());
            }
            if (CollectionUtils.isNotEmpty(uninterruptedConf.getUninterruptedOnlyReplySpecialAnswerIdSet())) {
                uninterruptedConf.getUninterruptedOnlyReplySpecialAnswerIdSet().stream()
                        .map(resource.getSpecialAnswerTriggerIntentIdSetMap()::get)
                        .filter(Objects::nonNull)
                        .flatMap(Set::stream)
                        .forEach(replySpecialIntentIds::add);
                eventContext.setCanInterruptSpecialAnswerIdSet(uninterruptedConf.getUninterruptedOnlyReplySpecialAnswerIdSet());
            }
            if (CollectionUtils.isNotEmpty(uninterruptedConf.getUninterruptedOnlyReplyBranchIntentIdSet())) {
                replySpecialIntentIds.addAll(uninterruptedConf.getUninterruptedOnlyReplyBranchIntentIdSet());
                eventContext.setCanInterruptBranchIntentIdSet(uninterruptedConf.getUninterruptedOnlyReplyBranchIntentIdSet());
            }
            eventContext.setCanInterruptIntentIdSet(replySpecialIntentIds);
            eventContext.getSpecialChatModes().add(SpecialChatModeEnum.UNINTERRUPTED);
            log.info("更新不可打断下, 可以打断的一些特殊配置, canInterruptSpecialIntentIds: {}, canInterruptStepIdSet: {}, canInterruptKnowledgeIdSet: {}, specialAnswerId:{}, canInterruptBranchIntentIdSet:{}",
                    eventContext.getCanInterruptIntentIdSet(),
                    eventContext.getCanInterruptStepIdSet(),
                    eventContext.getCanInterruptKnowledgeIdSet(),
                    eventContext.getCanInterruptSpecialAnswerIdSet(),
                    eventContext.getCanInterruptBranchIntentIdSet());
        }
    }

}
