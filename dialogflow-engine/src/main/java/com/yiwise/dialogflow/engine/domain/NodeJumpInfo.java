package com.yiwise.dialogflow.engine.domain;

import com.yiwise.dialogflow.entity.po.DialogBaseNodePO;
import lombok.Data;

@Data
public class NodeJumpInfo {
    String stepId;
    String fromNodeId;

    String fromNodeLabel;
    String byIntentId;
    String toNodeId;
    String toNodeLabel;

    public static NodeJumpInfo of(String stepId, DialogBaseNodePO fromNode, String byIntentId, DialogBaseNodePO toNode) {
        NodeJumpInfo info = new NodeJumpInfo();
        info.stepId = stepId;
        info.fromNodeId = fromNode.getId();
        info.fromNodeLabel = fromNode.getLabel();

        info.byIntentId = byIntentId;
        info.toNodeId = toNode.getId();
        info.toNodeLabel = toNode.getLabel();
        return info;
    }
}
