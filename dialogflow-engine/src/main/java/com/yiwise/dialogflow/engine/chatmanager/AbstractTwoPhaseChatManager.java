package com.yiwise.dialogflow.engine.chatmanager;

import com.yiwise.dialogflow.engine.context.EventContext;
import com.yiwise.dialogflow.engine.context.SessionContext;
import com.yiwise.dialogflow.engine.resource.RobotRuntimeResource;
import com.yiwise.dialogflow.engine.share.response.ChatResponse;
import reactor.core.publisher.Flux;

import java.util.Optional;

/**
 * 两阶段对话管理器, 所谓两阶段是像听不清重复上一句这样的第一个阶段播放自己配置的答案, 第二个阶段播放重复上一句的答案
 * 就是简单的流程信息
 * <AUTHOR>
 */
public abstract class AbstractTwoPhaseChatManager extends AbstractOneRoundChatManager {

    public AbstractTwoPhaseChatManager(RobotRuntimeResource resource) {
        super(resource);
    }

    @Override
    public Flux<ChatResponse> doProcess(SessionContext sessionContext, EventContext eventContext) {
        boolean firstPhase = isFirstPhase(sessionContext, eventContext);
        if (firstPhase) {
            return processFirstPhase(sessionContext, eventContext);
        } else {
            return processSecondPhase(sessionContext, eventContext);
        }
    }

    protected abstract boolean isFirstPhase(SessionContext sessionContext, EventContext eventContext);

    /**
     * 阶段1的处理, 目前就暂时和一轮对话一致
     */
    protected Flux<ChatResponse> processFirstPhase(SessionContext sessionContext, EventContext eventContext) {
        return super.doProcess(sessionContext, eventContext);
    }

    protected abstract Flux<ChatResponse> processSecondPhase(SessionContext sessionContext, EventContext eventContext);

}
