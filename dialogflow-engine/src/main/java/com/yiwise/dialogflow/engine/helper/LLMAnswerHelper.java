package com.yiwise.dialogflow.engine.helper;

import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.dialogflow.common.ApplicationConstant;
import com.yiwise.dialogflow.engine.resource.PatternCache;
import com.yiwise.dialogflow.pattern.PatternEnhance;
import com.yiwise.dialogflow.pattern.PatternEnhanceCache;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
public class LLMAnswerHelper {

    public static Optional<String> findHangupAnswer(String llmAnswer) {
        if (StringUtils.isBlank(llmAnswer)) {
            return Optional.empty();
        }
        List<PatternEnhance> patternEnhanceList = getHangupPatternList();
        if (CollectionUtils.isEmpty(patternEnhanceList)) {
            return Optional.empty();
        }
        for (PatternEnhance pattern : patternEnhanceList) {
            Optional<String> find = pattern.find(llmAnswer);
            if (find.isPresent()) {
                return find;
            }
        }
        return Optional.empty();
    }

    private static List<PatternEnhance> getHangupPatternList() {
        if (StringUtils.isBlank(ApplicationConstant.LLM_ANSWER_HANGUP_REGEX_JSON_LIST)) {
            return Collections.emptyList();
        }

        try {
            List<String> regexList = JsonUtils.string2ListObject(ApplicationConstant.LLM_ANSWER_HANGUP_REGEX_JSON_LIST, String.class);
            if (CollectionUtils.isEmpty(regexList)) {
                return Collections.emptyList();
            }

            return regexList.stream()
                    .map(regex -> PatternEnhanceCache.getOrCreate(regex, PatternCache.compile(regex), false))
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.warn("[LogHub_Warn]解析大模型生成的内容包含指定的正则失败", e);
            return Collections.emptyList();
        }
    }
}
