package com.yiwise.dialogflow.engine.service;

import com.yiwise.dialogflow.engine.analysis.OriginChatData;
import com.yiwise.dialogflow.engine.context.SessionContext;
import com.yiwise.dialogflow.engine.resource.RobotRuntimeResource;
import com.yiwise.dialogflow.engine.share.CallDataInfo;
import com.yiwise.dialogflow.engine.share.response.SessionInfo;

import java.util.List;

/**
 * 宜信定制化的两个字段
 * https://k3465odso4.feishu.cn/wiki/U5G5wMBHSi1IqkktAnVcOgtZnic
 */
public interface YixinAnalysisService {

    List<String> analysis(RobotRuntimeResource resource,
                          SessionContext sessionContext,
                          SessionInfo sessionInfo,
                          CallDataInfo callDataInfo,
                          OriginChatData originChatData);
}
