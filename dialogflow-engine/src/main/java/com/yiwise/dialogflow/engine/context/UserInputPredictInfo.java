package com.yiwise.dialogflow.engine.context;

import lombok.Data;

import java.util.List;

/**
 * 用户每句输入的预测信息(包含所有候选结果)
 */
@Data
public class UserInputPredictInfo {
    /**
     * 对话事件序列
     */
    Integer sequence;

    /**
     * 用户输入
     */
    String userInput;

    /**
     * 预测到的意图 id 列表
     */
    List<String> predictIntentIdList;

    /**
     * 当前处于活跃状态的节点 id
     */
    String activeNodeId;
}
