package com.yiwise.dialogflow.engine;

import com.google.common.collect.ImmutableMap;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.common.ApplicationConstant;
import com.yiwise.dialogflow.engine.chatfilter.ChatFilter;
import com.yiwise.dialogflow.engine.chatprocessor.ChatPostProcessor;
import com.yiwise.dialogflow.engine.chatprocessor.ChatPreProcessor;
import com.yiwise.dialogflow.engine.context.EventContext;
import com.yiwise.dialogflow.engine.context.SessionContext;
import com.yiwise.dialogflow.engine.dispatcher.ChatDispatcher;
import com.yiwise.dialogflow.engine.resource.RobotRuntimeResource;
import com.yiwise.dialogflow.engine.share.ChatMetaData;
import com.yiwise.dialogflow.engine.share.enums.ChatEventTypeEnum;
import com.yiwise.dialogflow.engine.share.enums.RobotSnapshotUsageTargetEnum;
import com.yiwise.dialogflow.engine.share.request.*;
import com.yiwise.dialogflow.engine.share.response.ChatResponse;
import com.yiwise.dialogflow.engine.share.response.SessionInfo;
import com.yiwise.dialogflow.engine.utils.AnswerRenderUtils;
import com.yiwise.dialogflow.engine.utils.DebugLogUtils;
import com.yiwise.dialogflow.engine.utils.SessionContextSerializeUtils;
import com.yiwise.dialogflow.utils.AnswerTextUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractChatEngine implements ChatEngine {
    private static final ChatResponse EMPTY_RESPONSE = new ChatResponse(null);
    protected final Long botId;

    protected final RobotRuntimeResource resource;

    protected final ImmutableMap<ChatEventTypeEnum, EventProcessPipeline> pipelineMap;

    private final RobotSnapshotUsageTargetEnum usageTarget;

    public AbstractChatEngine(Long botId, RobotSnapshotUsageTargetEnum usageTarget, RobotRuntimeResource resource) {
        log.info("初始化engine, botId={}, usageTarget={}, version={}", botId, usageTarget, resource.getVersion());
        // 各种组件初始化
        this.usageTarget = usageTarget;
        this.botId = botId;
        this.resource = resource;
        initChatComponent();
        this.pipelineMap = ImmutableMap.copyOf(initEventPipeline());
    }

    protected Map<ChatEventTypeEnum, EventProcessPipeline> initEventPipeline() {
        Map<ChatEventTypeEnum, EventProcessPipeline> pipelineMap = new HashMap<>(ChatEventTypeEnum.values().length);
        for (ChatEventTypeEnum eventType : ChatEventTypeEnum.values()) {
            pipelineMap.put(eventType, createEventPipeline(eventType));
        }
        return pipelineMap;
    }

    protected EventProcessPipeline createEventPipeline(ChatEventTypeEnum eventType) {
        EventProcessPipeline pipeline = new EventProcessPipeline();
        pipeline.setChatFilterList(initChatFilter(eventType));
        pipeline.setPreProcessorList(initPreProcessorList(eventType));
        pipeline.setChatDispatcher(initChatDispatcher(eventType));
        pipeline.setPostProcessorList(initPostProcessorList(eventType));

        String chatFilterNames = pipeline.getChatFilterList().stream().map(ChatFilter::getName).collect(Collectors.joining(","));
        String preName = pipeline.getPreProcessorList().stream().map(ChatPreProcessor::getName).collect(Collectors.joining(","));
        String postName = pipeline.getPostProcessorList().stream().map(ChatPostProcessor::getName).collect(Collectors.joining(","));
        String dispatcher = pipeline.getChatDispatcher().getName();
        log.info("初始化[{}] Pipeline,  chatFilter=[{}], preProcessor=[{}], dispatcher=[{}], postProcessor=[{}]", eventType, chatFilterNames, preName, dispatcher, postName);
        return pipeline;
    }

    protected abstract List<ChatPostProcessor> initPostProcessorList(ChatEventTypeEnum eventType);

    protected abstract ChatDispatcher initChatDispatcher(ChatEventTypeEnum eventType);

    protected abstract List<ChatPreProcessor> initPreProcessorList(ChatEventTypeEnum eventType);

    protected abstract List<ChatFilter> initChatFilter(ChatEventTypeEnum eventType);

    protected SessionContext createSessionContext(String sessionId,
                                                  ChatMetaData metaData,
                                                  Map<String, String> magicTemplateVarValueMap) {
        SessionContext sessionContext = new SessionContext();
        sessionContext.setSessionId(sessionId);
        sessionContext.setBotId(botId);
        sessionContext.setVersion(resource.getVersion());
        sessionContext.setUsageTarget(resource.getUsageTarget());

        Map<String, String> tempMap = new HashMap<>();
        if (MapUtils.isNotEmpty(resource.getTemplateVarNameValueMap())) {
            tempMap.putAll(resource.getTemplateVarNameValueMap());
        }
        if (MapUtils.isNotEmpty(magicTemplateVarValueMap)) {
            tempMap.putAll(magicTemplateVarValueMap);
        }
        if (Objects.nonNull(metaData) && MapUtils.isNotEmpty(metaData.getProperties())) {
            tempMap.putAll(metaData.getProperties());
        }
        tempMap.forEach((varName, varValue) -> {
            if (StringUtils.isNotBlank(varValue) && varValue.contains("${")) {
                sessionContext.getGlobalVariableValueMap().put(varName, AnswerTextUtils.renderTemplate(varValue, tempMap));
            } else {
                sessionContext.getGlobalVariableValueMap().put(varName, varValue);
            }
        });

        return sessionContext;
    }

    @Override
    public SessionContext generateAndInitSessionContext(SessionInfo sessionInfo, ChatMetaData chatMetaData) {
        return generateAndInitSessionContext(sessionInfo, chatMetaData, null);
    }
    @Override
    public SessionContext generateAndInitSessionContext(SessionInfo sessionInfo, ChatMetaData chatMetaData, Map<String, String> magicTemplateVarValueMap) {
        SessionContext sessionContext = createSessionContext(sessionInfo.getSessionId(), chatMetaData, magicTemplateVarValueMap);
        initSessionContext(sessionContext);
        return sessionContext;
    }

    private void initSessionContext(SessionContext sessionContext) {
        pipelineMap.forEach((eventType, pipeline) -> pipeline.initContext(sessionContext));
    }

    /**
     * 初始化各种组件, 比如filter, preProcessor, dispatcher等
     */
    protected abstract void initChatComponent();

    @Override
    public RobotRuntimeResource getResource() {
        return resource;
    }

    @Override
    public final Flux<ChatResponse> processAsync(ChatRequest request) {
        // 断句补齐状态回退
        EventContext eventContext = initEventContext();
        eventContext.setLogId(MDC.get(ApplicationConstant.MDC_LOG_ID));
        if (needRollbackSessionContext(request.getParam())) {
            DebugLogUtils.commonDebugLog(eventContext, "断句补齐, 重新判断");
            eventContext.setMergeRequest(true);
        }
        SessionContext sessionContext = parseSessionContextFromJson(request.getSessionContextJson());
        if (Objects.isNull(sessionContext)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "解析sessionContext失败");
        }
        if (ChatEventTypeEnum.LLM_REQUEST.equals(request.getParam().getEvent())) {
            throw new ComException(ComErrorCode.NOT_SUPPORT, "processRequest接口不支持LLM事件, 请使用llmProcess接口");
        }
        // 开始执行对话逻辑
        long asyncStartTime = System.currentTimeMillis();
        Flux<ChatResponse> responseFlux = doProcessAsync(sessionContext, eventContext, request);
        final SessionContext finalSessionContext = sessionContext;
        return responseFlux
                .doOnNext(chatResponse -> {
                    if (chatResponse != null) {
                        chatResponse.setSessionId(request.getSessionId());
                        String sessionContextJson = sessionContextToJson(finalSessionContext);
                        if (!StringUtils.equals(request.getSessionContextJson(), sessionContextJson)) {
                            chatResponse.setSessionContextJson(sessionContextJson);
                        }
                    }
                    long end = System.currentTimeMillis();
                    long cost = end - eventContext.getStartTime();
                    long asyncCost = end - asyncStartTime;
                    if (cost > 10 || asyncCost > 10) {
                        log.debug("process end, cost=[{}]ms, asyncCost=[{}]ms", end - eventContext.getStartTime(), end - asyncStartTime);
                    }
                })
                .doOnError((exception) -> {
                    log.warn("[LogHub_Warn] 处理对话异常, botId:{}", request.getBotId(), exception);
                });
    }

    @Override
    public Flux<ChatResponse> llmProcess(ChatRequest request) {
        // 断句补齐状态回退
        EventContext eventContext = initEventContext();
        eventContext.setLogId(MDC.get(ApplicationConstant.MDC_LOG_ID));
        if (needRollbackSessionContext(request.getParam())) {
            DebugLogUtils.commonDebugLog(eventContext, "断句补齐, 重新判断");
            eventContext.setMergeRequest(true);
        }
        SessionContext sessionContext = parseSessionContextFromJson(request.getSessionContextJson());
        if (Objects.isNull(sessionContext)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "解析sessionContext失败");
        }
        if (!ChatEventTypeEnum.LLM_REQUEST.equals(request.getParam().getEvent())) {
            throw new ComException(ComErrorCode.NOT_SUPPORT, "llmProcess接口只支持LLM事件, 请使用processRequest接口");
        }
        // 开始执行对话逻辑
        long asyncStartTime = System.currentTimeMillis();
        Flux<ChatResponse> response = doLlmAsync(sessionContext, eventContext, request);
        final SessionContext finalSessionContext = sessionContext;
        return response
                .doOnNext(chatResponse -> {
                    chatResponse.setSessionId(request.getSessionId());
                    String sessionContextJson = sessionContextToJson(finalSessionContext);
                    if (!StringUtils.equals(request.getSessionContextJson(), sessionContextJson)) {
                        chatResponse.setSessionContextJson(sessionContextJson);
                    }
                    long end = System.currentTimeMillis();
                    long cost = end - eventContext.getStartTime();
                    long asyncCost = end - asyncStartTime;
                    if (cost > 10 || asyncCost > 10) {
                        log.debug("llmProcess end, cost=[{}]ms, asyncCost=[{}]ms", end - eventContext.getStartTime(), end - asyncStartTime);
                    }
                });
    }

    protected EventContext initEventContext() {
        return new EventContext();
    }

    protected final Flux<ChatResponse> doProcessAsync(SessionContext sessionContext,
                                                      EventContext eventContext,
                                                      ChatRequest request) {
        EventParam param = request.getParam();
        if (Objects.nonNull(request.getSequence())) {
            eventContext.setSeq(request.getSequence());
        } else {
            // todo
            eventContext.setSeq(1);
        }
        eventContext.setEvent(param.getEvent());
        eventContext.setOriginEventParam(param);
        if (param instanceof UserSayEvent) {
            eventContext.setUserInput(((UserSayEvent) param).getInputText());
            String originInputText = ((UserSayEvent) param).getOriginInputText();
            if (StringUtils.isBlank(originInputText)) {
                originInputText = ((UserSayEvent) param).getInputText();
            }
            eventContext.setOriginUserInput(originInputText);
        } else if (param instanceof LLMRequestEvent) {
            LLMRequestEvent llmRequestEvent = (LLMRequestEvent) param;
            eventContext.setUserInput(llmRequestEvent.getInputText());
        }
        EventProcessPipeline pipeline = pipelineMap.get(param.getEvent());

        AtomicLong start = new AtomicLong(System.currentTimeMillis());
        return processPipelineAsync(sessionContext, pipeline, eventContext)
                .doOnSubscribe(subscription -> {
                    start.set(System.currentTimeMillis());
                    log.info("=========开始执行{}-pipeline=========", param.getEvent());
                })
                .doOnNext(response -> {
                    long end = System.currentTimeMillis();
                    response.setSessionId(sessionContext.getSessionId());
                    response.setSequence(request.getSequence());
                    response.setDebugLogList(eventContext.getDebugLog());
                    response.setSimpleDebugLogList(eventContext.getSimpleDebugLog());
                    response.setInputText(eventContext.getUserInput());
                    log.info("*********{}-pipeline执行完成, costTime:{}ms **********", param.getEvent(), (end -start.get()));
                });
    }

    protected final Flux<ChatResponse> doLlmAsync(SessionContext sessionContext,
                                                  EventContext eventContext,
                                                  ChatRequest request) {
        EventParam param = request.getParam();
        if (Objects.nonNull(request.getSequence())) {
            eventContext.setSeq(request.getSequence());
        } else {
            // todo
            eventContext.setSeq(1);
        }
        eventContext.setEvent(param.getEvent());
        eventContext.setOriginEventParam(param);
        if (param instanceof UserSayEvent) {
            eventContext.setUserInput(((UserSayEvent) param).getInputText());
            String originInputText = ((UserSayEvent) param).getOriginInputText();
            if (StringUtils.isBlank(originInputText)) {
                originInputText = ((UserSayEvent) param).getInputText();
            }
            eventContext.setOriginUserInput(originInputText);
        }else if (param instanceof LLMRequestEvent) {
            LLMRequestEvent llmRequestEvent = (LLMRequestEvent) param;
            eventContext.setUserInput(llmRequestEvent.getInputText());
        }
        EventProcessPipeline pipeline = pipelineMap.get(param.getEvent());

        AtomicLong start = new AtomicLong(System.currentTimeMillis());
        return llmProcessPipelineAsync(sessionContext, pipeline, eventContext)
                .doOnSubscribe(subscription -> {
                    start.set(System.currentTimeMillis());
                    log.info("=========开始执行{}-pipeline=========", param.getEvent());
                })
                .doOnNext(response -> {
                    long end = System.currentTimeMillis();
                    response.setSessionId(sessionContext.getSessionId());
                    response.setSequence(request.getSequence());
                    response.setDebugLogList(eventContext.getDebugLog());
                    response.setSimpleDebugLogList(eventContext.getSimpleDebugLog());
                    response.setInputText(eventContext.getUserInput());
                    log.info("*********{}-pipeline执行完成, costTime:{}ms **********", param.getEvent(), (end -start.get()));
                });
    }

    private boolean needRollbackSessionContext(EventParam event) {
        if (!ChatEventTypeEnum.USER_SAY_FINISH.equals(event.getEvent())) {
            return false;
        }
        return BooleanUtils.isTrue(((UserSayFinishEvent) event).getIsMergeInput());
    }

    private SessionContext parseSessionContextFromJson(String json) {
        return SessionContextSerializeUtils.deserialize(json);
    }

    private String sessionContextToJson(SessionContext sessionContext) {
        return SessionContextSerializeUtils.serialize(sessionContext);
    }

    private Flux<ChatResponse> processFilter(SessionContext sessionContext, EventProcessPipeline pipeline, EventContext eventContext) {
        // 顺序执行过滤器, 并返回第一个非空的结果
        List<ChatFilter> chatFilters = pipeline.getChatFilterList();
        if (CollectionUtils.isEmpty(chatFilters)) {
            return Flux.empty();
        }

        return Flux.fromIterable(chatFilters)
                .concatMap(filter -> filter.filterAsync(sessionContext, eventContext))
                .take(1);
    }

    private Flux<ChatResponse> processProcessorAsync(SessionContext sessionContext,
                                                     EventProcessPipeline pipeline,
                                                     EventContext eventContext) {
        return Flux.fromIterable(pipeline.getPreProcessorList())
                // 执行前置处理器, 不需要结果, 执行结果会更新到eventContext中
                .concatMap(processor ->  processor.processAsync(sessionContext, eventContext))
                // 执行dispatcher
                .thenMany(Flux.defer(() -> {
                    // 执行dispatcher
                    return pipeline.getChatDispatcher().dispatchAsync(sessionContext, eventContext)
                            .doOnNext(response -> {
                               log.debug("response:{}", response);
                            });
                }));
    }

    private Flux<ChatResponse> llmProcessProcessorAsync(SessionContext sessionContext,
                                                     EventProcessPipeline pipeline,
                                                     EventContext eventContext) {
        return Flux.fromIterable(pipeline.getPreProcessorList())
                // 执行前置处理器, 不需要结果, 执行结果会更新到eventContext中
                .concatMap(processor ->  processor.processAsync(sessionContext, eventContext))
                // 执行dispatcher
                .thenMany(Flux.defer(() -> {
                    // 执行dispatcher
                    return pipeline.getChatDispatcher().dispatchLLMEventAsync(sessionContext, eventContext);
                }));
    }

    protected Flux<ChatResponse> processPipelineAsync(SessionContext sessionContext, EventProcessPipeline pipeline, EventContext eventContext) {
        // 处理过滤器
        return processFilter(sessionContext, pipeline, eventContext)
                // 没有被过滤, 执行后续处理
                .switchIfEmpty(Flux.defer(() -> {
                    // 处理前置处理器, dispatcher, 后置处理器
                    return processProcessorAsync(sessionContext, pipeline, eventContext);
                }))
                // 执行后置处理器
                .flatMap(chatResponse -> Flux.fromIterable(pipeline.getPostProcessorList())
                        .concatMap(postProcessor -> postProcessor.processAsync(sessionContext, eventContext, chatResponse))
                        .then(Mono.just(chatResponse))
                );
    }
    protected Flux<ChatResponse> llmProcessPipelineAsync(SessionContext sessionContext, EventProcessPipeline pipeline, EventContext eventContext) {
        // 处理过滤器
        return processFilter(sessionContext, pipeline, eventContext)
                // 没有被过滤, 执行后续处理
                .defaultIfEmpty(EMPTY_RESPONSE)
                .flatMap(chatResponse -> {
                    if (EMPTY_RESPONSE.equals(chatResponse)) {
                        return llmProcessProcessorAsync(sessionContext, pipeline, eventContext);
                    } else {
                        return Flux.just(chatResponse);
                    }
                })
                // 执行后置处理器
                .flatMap(chatResponse -> Flux.fromIterable(pipeline.getPostProcessorList())
                        .concatMap(postProcessor -> postProcessor.processAsync(sessionContext, eventContext, chatResponse))
                        .then(Mono.just(chatResponse))
                );
    }
}
