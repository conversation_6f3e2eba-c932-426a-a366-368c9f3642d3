package com.yiwise.dialogflow.engine.utils;

import com.yiwise.base.common.context.AppContextUtils;
import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.dialogflow.common.ApplicationConstant;
import com.yiwise.dialogflow.common.MdcLogIdLifterTransformer;
import com.yiwise.dialogflow.engine.domain.EntityCollectContext;
import com.yiwise.dialogflow.engine.helper.SessionContextHelper;
import com.yiwise.dialogflow.entity.bo.EntityValueBO;
import com.yiwise.dialogflow.entity.bo.entity.RuntimeLargeModelEntityBO;
import com.yiwise.dialogflow.entity.enums.EntityTypeEnum;
import com.yiwise.dialogflow.entity.enums.LargeModelEntityModelTypeEnum;
import com.yiwise.dialogflow.entity.po.EntitySynonymPO;
import com.yiwise.dialogflow.entity.query.EntityMemberGenerateRequestVO;
import com.yiwise.dialogflow.entity.query.EntitySynonymGenerateRequestVO;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.http.HttpMethod;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.io.Serializable;
import java.time.Duration;
import java.time.temporal.ChronoUnit;
import java.util.*;

/**
 * <AUTHOR>
 */
@Slf4j
public class LargeModelEntityCollectUtils {

    private static final WebClient webClient = (WebClient) AppContextUtils.getBean("largeModelCollectWebClient");

    public static Flux<EntityValueBO> collect(String inputText, List<RuntimeLargeModelEntityBO> entityList, EntityCollectContext entityCollectContext) {
        if (CollectionUtils.isEmpty(entityList)) {
            return Flux.empty();
        }

        return Flux.fromIterable(entityList)
                .flatMap(entity -> {
                    LargeModelEntityModelTypeEnum modelType = entity.getModelType();
                    if (LargeModelEntityModelTypeEnum.isTuneModel(modelType)) {
                        return tunedModelPredict(entity, inputText, entityCollectContext);
                    }
                    if (LargeModelEntityModelTypeEnum.isCommonModel(modelType)) {
                        return commonModelPredict(entity, inputText);
                    }
                    return Mono.empty();
                }).concatMap(Flux::fromIterable);
    }

    private static Mono<List<EntityValueBO>> tunedModelPredict(RuntimeLargeModelEntityBO entity, String inputText, EntityCollectContext entityCollectContext) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("env", ApplicationConstant.ALGORITHM_SERVICE_NAMESPACE);
        paramMap.put("bot_id", entity.getBotId());
        paramMap.put("user_content", inputText);
        if (Objects.nonNull(entityCollectContext)) {
            paramMap.put("service_content", SessionContextHelper.getLastAiSay(entityCollectContext.getSessionContext(), entityCollectContext.getRobotRuntimeResource()));
        }
        paramMap.put("entity_title", entity.getName());
        paramMap.put("entity_description", entity.getDesc());
        List<Map<String, Object>> choiceList = new ArrayList<>();
        for (EntitySynonymPO member : entity.getMemberList()) {
            Map<String, Object> map = new HashMap<>();
            map.put("value", member.getName());
            map.put("descriptions", member.getSynonymList());
            choiceList.add(map);
        }
        paramMap.put("choices", choiceList);

        String url = ApplicationConstant.ALGORITHM_NLU_ENTITY_PREDICT_URL;
        String body = JsonUtils.object2String(paramMap);
        log.info("大模型NLU实体提取, url={}, body={}", url, body);
        long start = System.currentTimeMillis();

        return webClient.method(HttpMethod.POST)
                .uri(url)
                .body(BodyInserters.fromValue(body))
                .exchange().timeout(Duration.of(15000, ChronoUnit.MILLIS))
                .flatMap(response -> response.bodyToMono(String.class))
                .transformDeferred(MdcLogIdLifterTransformer.lift())
                .doOnNext(response -> log.info("大模型NLU实体提取, cost={}ms, response={}", System.currentTimeMillis() - start, response))
                .map(json -> parseTunedModelResult(inputText, json, entity))
                .onErrorResume(e -> {
                    log.warn("[LogHub_Warn]大模型NLU实体提取失败, url={}, body={}", url, body, e);
                    return Mono.empty();
                });
    }

    @Data
    @FieldDefaults(level = AccessLevel.PRIVATE)
    static class NluRes implements Serializable {

        Integer code;

        List<NluEntityRes> entities;

        public boolean isSuccess() {
            return Objects.nonNull(code) && code == 0;
        }
    }

    @Data
    @FieldDefaults(level = AccessLevel.PRIVATE)
    static class NluEntityRes implements Serializable {

        String value;
    }

    private static List<EntityValueBO> parseTunedModelResult(String inputText, String json, RuntimeLargeModelEntityBO entity) {
        if (StringUtils.isBlank(json)) {
            return Collections.emptyList();
        }

        NluRes response = JsonUtils.string2Object(json, NluRes.class);
        if (Objects.isNull(response) || !response.isSuccess() || CollectionUtils.isEmpty(response.getEntities())) {
            return Collections.emptyList();
        }

        List<EntityValueBO> list = new ArrayList<>();
        for (NluEntityRes res : response.getEntities()) {
            String result = res.getValue();
            if (StringUtils.isBlank(result)) {
                continue;
            }
            EntityValueBO entityValue = new EntityValueBO();
            entityValue.setOriginValue(result);
            entityValue.setValue(result);
            int indexOf = inputText.indexOf(result);
            if (indexOf == -1) {
                entityValue.setStartOffset(0);
                entityValue.setEndOffset(inputText.length() - 1);
            } else {
                entityValue.setStartOffset(indexOf);
                entityValue.setEndOffset(indexOf + result.length() - 1);
            }
            entityValue.setEntityType(EntityTypeEnum.LARGE_MODEL);
            entityValue.setEntityId(entity.getId());
            entityValue.setEntityName(entity.getName());
            entityValue.setInputText(inputText);
            list.add(entityValue);
        }
        return list;
    }

    private static Mono<List<EntityValueBO>> commonModelPredict(RuntimeLargeModelEntityBO entity, String inputText) {
        Map<String, String> param = new HashMap<>(2);
        param.put("prompt", entity.getPrompt());
        param.put("text", inputText);
        param.put("llm_service_name", ApplicationConstant.LARGE_MODEL_ENTITY_COLLECT_SERVICE_NAME);
        param.put("model_version", ApplicationConstant.LARGE_MODEL_ENTITY_COLLECT_VERSION);
        String url = ApplicationConstant.LARGE_MODEL_ENTITY_COLLECT_URL;
        String body = JsonUtils.object2String(param);
        log.info("大模型实体提取, url={}, body={}", url, body);
        long start = System.currentTimeMillis();

        return webClient.method(HttpMethod.POST)
                .uri(url)
                .body(BodyInserters.fromValue(body))
                .exchange().timeout(Duration.of(15000, ChronoUnit.MILLIS))
                .flatMap(response -> response.bodyToMono(String.class))
                .transformDeferred(MdcLogIdLifterTransformer.lift())
                .doOnNext(response -> log.info("大模型实体提取, cost={}ms, response={}", System.currentTimeMillis() - start, response))
                .map(json -> parseCommonModelResult(inputText, json, entity))
                .onErrorResume(e -> {
                    log.warn("[LogHub_Warn]大模型实体提取失败, url={}, body={}", url, body, e);
                    return Mono.empty();
                });
    }

    private static List<EntityValueBO> parseCommonModelResult(String inputText, String json, RuntimeLargeModelEntityBO entity) {
        if (StringUtils.isBlank(json)) {
            return Collections.emptyList();
        }

        Response response = JsonUtils.string2Object(json, Response.class);
        if (Objects.isNull(response) || CollectionUtils.isEmpty(response.getResult())) {
            return Collections.emptyList();
        }

        List<EntityValueBO> list = new ArrayList<>();
        for (String result : response.getResult()) {
            EntityValueBO entityValue = new EntityValueBO();
            entityValue.setOriginValue(result);
            entityValue.setValue(result);
            int indexOf = inputText.indexOf(result);
            if (indexOf == -1) {
                entityValue.setStartOffset(0);
                entityValue.setEndOffset(inputText.length() - 1);
            } else {
                entityValue.setStartOffset(indexOf);
                entityValue.setEndOffset(indexOf + result.length() - 1);
            }
            entityValue.setEntityType(EntityTypeEnum.LARGE_MODEL);
            entityValue.setEntityId(entity.getId());
            entityValue.setEntityName(entity.getName());
            entityValue.setInputText(inputText);
            list.add(entityValue);
        }
        return list;
    }

    @Data
    private static class Response implements Serializable {

        List<String> result;
    }

    public static Mono<List<String>> generateSynonymList(EntitySynonymGenerateRequestVO request) {
        Map<String, Object> param = new HashMap<>(4);
        param.put("word", request.getMemberName());
        param.put("limit", request.getNum());
        param.put("llm_service_name", ApplicationConstant.LARGE_MODEL_GENERATE_SERVICE_NAME);
        param.put("model_version", ApplicationConstant.LARGE_MODEL_GENERATE_VERSION);
        String url = ApplicationConstant.LARGE_MODEL_GENERATE_SYNONYM_URL;
        String body = JsonUtils.object2String(param);
        log.info("大模型生成同义词, url={}, body={}", url, body);
        long start = System.currentTimeMillis();
        return webClient.method(HttpMethod.POST)
                .uri(url)
                .body(BodyInserters.fromValue(body))
                .retrieve()
                .bodyToMono(String.class)
                .transformDeferred(MdcLogIdLifterTransformer.lift())
                .doOnNext(response -> log.info("大模型生成同义词, cost={}ms, response={}", System.currentTimeMillis() - start, response))
                .map(LargeModelEntityCollectUtils::parseCommonModelResult)
                .onErrorResume(e -> {
                    log.warn("[LogHub_Warn]大模型生成同义词失败, url={}, body={}", url, body, e);
                    return Mono.just(Collections.emptyList());
                })
                .subscriberContext(context -> context.put(ApplicationConstant.MDC_LOG_ID, MDC.get(ApplicationConstant.MDC_LOG_ID)));
    }

    public static Mono<List<String>> generateMemberList(EntityMemberGenerateRequestVO request) {
        Map<String, Object> param = new HashMap<>(4);
        param.put("words", request.getMemberNameList());
        param.put("limit", request.getNum());
        param.put("llm_service_name", ApplicationConstant.LARGE_MODEL_GENERATE_SERVICE_NAME);
        param.put("model_version", ApplicationConstant.LARGE_MODEL_GENERATE_VERSION);
        String url = ApplicationConstant.LARGE_MODEL_GENERATE_MEMBER_URL;
        String body = JsonUtils.object2String(param);
        log.info("大模型生成相似实体, url={}, body={}", url, body);
        long start = System.currentTimeMillis();
        return webClient.method(HttpMethod.POST)
                .uri(url)
                .body(BodyInserters.fromValue(body))
                .retrieve()
                .bodyToMono(String.class)
                .transformDeferred(MdcLogIdLifterTransformer.lift())
                .doOnNext(response -> log.info("大模型生成相似实体, cost={}ms, response={}", System.currentTimeMillis() - start, response))
                .map(LargeModelEntityCollectUtils::parseCommonModelResult)
                .onErrorResume(e -> {
                    log.warn("[LogHub_Warn]大模型生成相似实体失败, url={}, body={}", url, body, e);
                    return Mono.just(Collections.emptyList());
                })
                .subscriberContext(context -> context.put(ApplicationConstant.MDC_LOG_ID, MDC.get(ApplicationConstant.MDC_LOG_ID)));
    }

    private static List<String> parseCommonModelResult(String json) {
        Response response = JsonUtils.string2Object(json, Response.class);
        if (Objects.isNull(response) || CollectionUtils.isEmpty(response.getResult())) {
            return Collections.emptyList();
        }
        return response.getResult();
    }
}
