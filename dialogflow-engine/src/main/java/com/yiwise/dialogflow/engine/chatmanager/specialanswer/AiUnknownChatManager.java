package com.yiwise.dialogflow.engine.chatmanager.specialanswer;

import com.yiwise.dialogflow.engine.chatmanager.ChatManagerPriorityEnum;
import com.yiwise.dialogflow.engine.chatmanager.ChatManagerTriggerCondition;
import com.yiwise.dialogflow.engine.context.*;
import com.yiwise.dialogflow.engine.enums.SpecialChatModeEnum;
import com.yiwise.dialogflow.engine.resource.RobotRuntimeResource;
import com.yiwise.dialogflow.entity.po.SpecialAnswerConfigPO;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
public class AiUnknownChatManager extends AbstractCommonSpecialAnswerChatManager {

    public AiUnknownChatManager(RobotRuntimeResource resource) {
        super(resource, resource.getSpecialAnswerNameMap().get(SpecialAnswerConfigPO.AI_UNKNOWN));
    }

    @Override
    public void initContext(SessionContext sessionContext) {
        sessionContext.setAiUnknownContext(new AiUnknownContext());
    }

    @Override
    public String getName() {
        return "ai无法应答";
    }

    @Override
    protected CommonSpecialAnswerContext getSpecialAnswerContext(SessionContext sessionContext) {
        return sessionContext.getAiUnknownContext();
    }

    @Override
    public List<ChatManagerTriggerCondition> getTriggerConditions(SessionContext sessionContext, EventContext context) {
        if (!enable) {
            return Collections.emptyList();
        }
        ChatManagerTriggerCondition condition = new ChatManagerTriggerCondition(ChatManagerPriorityEnum.DEFAULT);
        if (context.getCanInterruptSpecialAnswerIdSet().contains(specialAnswerConfig.getId())) {
            // 当前处于不可打断, 判断当前节点是否允许响应该问答知识
            log.info("当前处于不可打断, 但当前节点允许响应特殊语境:{}", specialAnswerConfig.getName());
        } else {
            condition.getMustNotMatchModes().add(SpecialChatModeEnum.UNINTERRUPTED);
        }
        return Collections.singletonList(condition);
    }
}
