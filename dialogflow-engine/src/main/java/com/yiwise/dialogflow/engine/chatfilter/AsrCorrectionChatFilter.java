package com.yiwise.dialogflow.engine.chatfilter;

import com.yiwise.base.common.context.AppContextUtils;
import com.yiwise.dialogflow.common.ApplicationConstant;
import com.yiwise.dialogflow.engine.context.EventContext;
import com.yiwise.dialogflow.engine.context.SessionContext;
import com.yiwise.dialogflow.engine.resource.AsrCorrectionRuntimeConfig;
import com.yiwise.dialogflow.engine.resource.RobotRuntimeResource;
import com.yiwise.dialogflow.engine.share.request.UserSayEvent;
import com.yiwise.dialogflow.engine.share.response.ChatResponse;
import com.yiwise.dialogflow.entity.vo.asrmodel.AsrCorrectionRequestVO;
import com.yiwise.dialogflow.service.asrmodel.AsrErrorCorrectionService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import reactor.core.publisher.Mono;

import java.util.Objects;
import java.util.Optional;

@Slf4j
public class AsrCorrectionChatFilter extends AbstractChatFilter {

    private static final AsrErrorCorrectionService asrErrorCorrectionService = AppContextUtils.getBean(AsrErrorCorrectionService.class);
    private final AsrCorrectionRuntimeConfig asrCorrectionConfig;

    public AsrCorrectionChatFilter(RobotRuntimeResource resource) {
        this.asrCorrectionConfig = resource.getAsrCorrectionRuntimeConfig();
    }

    @Override
    public void initContext(SessionContext sessionContext) {

    }

    @Override
    public String getName() {
        return "AsrCorrectionChatFilter";
    }

    @Override
    public Optional<ChatResponse> doFilter(SessionContext sessionContext, EventContext eventContext) {
        // 执行asr纠错
        String userInput = eventContext.getUserInput();
        String correctionResult = doAsrErrCorrection(sessionContext.getBotId(), userInput);
        if (!StringUtils.equals(userInput, correctionResult)) {
            eventContext.setUserInput(correctionResult);
            if (eventContext.getOriginEventParam() instanceof UserSayEvent) {
                ((UserSayEvent) eventContext.getOriginEventParam()).setInputText(correctionResult);
            }
            log.info("asr纠错结果: {}", correctionResult);
        }

        return Optional.empty();
    }

    @Override
    public Mono<ChatResponse> doFilterAsync(SessionContext sessionContext, EventContext eventContext) {
        // 执行asr纠错
        String userInput = eventContext.getUserInput();
        return doAsrErrCorrectionAsync(sessionContext.getBotId(), userInput)
                .doOnNext(correctionResult -> {
                    if (!StringUtils.equals(userInput, correctionResult)) {
                        eventContext.setUserInput(correctionResult);
                        if (eventContext.getOriginEventParam() instanceof UserSayEvent) {
                            ((UserSayEvent) eventContext.getOriginEventParam()).setInputText(correctionResult);
                        }
                        log.info("asr纠错结果: {}", correctionResult);
                    }
                }).then(Mono.empty());
    }

    private String doAsrErrCorrection(Long botId, String realMatchingText) {
        if (StringUtils.isBlank(realMatchingText)) {
            return realMatchingText;
        }
        try {
            if (CollectionUtils.isNotEmpty(asrCorrectionConfig.getAsrCorrectionWhiteList())) {
                for (String text : asrCorrectionConfig.getAsrCorrectionWhiteList()) {
                    if (realMatchingText.contains(text)) {
                        return realMatchingText;
                    }
                }
            }

            String result;
            if (StringUtils.isEmpty(asrCorrectionConfig.getAsrErrorCorrectionDetailId())) {
                log.info("未关联ASR纠错模型");
                return realMatchingText;
            }
            // 请求v3的ASR纠错
            result = asrErrorCorrectionService.requestAsrErrorCorrectionByBotId(botId, realMatchingText);

            if (!realMatchingText.equals(result)) {
                log.info("执行asr纠错模型进行文本纠正, 纠错前=[{}], 纠错后=[{}]", realMatchingText, result);
            }
            return result;
        } catch (Exception e) {
            log.error("执行ASR纠错异常", e);
            return realMatchingText;
        }
    }

    private Mono<String> doAsrErrCorrectionAsync(Long botId, String realMatchingText) {
        if (StringUtils.isBlank(realMatchingText)) {
            return Mono.just(realMatchingText);
        }
        try {
            if (CollectionUtils.isNotEmpty(asrCorrectionConfig.getAsrCorrectionWhiteList())) {
                for (String text : asrCorrectionConfig.getAsrCorrectionWhiteList()) {
                    if (realMatchingText.contains(text)) {
                        return Mono.just(realMatchingText);
                    }
                }
            }

            if (StringUtils.isEmpty(asrCorrectionConfig.getAsrErrorCorrectionDetailId())
                    || Objects.isNull(asrCorrectionConfig.getModelId())
                    || Objects.isNull(asrCorrectionConfig.getThreshold())) {
                if (ApplicationConstant.enableDebug) {
                    log.info("未关联ASR纠错模型, 或配置不完整: asrErrorCorrectionDetailId={}, modelId={}, threshold={}",
                            asrCorrectionConfig.getAsrErrorCorrectionDetailId(), asrCorrectionConfig.getModelId(), asrCorrectionConfig.getThreshold());
                }
                return Mono.just(realMatchingText);
            }

        } catch (Exception e) {
            log.error("执行ASR纠错异常", e);
            return Mono.just(realMatchingText);
        }
        // 请求v3的ASR纠错
        AsrCorrectionRequestVO request = AsrCorrectionRequestVO.builder()
                .botId(botId)
                .modelId(asrCorrectionConfig.getModelId())
                .threshold(asrCorrectionConfig.getThreshold())
                .userInput(realMatchingText)
                .build();
        return asrErrorCorrectionService.requestAsrErrorCorrection(request)
                .onErrorReturn(realMatchingText);
    }
}
