package com.yiwise.dialogflow.engine.service.entitycollect;

import com.yiwise.dialogflow.entity.bo.EntityValueBO;
import com.yiwise.dialogflow.entity.enums.SystemEntityCategoryEnum;
import com.yiwise.dialogflow.utils.NumberUtils;
import reactor.core.publisher.Flux;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class NumberEntityCollector implements SystemEntityCollector {

    private static final Pattern NUMBER = Pattern.compile("([0-9零一二两三四五六七八九十壹贰叁肆伍陆柒捌玖拾]+[百佰千仟万萬亿]?)+");

    @Override
    public void register(Map<SystemEntityCategoryEnum, SystemEntityCollector> registry) {
        registry.put(SystemEntityCategoryEnum.NUMBER, this);
    }

    @Override
    public Flux<EntityValueBO> asyncCollect(SystemEntityCategoryEnum category, String userInput, String userInputPinyin) {
        Matcher matcher = NUMBER.matcher(userInput);
        List<EntityValueBO> list = new ArrayList<>();
        while (matcher.find()) {
            String value = matcher.group();
            String number = NumberUtils.str2Arab(value);
            EntityValueBO bo = new EntityValueBO();
            bo.setOriginValue(value);
            bo.setValue(number);
            bo.setStartOffset(matcher.start());
            bo.setEndOffset(matcher.end() - 1);
            bo.setSystemEntityCategory(category);
            bo.setInputText(userInput);
            list.add(bo);
        }
        return Flux.fromIterable(list);
    }
}
