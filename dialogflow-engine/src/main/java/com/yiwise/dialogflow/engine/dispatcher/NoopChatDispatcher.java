package com.yiwise.dialogflow.engine.dispatcher;

import com.yiwise.dialogflow.engine.share.response.ChatResponse;
import com.yiwise.dialogflow.engine.context.EventContext;
import com.yiwise.dialogflow.engine.context.SessionContext;
import reactor.core.publisher.Flux;


public class NoopChatDispatcher extends AbstractChatDispatcher {
    @Override
    public void initContext(SessionContext sessionContext) {

    }

    @Override
    public String getName() {
        return "NoopChatDispatcher";
    }

    @Override
    public Flux<ChatResponse> doDispatch(SessionContext sessionContext, EventContext eventContext) {
        return Flux.empty();
    }
}
