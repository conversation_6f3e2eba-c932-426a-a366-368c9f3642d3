package com.yiwise.dialogflow.engine.chatprocessor;

import com.yiwise.dialogflow.engine.ChatComponent;
import com.yiwise.dialogflow.engine.share.response.ChatResponse;
import com.yiwise.dialogflow.engine.context.EventContext;
import com.yiwise.dialogflow.engine.context.SessionContext;
import reactor.core.publisher.Mono;

import java.util.Optional;

/**
 * <AUTHOR>
 */
public interface ChatPostProcessor extends ChatComponent {
    Optional<ChatResponse> process(SessionContext sessionContext,
                                   EventContext eventContext,
                                   ChatResponse chatResponse);

    default Mono<ChatResponse> processAsync(SessionContext sessionContext,
                                    EventContext eventContext,
                                    ChatResponse chatResponse) {
        return Mono.justOrEmpty(process(sessionContext, eventContext, chatResponse));
    }
}
