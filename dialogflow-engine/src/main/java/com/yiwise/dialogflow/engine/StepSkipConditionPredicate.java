package com.yiwise.dialogflow.engine;

import com.yiwise.dialogflow.engine.context.SessionContext;
import com.yiwise.dialogflow.engine.resource.RobotRuntimeResource;
import com.yiwise.dialogflow.entity.po.StepSkipConditionPO;

import java.util.function.Predicate;

/**
 * 流程跳过条件断言
 */
public class StepSkipConditionPredicate extends AbstractConditionPredicate<StepSkipConditionPO> implements Predicate<StepSkipConditionPO> {

    public StepSkipConditionPredicate(RobotRuntimeResource robotRuntimeResource, SessionContext sessionContext) {
        super(robotRuntimeResource, sessionContext, null);
    }

    @Override
    public boolean test(StepSkipConditionPO stepSkipCondition) {
        return super.doTest(stepSkipCondition);
    }
}
