package com.yiwise.dialogflow.engine.chatmanager;

import com.yiwise.dialogflow.entity.bo.algorithm.PredictResult;
import com.yiwise.dialogflow.entity.enums.IntentPriorityLevelEnum;
import com.yiwise.dialogflow.entity.enums.PredictTypeEnum;

import java.util.Comparator;

/**
 * <AUTHOR>
 */
public class PredictResultComparator implements Comparator<PredictResult> {

    @Override
    public int compare(PredictResult pr1, PredictResult pr2) {
        IntentPriorityLevelEnum l1 = pr1.getIntentPriorityLevel();
        IntentPriorityLevelEnum l2 = pr2.getIntentPriorityLevel();
        if (!l1.equals(l2)) {
            return l1.getCode() - l2.getCode();
        }
        if (!pr1.getConfidence().equals(pr2.getConfidence())) {
            return pr1.getConfidence() - pr2.getConfidence() > 0 ? -1 : 1;
        }
        if (PredictTypeEnum.REGEX.equals(pr1.getPredictType())) {
            return pr2.getPattern().pattern().length() - pr1.getPattern().pattern().length();
        }
        return 0;
    }
}