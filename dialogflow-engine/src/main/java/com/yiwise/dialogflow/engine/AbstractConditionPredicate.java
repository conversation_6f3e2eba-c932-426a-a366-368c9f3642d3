package com.yiwise.dialogflow.engine;

import com.google.common.collect.ImmutableList;
import com.yiwise.dialogflow.common.ApplicationConstant;
import com.yiwise.dialogflow.engine.context.EventContext;
import com.yiwise.dialogflow.engine.context.SessionContext;
import com.yiwise.dialogflow.engine.resource.RobotRuntimeResource;
import com.yiwise.dialogflow.entity.enums.ConditionEnum;
import com.yiwise.dialogflow.entity.enums.ConditionVarTypeEnum;
import com.yiwise.dialogflow.entity.po.BaseConditionGroup;
import com.yiwise.dialogflow.entity.po.ConditionExpressionPO;
import com.yiwise.dialogflow.entity.po.VariablePO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.*;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

@Slf4j
public abstract class AbstractConditionPredicate<T extends BaseConditionGroup> {

    /**
     * k:变量id v:运行时变量实际的值,外呼之前手动填写的值
     */
    private final Map<String, String> varIdValueMap = new HashMap<>();

    /**
     * 意图预测结果集合
     */
    private final List<String> candidateIntentIdList;

    protected static final BiFunction<RobotRuntimeResource, SessionContext, Map<String, String>> defaultVarIdValueFunc = (resource, sessionContext) -> {
        Map<String, String> tmpValueMap = new HashMap<>();
        ImmutableList<VariablePO> variableList = resource.getVariableIdMap().values().asList();
        Map<String, String> globalVariableValueMap = Optional.ofNullable(sessionContext.getGlobalVariableValueMap()).orElse(Collections.emptyMap());
        Map<String, String> varNameIdMap = variableList.stream().collect(Collectors.toMap(VariablePO::getName, VariablePO::getId));
        if (ApplicationConstant.enableDebug) {
            log.debug("runtime variable:{}", varNameIdMap);
            log.debug("session variable:{}", globalVariableValueMap);
        }
        for (Map.Entry<String, String> entry : globalVariableValueMap.entrySet()) {
            String varName = entry.getKey();
            if (!varNameIdMap.containsKey(varName)) {
                continue;
            }
            tmpValueMap.put(varNameIdMap.get(varName), entry.getValue());
        }
        return tmpValueMap;
    };

    protected static final Function<EventContext, List<String>> defaultCandidateIntentIdListFunc = (eventContext) -> {
        return Optional.ofNullable(eventContext).map(EventContext::getCandidateIntentIdList).orElse(Collections.emptyList()).stream().sorted().collect(Collectors.toList());
    };

    public AbstractConditionPredicate(RobotRuntimeResource robotRuntimeResource, SessionContext sessionContext, EventContext eventContext) {
        this(() -> defaultVarIdValueFunc.apply(robotRuntimeResource, sessionContext),
                () -> defaultCandidateIntentIdListFunc.apply(eventContext));
    }

    AbstractConditionPredicate(Supplier<Map<String, String>> varIdValueMapSupplier,
                               Supplier<List<String>> candidateIntentIdListSupplier) {
        varIdValueMap.putAll(varIdValueMapSupplier.get());
        candidateIntentIdList = candidateIntentIdListSupplier.get();
    }


    private <PRE, POST> void matchLog(boolean matched, ConditionVarTypeEnum preVarType, PRE pre, ConditionEnum condition, ConditionVarTypeEnum postVarType, POST post) {
        String matchResult = matched ? "匹配成功": "匹配失败";
        if (ConditionEnum.onlySupportIntent(condition)) {
            log.debug("[{}]：[{}]{}{}", matchResult, preVarType, pre, condition);
        } if (ConditionEnum.onlySupportPreValue(condition)) {
            log.debug("[{}]：[{}]{}{}", matchResult, preVarType, pre, condition);
        } else {
            log.debug("[{}]：[{}][{}]{}{}{}", matchResult, preVarType, pre, condition, postVarType, post);
        }
    }

    protected boolean doTest(BaseConditionGroup baseAnswerContent) {
        if (baseAnswerContent == null) {
            return false;
        }
        List<List<ConditionExpressionPO>> conditionList = baseAnswerContent.getConditionList();

        if (CollectionUtils.isEmpty(conditionList)) {
            return true;
        }
        log.info("varIdValueMap:{}",varIdValueMap);
        log.info("candidateIntentIdList={}", candidateIntentIdList);
        return conditionList.stream().anyMatch(andConditionList -> andConditionList.stream().allMatch(this::testOneCondition));
    }

    protected boolean testOneCondition(ConditionExpressionPO andCondition) {
        // 意图特殊处理
        if (ConditionVarTypeEnum.INTENT.equals(andCondition.getPreVarType())) {
            List<String> intentIdList = andCondition.getIntentIdList();
            boolean result = false;
            // 全部命中
            if (ConditionEnum.ALL_HITS.equals(andCondition.getCondition())) {
                result = CollectionUtils.isNotEmpty(candidateIntentIdList) && new HashSet<>(candidateIntentIdList).containsAll(intentIdList);
            }
            // 全部不命中
            if (ConditionEnum.ALL_MISS.equals(andCondition.getCondition())) {
                result = CollectionUtils.isEmpty(candidateIntentIdList) || CollectionUtils.isEmpty(ListUtils.intersection(candidateIntentIdList, intentIdList));
            }
            // 任意命中
            if (ConditionEnum.ANY_HIT.equals(andCondition.getCondition())) {
                result = candidateIntentIdList.stream().anyMatch(intentIdList::contains);
            }
            // 任意不命中
            if (ConditionEnum.ANY_MISS.equals(andCondition.getCondition())) {
                result = candidateIntentIdList.stream().anyMatch(intentId -> !intentIdList.contains(intentId));
            }

            matchLog(result, andCondition.getPreVarType(), intentIdList, andCondition.getCondition(), andCondition.getPostVarType(), null);
            return result;
        }

        String pre = preValue(andCondition);
        List<String> postList = postValue(andCondition);
        ConditionEnum condition = andCondition.getCondition();
        boolean result;
        switch (condition) {
            case BLANK:
                result = StringUtils.isBlank(pre);
                break;
            case NOT_BLANK:
                result = StringUtils.isNotBlank(pre);
                break;
            case EQ:
                // pre == any post
                result = postList.stream().anyMatch(item -> StringUtils.equals(pre, item));
                break;
            case NEQ:
                // pre != all post
                result = postList.stream().noneMatch(item -> StringUtils.equals(pre, item));
                break;
            case GE:
                // pre >= all post
                result = postList.stream().allMatch(post -> NumberUtils.isCreatable(post) && NumberUtils.isCreatable(pre) &&
                        NumberUtils.createBigDecimal(pre).compareTo(NumberUtils.createBigDecimal(post)) >= 0);
                break;
            case LE:
                // pre <=  all post
                result = postList.stream().allMatch(post -> NumberUtils.isCreatable(post) && NumberUtils.isCreatable(pre) &&
                        NumberUtils.createBigDecimal(pre).compareTo(NumberUtils.createBigDecimal(post)) <= 0);
                break;
            case GT:
                // pre > all post
                result = postList.stream().allMatch(post -> NumberUtils.isCreatable(post) && NumberUtils.isCreatable(pre) &&
                        NumberUtils.createBigDecimal(pre).compareTo(NumberUtils.createBigDecimal(post)) > 0);
                break;
            case LT:
                // pre < all post
                result = postList.stream().allMatch(post -> NumberUtils.isCreatable(post) && NumberUtils.isCreatable(pre) &&
                        NumberUtils.createBigDecimal(pre).compareTo(NumberUtils.createBigDecimal(post)) < 0);
                break;
            case CT:
                // pre contains any post
                result = pre != null && postList.stream().anyMatch(pre::contains);
                break;
            case NCT:
                // pre not contains all post
                result = pre != null && postList.stream().noneMatch(pre::contains);
                break;
            default:
                result = false;
                break;
        }
        matchLog(result, andCondition.getPreVarType(), pre, andCondition.getCondition(), andCondition.getPostVarType(), postList);
        return result;
    }

    private String preValue(ConditionExpressionPO andCondition) {
        // todo
        if (isVarType(andCondition.getPreVarType())) {
            return varIdValueMap.get(andCondition.getPreVarId());
        }
        return null;
    }

    private boolean isVarType(ConditionVarTypeEnum conditionVarType) {
        return ConditionVarTypeEnum.CUSTOM.equals(conditionVarType) || ConditionVarTypeEnum.DYNAMIC.equals(conditionVarType);
    }

    private List<String> postValue(ConditionExpressionPO andCondition) {
        if (ConditionEnum.BLANK.equals(andCondition.getCondition()) || ConditionEnum.NOT_BLANK.equals(andCondition.getCondition())) {
            return Collections.singletonList(null);
        }
        if (isVarType(andCondition.getPostVarType())) {
            return Collections.singletonList(varIdValueMap.get(andCondition.getPostVarId()));
        }
        if (ConditionVarTypeEnum.CONSTANT.equals(andCondition.getPostVarType())) {
            return split(andCondition.getConstantStr());
        }
        return Collections.singletonList(null);
    }

    private List<String> split(String constant) {
        return Arrays.asList(constant.split("[,，]"));
    }
}
