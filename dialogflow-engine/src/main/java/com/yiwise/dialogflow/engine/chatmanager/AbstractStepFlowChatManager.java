package com.yiwise.dialogflow.engine.chatmanager;

import com.yiwise.dialogflow.engine.context.SessionContext;
import com.yiwise.dialogflow.engine.context.StepFlowContext;
import com.yiwise.dialogflow.engine.resource.RobotRuntimeResource;
import com.yiwise.dialogflow.engine.resource.StepRuntime;
import com.yiwise.dialogflow.engine.share.enums.ActiveTypeEnum;
import com.yiwise.dialogflow.engine.share.response.ActiveManagerInfo;
import com.yiwise.dialogflow.entity.enums.StepSubTypeEnum;
import lombok.Getter;

public abstract class AbstractStepFlowChatManager extends AbstractContextChatManager {

    @Getter
    protected final String stepId;

    @Getter
    protected final String stepName;

    @Getter
    protected final StepRuntime step;

    @Getter
    protected final ActiveManagerInfo activeManagerInfo;

    public AbstractStepFlowChatManager(RobotRuntimeResource resource, StepRuntime stepRuntime) {
        super(resource);
        this.step = stepRuntime;
        this.stepId = stepRuntime.getId();
        this.stepName = step.getName();
        ActiveManagerInfo info = new ActiveManagerInfo();
        info.setActiveType(ActiveTypeEnum.STEP);
        info.setChatManagerName(getName());
        info.setOriginId(stepId);
        info.setOriginName(stepName);
        info.setOriginLabel(step.getLabel());
        info.setLLM(StepSubTypeEnum.isLlm(step.getSubType()));
        this.activeManagerInfo = info;
    }

    @Override
    public ActiveManagerInfo getChatManagerInfo(SessionContext sessionContext) {
        return activeManagerInfo;
    }

    protected StepFlowContext getStepFlowContext(SessionContext sessionContext) {
        StepFlowContext context = sessionContext.getStepFlowContextMap().get(stepId);
        if (context == null) {
            initContext(sessionContext);
            context = sessionContext.getStepFlowContextMap().get(stepId);
        }
        return context;
    }

}
