import com.yiwise.dialogflow.pattern.PatternEnhance;
import com.yiwise.dialogflow.pattern.PatternEnhanceCache;

import java.io.*;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class PredictPatternTest {
    private static Boolean calculateRegexCanUseContains(String regex) {
        // 判断表达式中没有任何正则表达式中的字符, 即可
        String patternSymbol = ".*?[]{}(|)<>\\^$+";
        String[] symbols = patternSymbol.split("");
        return Stream.of(symbols).noneMatch(regex::contains);
    }

    /**
     * 计算正则表达式最少需要的输入长度, 用来解决用户输入内容很短, 但是正则很长, 明显不会匹配的情况, 就不用正则匹配了
     * 比如用户输入的是[嗯], 对于正则表达式[.*可以的.*] 就肯定不会命中, 因为可以的长度是3, 用户输入的长度是1
     */
    private static Integer calculateRegexRequireInputLength(String regex) {
        return 1;
    }

    public static void main(String[] args) throws IOException {

//        String regex = "^[^不|别]*加.*微信是吗(?!.*(算了|不用|不加))";
//        String regex = "^[^不|别]加微信是吗";
//        String regex = "哪些.{0,5}(折上九折|折上(折上九折|折上9折))";
//        String regex = "这几天.{0,4}才[买下]";
//        String regex = "(?=.*号码)(?=.*得到).*$";
//        String regex = "(你好|可以|的).{0,4}(什么|吗|哈哈|盒盒盒)";
//        PatternEnhance patternEnhance = new PatternEnhance(regex, Pattern.compile(regex));

//        System.out.println(patternEnhance);

//        bbb();

        abd();
    }

    private static void abd() throws FileNotFoundException {
        String path = "/Users/<USER>/Downloads/666.log";
        File file = new File(path);
        BufferedInputStream reader = new BufferedInputStream(new FileInputStream(file));
        BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(reader));

        List<PatternEnhance> patternEnhanceList = new ArrayList<>();
        bufferedReader.lines().forEach(line -> {
            line = line.trim();
            PatternEnhance patternEnhance = PatternEnhanceCache.getOrCreate(line, Pattern.compile(line), false);
            if (patternEnhance.getRequireInputLength() > 0) {
                patternEnhanceList.add(patternEnhance);
            } else {
                patternEnhanceList.add(patternEnhance);
            }
        });

        List<String> inputList = Arrays.asList("onnected service for enders. Your call can not be connected. Is busy, you please sist. Please connect service, please dial again. Your call can not be connected. Is the usual exist. Please connected service, please dial again. Your call cannot be connected. Is busy consist is. Please connect service, please dial again. Your call can be connected. Is busy, please request. Please connect, subscribe. Your call can not be connected. Is busy, please request id. Please connect service, please dial. Your call cannot be connected. Is the subpost st. Please connected service, please dial again.onnected service for enders. Your call can not be connected. Is busy, you please sist. Please connect service, please dial again. Your call can not be connected. Is the usual exist. Please connected service, please dial again. Your call cannot be connected. Is busy consist is. Please connect service, please dial again. Your call can be connected. Is busy, please request. Please connect, subscribe. Your call can not be connected. Is busy, please request id. Please connect service, please dial. Your call cannot be connected. Is the subpost st. Please connected service, please dial again.onnected service for enders. Your call can not be connected. Is busy, you please sist. Please connect service, please dial again. Your call can not be connected. Is the usual exist. Please connected service, please dial again. Your call cannot be connected. Is busy consist is. Please connect service, please dial again. Your call can be connected. Is busy, please request. Please connect, subscribe. Your call can not be connected. Is busy, please request id. Please connect service, please dial. Your call cannot be connected. Is the subpost st. Please connected service, please dial again.onnected service for enders. Your call can not be connected. Is busy, you please sist. Please connect service, please dial again. Your call can not be connected. Is the usual exist. Please connected service, please dial again. Your call cannot be connected. Is busy consist is. Please connect service, please dial again. Your call can be connected. Is busy, please request. Please connect, subscribe. Your call can not be connected. Is busy, please request id. Please connect service, please dial. Your call cannot be connected. Is the subpost st. Please connected service, please dial again.");

        for (int i = 0; i < 1000; i++) {
            jfiew(patternEnhanceList, inputList);
        }

    }

    private static void jfiew(List<PatternEnhance> patternEnhanceList, List<String> inputList) {
        long start = System.nanoTime();
        AtomicInteger count = new AtomicInteger();
        for (PatternEnhance patternEnhance : patternEnhanceList) {
            for (String s : inputList) {
                patternEnhance.find(s, count);
            }
        }
        long end = System.nanoTime();
        System.out.println("use: " + (end - start) + " ns, count: " + count.get());
    }

    private static void bbb() {
        List<String> regexList = Arrays.asList("(?=.*号码)(?=.*得到).*$",
                "(?=.*号码)(?=.*得来).*$",
                "(?=.*号码)(?=.*哪来).*$",
                "(?=.*积分)(?=.*有效期).*$",
                "(?=.*积分)(?=.*期限).*$",
                "找<ta>.?什么事",
                "找<ta>干什么",
                "找<ta>干嘛",
                "找<ta>.?啥事",
                "找<ta>.?事吗",
                "<ta>.?开会",
                "<ta>不在",
                "<ta>没.?带手机",
                "<ta>没.?拿手机",
                "^嗯+$",
                "[7七][是对吧么吗]+");

        List<PatternEnhance> patternEnhanceList = regexList.stream()
                .map(regex -> PatternEnhanceCache.getOrCreate(regex, Pattern.compile(regex), false))
                .collect(Collectors.toList());

        String text = "onnected service for enders. Your call can not be connected. Is busy, you please sist. Please connect service, please dial again. Your call can not be connected. Is the usual exist. Please connected service, please dial again. Your call cannot be connected. Is busy consist is. Please connect service, please dial again. Your call can be connected. Is busy, please request. Please connect, subscribe. Your call can not be connected. Is busy, please request id. Please connect service, please dial. Your call cannot be connected. Is the subpost st. Please connected service, please dial again.onnected service for enders. Your call can not be connected. Is busy, you please sist. Please connect service, please dial again. Your call can not be connected. Is the usual exist. Please connected service, please dial again. Your call cannot be connected. Is busy consist is. Please connect service, please dial again. Your call can be connected. Is busy, please request. Please connect, subscribe. Your call can not be connected. Is busy, please request id. Please connect service, please dial. Your call cannot be connected. Is the subpost st. Please connected service, please dial again.onnected service for enders. Your call can not be connected. Is busy, you please sist. Please connect service, please dial again. Your call can not be connected. Is the usual exist. Please connected service, please dial again. Your call cannot be connected. Is busy consist is. Please connect service, please dial again. Your call can be connected. Is busy, please request. Please connect, subscribe. Your call can not be connected. Is busy, please request id. Please connect service, please dial. Your call cannot be connected. Is the subpost st. Please connected service, please dial again.onnected service for enders. Your call can not be connected. Is busy, you please sist. Please connect service, please dial again. Your call can not be connected. Is the usual exist. Please connected service, please dial again. Your call cannot be connected. Is busy consist is. Please connect service, please dial again. Your call can be connected. Is busy, please request. Please connect, subscribe. Your call can not be connected. Is busy, please request id. Please connect service, please dial. Your call cannot be connected. Is the subpost st. Please connected service, please dial again.";


        for (int i = 0; i < 100; i++) {
            bbb1(patternEnhanceList, text);
        }

    }

    private static void bbb1(List<PatternEnhance> patternEnhanceList, String text) {
        AtomicInteger total = new AtomicInteger();
        long start = System.nanoTime();
        for (int i = 0; i < 1; i++) {
            for (int i1 = 0; i1 < patternEnhanceList.size(); i1++) {
                PatternEnhance patternEnhance = patternEnhanceList.get(i1);
                patternEnhance.find(text, total);
            }
        }
        long end = System.nanoTime();
        System.out.println("total: " + total.get() + ", use: " + (end - start) + " ns");
    }

    private static void aaa() throws FileNotFoundException {
        String filePath = "/Users/<USER>/Downloads/getPatternCacheUseInfo.json";

        File file = new File(filePath);
        BufferedInputStream reader = new BufferedInputStream(new FileInputStream(file));
        BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(reader));
        AtomicInteger total = new AtomicInteger();
        AtomicInteger fail = new AtomicInteger();
        List<PatternEnhance> patternEnhanceList = new ArrayList<>();
        bufferedReader.lines().forEach(line -> {
            line = line.split(":")[0];
            line = line.substring(1, line.length() - 1);
            total.incrementAndGet();
            try {
                PatternEnhance patternEnhance = PatternEnhanceCache.getOrCreate(line, Pattern.compile(line), false);
                if (patternEnhance.getRequireInputLength() < 1) {
                    fail.incrementAndGet();

                }
                patternEnhanceList.add(patternEnhance);

                System.out.println(patternEnhance);
                System.out.println();
            } catch (Exception e) {}

        });

        patternEnhanceList.sort(Comparator.comparingInt(PatternEnhance::getRequireInputLength).reversed());
        // 记录不同长度在数据中的开始下标
        int maxLength = 100;
        int[] requireInputLengthIndex = new int[maxLength];

        for (int i = 0; i < patternEnhanceList.size(); i++) {
            PatternEnhance enhance = patternEnhanceList.get(i);
            if (enhance.getRequireInputLength() < maxLength) {
                int index = requireInputLengthIndex[enhance.getRequireInputLength()];
                if (index == 0) {
                    requireInputLengthIndex[enhance.getRequireInputLength()] = i;
                }
            }
        }

        System.out.println("total: " + total.get());
        System.out.println("fail: " + fail.get());
        System.out.println(Arrays.toString(requireInputLengthIndex));
    }

    public static void test() {
        String regex = "^[^(不|没)]*我知道怎么操作[^(不|没)]*";
        String userInput = "计算正则表达式最少需要的输入长度, 用来解决用户输入内容很短可以,我知道怎么操作的其他可以的 但是正则很长,费用 明显不会匹配的情况, 就不用正则匹配了";
        int count = 2000000;
        for (int i = 0; i < 100; i++) {
            matches(String.format(".*%s.*", regex), userInput, count);
            matches(String.format(".*(%s).*", regex), userInput, count);
            find(String.format("%s", regex), userInput, count);
            find(String.format("(%s)", regex), userInput, count);
            contains(String.format("(%s)", regex), userInput, count);
        }
    }

    private static void matches(String regex, String userInput, int count) {
        long start = System.currentTimeMillis();
        Pattern pattern = Pattern.compile(regex);
        int matchCount = 0;
        for (int i = 0; i < count; i++) {
            if (pattern.matcher(userInput).matches()) {
                matchCount++;
            }
        }
        long end = System.currentTimeMillis();
        System.out.println( regex + " matches use: " +( end - start) + " ms" + ", matchCount: " + matchCount);
    }

    private static void find(String regex, String userInput, int count) {
        long start = System.currentTimeMillis();
        Pattern pattern = Pattern.compile(regex);
        int matchCount = 0;
        for (int i = 0; i < count; i++) {
            if (pattern.matcher(userInput).find()) {
                matchCount++;
            }
        }
        long end = System.currentTimeMillis();
        System.out.println( regex + "   find use: " +( end - start) + " ms" + ", matchCount: " + matchCount);
    }
    private static void contains(String regex, String userInput, int count) {
        long start = System.currentTimeMillis();
        Pattern pattern = Pattern.compile(regex);
        int matchCount = 0;
        for (int i = 0; i < count; i++) {
            if (userInput.contains(regex)) {
                matchCount++;
            }
        }
        long end = System.currentTimeMillis();
        System.out.println( regex + "   contains use: " +( end - start) + " ms" + ", matchCount: " + matchCount);
    }
}
