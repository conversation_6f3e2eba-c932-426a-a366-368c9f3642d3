package com.yiwise.dialogflow.common;

import org.jctools.queues.MpscArrayQueue;

import java.util.Collection;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.locks.Condition;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.LockSupport;
import java.util.concurrent.locks.ReentrantLock;

public class MpscArrayBlockingQueue<E> extends MpscArrayQueue<E> implements BlockingQueue<E> {

    public static final WaitStrategy PROGRESSIVE = (idleCounter, nanos) -> {
        if (idleCounter > 500) {
            LockSupport.parkNanos(1);
        } else if (idleCounter > 300) {
            Thread.yield();
        }
        return idleCounter + 1;
    };

    private final WaitStrategy producerWaitStrategy;
    private final ConsumerWaitStrategy consumerWaitStrategy;

    public MpscArrayBlockingQueue(int capacity, WaitStrategy producerWaitStrategy) {
        super(capacity);
        this.producerWaitStrategy = producerWaitStrategy;
        this.consumerWaitStrategy = new ConsumerWaitStrategy();
    }

    public int drainTo(Collection<? super E> c) {
        return this.drainTo(c, this.capacity());
    }

    public int drainTo(final Collection<? super E> c, int maxElements) {
        return this.drain(c::add, maxElements);
    }

    public boolean offer(E e, long timeout, TimeUnit unit) throws InterruptedException {
        int idleCounter = 0;
        long timeoutNanos = System.nanoTime() + unit.toNanos(timeout);

        while(!this.offer(e)) {
            long left = timeoutNanos - System.nanoTime();
            if (left <= 0L) {
                return false;
            }

            idleCounter = this.producerWaitStrategy.idle(idleCounter, left);
            if (Thread.interrupted()) {
                throw new InterruptedException();
            }
        }
        this.consumerWaitStrategy.wakeup();
        return true;
    }

    public E poll(long timeout, TimeUnit unit) throws InterruptedException {
        int idleCounter = 0;
        long timeoutNanos = System.nanoTime() + unit.toNanos(timeout);

        do {
            E result = this.poll();
            if (result != null) {
                return result;
            }

            long left = timeoutNanos - System.nanoTime();
            if (left <= 0L) {
                return null;
            }
            idleCounter = this.consumerWaitStrategy.idle(idleCounter, left);
        } while(!Thread.interrupted());

        throw new InterruptedException();
    }

    public void put(E e) throws InterruptedException {
        int idleCounter = 0;
        while(!this.offer(e)) {
            idleCounter = this.producerWaitStrategy.idle(idleCounter, -1);
            if (Thread.interrupted()) {
                throw new InterruptedException();
            }
        }
        this.consumerWaitStrategy.wakeup();
    }

    public boolean offer(E e) {
        boolean success = this.offerIfBelowThreshold(e, this.capacity() - 32);
        if (success) {
            this.consumerWaitStrategy.wakeup();
        }
        return success;
    }

    public int remainingCapacity() {
        return this.capacity() - this.size();
    }

    public E take() throws InterruptedException {
        int idleCounter = 100;

        do {
            E result = this.relaxedPoll();
            if (result != null) {
                return result;
            }

            idleCounter = this.consumerWaitStrategy.idle(idleCounter, -1);
        } while(!Thread.interrupted());

        throw new InterruptedException();
    }

    static class ConsumerWaitStrategy implements WaitStrategy {

        final AtomicBoolean waiting = new AtomicBoolean(false);

        final Lock lock = new ReentrantLock(true);
        final Condition notEmpty = lock.newCondition();
        @Override
        public int idle(int idleCounter, long leftNanoTime) throws InterruptedException {

            if (idleCounter < 100) {
                return idleCounter + 1;
            }
            if (idleCounter < 200) {
                Thread.yield();
                return idleCounter + 1;
            }

            lock.lock();
            try {
                waiting.set(true);
                if (leftNanoTime >= 0) {
                    notEmpty.awaitNanos(leftNanoTime);
                } else {
                    notEmpty.await();
                }
                return idleCounter + 1;
            } finally {
                lock.unlock();
            }
        }

        public void wakeup() {
            if (waiting.compareAndSet(true, false)) {
                lock.lock();
                try {
                    notEmpty.signalAll();
                } finally {
                    lock.unlock();
                }
            }
        }
    }

    public static interface WaitStrategy {
        int idle(int idleCounter, long leftNanoTime) throws InterruptedException;
    }
}