package com.yiwise.dialogflow.utils;

import ch.qos.logback.classic.LoggerContext;
import ch.qos.logback.core.Appender;
import ch.qos.logback.core.OutputStreamAppender;
import com.yiwise.dialogflow.logback.MyAsyncAppender;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.ILoggerFactory;
import org.slf4j.LoggerFactory;

@Slf4j
public class LogUtils {

    public static void setLoggerImmediateFlush(boolean immediateFlush, String asyncAppenderName, String rollingAppenderName) {
        log.info("setLoggerImmediateFlush, asyncAppenderName: {}, rollingAppenderName: {}, immediateFlush: {}", asyncAppenderName, rollingAppenderName, immediateFlush);
        try {
            ILoggerFactory loggerFactory = LoggerFactory.getILoggerFactory();
            if (loggerFactory instanceof LoggerContext) {
                LoggerContext context = (LoggerContext) loggerFactory;
                Appender asyncAppender = context.getLogger("ROOT").getAppender(asyncAppenderName);
                if (asyncAppender instanceof MyAsyncAppender) {
                    Appender outputStreamAppender = ((MyAsyncAppender)asyncAppender).getAppender(rollingAppenderName);
                    if (outputStreamAppender instanceof OutputStreamAppender) {
                        ((OutputStreamAppender)outputStreamAppender).setImmediateFlush(immediateFlush);
                    }
                }
            }
        } catch (Exception e) {
            log.error("setLoggerImmediateFlush error", e);
        }
    }
}
