package com.yiwise.dialogflow.api;

import com.yiwise.dialogflow.api.dto.request.IntentLevelTagListRequest;
import com.yiwise.dialogflow.api.dto.response.SimpleIntentLevelTag;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 意向等级标签
 * todo 通过ai-call-back 项目直接提供接口
 */
@RequestMapping({"/apiBot/v3/intentLevelTag"})
public interface IntentLevelTagApi {

    /**
     * 查询意向等级标签详情
     * @param intentLevelTagId 意向等级标签id
     * @return 意向标签等级
     */
    @GetMapping("getSimpleDetailInfo")
    SimpleIntentLevelTag getSimpleDetailInfo(@RequestParam("intentLevelTagId") Long intentLevelTagId);

    /**
     * 根据 dialogFlowId 查询意向等级标签详情
     * @param dialogFlowId dialogFlowId
     * @return 意向标签等级
     */
    @GetMapping("getSimpleDetailInfoByDialogFlowId")
    SimpleIntentLevelTag getSimpleDetailInfoByDialogFlowId(@RequestParam("dialogFlowId") Long dialogFlowId);

    /**
     * 根据条件查询意向等级标签列表
     * @param request 意向等级查询条件
     * @return 意向等级标签列表
     */
    @PostMapping("getSimpleDetailListByCondition")
    List<SimpleIntentLevelTag> getSimpleDetailListByIntentLevelTagIdList(@RequestBody IntentLevelTagListRequest request);
}