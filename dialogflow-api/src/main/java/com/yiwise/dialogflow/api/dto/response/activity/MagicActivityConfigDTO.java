package com.yiwise.dialogflow.api.dto.response.activity;

import com.yiwise.dialogflow.api.dto.response.TtsVoiceConfigDTO;
import lombok.Data;

import java.util.Map;

@Data
public class MagicActivityConfigDTO {

    private String id;

    /**
     * botId
     */
    private Long botId;

    /**
     * 活动 id
     */
    private Long activityId;

    /**
     * 活动关联的新版外呼任务 id
     */
    private Long callJobId;

    /**
     * 租户 id
     */
    private Long tenantId;

    /**
     * 模板变量值 map, key 变量名, value: 变量值
     */
    private Map<String, String> templateVarValueMap;

    /**
     * tts配置
     */
    private TtsVoiceConfigDTO ttsConfig;
}
