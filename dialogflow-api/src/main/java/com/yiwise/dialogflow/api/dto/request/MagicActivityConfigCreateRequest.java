package com.yiwise.dialogflow.api.dto.request;


import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Map;

@Data
@EqualsAndHashCode
public class MagicActivityConfigCreateRequest {

    /**
     * 模板 id
     */
    Long botTemplateId;

    /**
     * 用户 id
     */
    Long userId;

    /**
     * 租户 id
     */
    Long tenantId;

    /**
     * 任务 id
     */
    Long callJobId;

    /**
     * 活动id
     */
    Long activityId;

    /**
     * 模板变量键值对, key: 模板变量名称, value: 模板变量值
     */
    Map<String, String> templateVariableMap;

    /**
     * tts 音色
     */
    String ttsVoice;

    /**
     * 客户选中的可选变量名称列表
     */
    List<String> selectedVariableNameList;
}
