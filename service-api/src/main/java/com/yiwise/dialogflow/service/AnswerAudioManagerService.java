package com.yiwise.dialogflow.service;

import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.dialogflow.api.dto.response.audio.AudioUploadResult;
import com.yiwise.dialogflow.engine.share.response.AnswerLocateBO;
import com.yiwise.dialogflow.api.enums.AudioTypeEnum;
import com.yiwise.dialogflow.entity.enums.SyncModeEnum;
import com.yiwise.dialogflow.entity.po.*;
import com.yiwise.dialogflow.entity.vo.SimpleUploadResultVO;
import com.yiwise.dialogflow.entity.vo.audio.*;
import com.yiwise.dialogflow.entity.vo.audio.request.AnswerAudioRequestVO;
import com.yiwise.dialogflow.entity.vo.audio.request.AnswerUpdateAudioRequestVO;
import com.yiwise.dialogflow.entity.vo.audio.request.BatchAdjustVolumeRequestVO;
import com.yiwise.dialogflow.entity.vo.audio.request.SyncAudioRequestVO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 答案录音管理, 负责录音的处理, 等
 * <AUTHOR>
 */
public interface AnswerAudioManagerService {

    Optional<LocalDateTime> getLastAudioCreateTime(Long botId);

    PageResultObject<AnswerAudioWrapVO> queryByCondition(AnswerAudioRequestVO request);

    List<AnswerAudioWrapVO> queryByNodeId(Long botId, String stepId, String nodeId);

    List<AnswerAudioWrapVO> queryByKnowledgeId(Long botId, String knowledgeId);

    List<AnswerAudioWrapVO> queryBySpecialAnswerId(Long botId, String specialAnswerId);

    AnswerAudioWrapVO wrapSingleAnswerAudio(Long botId, AnswerLocateBO answerLocateBO);

    List<AnswerAudioWrapVO> getAllByBotId(Long botId);

    List<AnswerAudioWrapVO> getByRobotSnapshot(Long botId, RobotSnapshotPO snapshot);

    TtsJobPO startCompose(Long botId, Long userId);

    AudioCompleteProgressVO getAudioCompleteProgress(Long botId);

    void clearAudioCompleteProgressCache(Long botId);

    /**
     * 把录音文件存入当前录音师的录音映射文件下面
     * 这里面其实也会存在一个问题, 就是录音师切换了, 但是已经完成的录音不想切换
     * 这里目前先对齐目前1.0的逻辑, 在录音师切换的时候, 先把录音复制出来一份
     * @param request 参数
     * @return 上传
     */
    void updateAnswerAudio(AnswerUpdateAudioRequestVO request, Long userId);

    /**
     * 机器人录音师变更
     * 在变更时把原有的录音复制到当前的录音师下面
     * @param botId 机器人id
     * @param oldUserId 原录音师id
     * @param newUserId 最新录音师id
     */
    void copyAllToOtherRecorder(Long botId, Long oldUserId, Long newUserId);

    Integer copyAllOnCopyBot(Long fromBotId, Long fromUserId, Long toBotId, Long toUserId, Long operationUserId, List<String> filterAnswerIdList, SyncModeEnum syncMode);

    AudioUploadResultVO adjustVolume(Long botId, String url, Integer volume);

    AudioBatchUploadResultVO batchUploadAudio(Long botId, MultipartFile file, Long userId);

    void importAudio(Long botId, Long userId, String audioDirPath);

    SimpleUploadResultVO downloadAudioZip(Long botId);

    /**
     * 下载所有音频文件到指定目录
     */
    void downloadAllAudioToDirPath(Long botId, String dirPath);

    TtsComposeProgressVO getTtsCompleteProgress(Long botId);

    Integer copyAudio(Long fromBotId, Long toBotId, Long operationUserId);

    void batchSyncAudio(BatchSyncAudioRequestVO request, Long userId);

    Boolean copyAudioByAnswerSource(Long fromBotId, Long toBotId, List<? extends Labelled> list, Long userId);

    /**
     * 重置所有录音师录制的录音
     */
    void resetAllManMadeAudio(Long botId, Long userId);

    void resetByAnswer(Long botId, List<String> answerTextList, Long userId);

    AnswerAudioWrapVO convertAnswer(Map<String, AnswerAudioMappingVO> textAudioStorageMap,
                                    AnswerLocateBO locate,
                                    BaseAnswerContent answer,
                                    AudioTypeEnum audioType);

    AnswerAudioWrapVO convertAnswer(Map<String, AnswerAudioMappingVO> textAudioStorageMap,
                                    StepPO step,
                                    DialogBaseNodePO node,
                                    BaseAnswerContent answer,
                                    AudioTypeEnum audioType);

    void downloadSingleAudio(HttpServletResponse response, String ossKey, String label);

    SimpleUploadResultVO downloadZip(Long botId, List<AnswerAudioWrapVO> allAudioList);

    String batchAdjustVolume(BatchAdjustVolumeRequestVO request, Long userId);

    /**
     * 同步音频到公共音频库
     */
    void sync(SyncAudioRequestVO request, Long userId);

    AudioUploadResult uploadAndUpdate(Long dialogFlowId, String text, String ossKey, Long userId);
}
