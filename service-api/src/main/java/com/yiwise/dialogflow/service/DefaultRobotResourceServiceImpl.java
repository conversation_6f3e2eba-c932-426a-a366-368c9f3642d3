package com.yiwise.dialogflow.service;

import com.google.common.collect.Lists;
import com.yiwise.dialogflow.entity.context.RobotResourceContext;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/6/13
 */
@Service
public class DefaultRobotResourceServiceImpl implements RobotResourceService {

    @Override
    public void saveToSnapshot(RobotResourceContext context) {

    }

    @Override
    public void validateResource(RobotResourceContext context) {

    }

    @Override
    public void loadFromSnapshot(RobotResourceContext context) {

    }

    @Override
    public List<Class<? extends RobotResourceService>> dependsOn() {
        return Lists.newArrayList();
    }
}
