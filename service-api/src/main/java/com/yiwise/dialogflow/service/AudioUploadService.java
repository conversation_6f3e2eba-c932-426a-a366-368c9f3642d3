package com.yiwise.dialogflow.service;

import com.yiwise.dialogflow.entity.vo.audio.AudioUploadResultVO;
import com.yiwise.dialogflow.entity.vo.audio.AudioBatchUploadResultVO;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface AudioUploadService {
    /**
     * 上传录音文件
     * @param botId bogId
     * @param file 文件
     * @return
     */
    AudioUploadResultVO uploadAudio(Long botId, MultipartFile file, Boolean autoCut);
    AudioUploadResultVO uploadAudio(Long botId, File file, Boolean autoCut);

    AudioUploadResultVO uploadAndGenerateWaveform(Long botId, MultipartFile file, Boolean autoCut);

    AudioUploadResultVO generateWaveform(String key);

    AudioUploadResultVO validAndUpload(Long botId, File localTmpFile, String originFileName, Boolean autoCut,
                                       Boolean generateWaveform);

    AudioUploadResultVO cutAudio(Long botId, String audioUrl, Double startTimeIndex, Double endTimeIndex);

    /**
     * 调整目标音频文件的音量大小
     * @param botId
     * @param url
     * @param expectVolume
     * @return
     */
    AudioUploadResultVO adjustVolume(Long botId, String url, Integer expectVolume);

    AudioUploadResultVO adjustByMultiple(Long botId, String url, double multi);

    Map<String, AudioUploadResultVO> batchUploadAudio(Long botId, MultipartFile file, Map<String, String> answerLabel2ContentMap, AudioBatchUploadResultVO batchResult);
    Map<String, AudioUploadResultVO> batchUploadAudio(Long botId, String localDirPath, Map<String, String> answerLabel2ContentMap, AudioBatchUploadResultVO batchResult);

    void downAudioToLocal(Long botId, String parentPath, Map<String, String> fileName2UrlMap);

    File downloadRemoteAudio(String ossKey, String label);

    void uploadAndSave(Long botId, String text, File localFile, Integer samplesPerSec, Long recordUserId);
}
