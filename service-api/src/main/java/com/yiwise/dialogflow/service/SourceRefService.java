package com.yiwise.dialogflow.service;

import com.yiwise.dialogflow.entity.bo.SourceRefBO;
import com.yiwise.dialogflow.entity.enums.IntentRefTypeEnum;
import com.yiwise.dialogflow.entity.enums.SourceTypeEnum;
import com.yiwise.dialogflow.entity.po.SourceRefPO;
import com.yiwise.middleware.mysql.service.BasicService;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR> @ yiwise . com>
 * @version v0.1 2022/7/20
 * @class <code>SourceRefService</code>
 * @see
 * @since JDK1.8
 */
public interface SourceRefService extends BasicService<SourceRefPO> {

    /**
     * 根据父关联id删除关联信息
     *
     * @param parentRefId
     * @param parentRefType
     */
    void deleteSourceByParentRefId(Long botId, String parentRefId, IntentRefTypeEnum parentRefType);

    void deleteSourceByParentRefIdList(Long botId, List<String> parentRefIdList, IntentRefTypeEnum parentRefType);

    /**
     * 新增关联信息
     */
    SourceRefPO addSourceRef(SourceRefPO sourceRefPO);

    /**
     * 保存关联信息
     *
     * @param sourceRefBO
     */
    void saveSourceRef(SourceRefBO sourceRefBO);

    /**
     * 根据refId删除关联信息
     *
     * @param botId
     * @param id
     */
    void deleteSourceByRefId(Long botId, String id);

    /**
     * 批量删除关联信息
     *
     * @param botId botId
     * @param refIdList refIdList
     */
    void deleteSourceByRefIds(Long botId, List<String> refIdList);


    void deleteSourceByRefIds(Long botId, List<String> refIdList, SourceTypeEnum sourceType);



    /**
     * 批量删除
     *
     * @param refIdList
     * @param refType
     */
    void deleteSourceByRefIdList(Long botId, List<String> refIdList, IntentRefTypeEnum refType);

    /**
     * 根据关联id获取意图
     *
     * @param refIdList
     * @param refType
     * @return
     */
    List<SourceRefPO> getIntentByRefIdList(Long botId, List<String> refIdList, IntentRefTypeEnum refType);

    /**
     * 根据sourceId和type获取关联知识
     *
     * @param sourceId
     * @param sourceType
     * @return
     */
    List<SourceRefPO> getBySourceIdList(Long botId, List<String> sourceId, SourceTypeEnum sourceType);

    /**
     * 获取所有引用信息
     *
     * @param botId
     * @param sourceType
     * @return
     */
    List<SourceRefPO> getListBySourceType(Long botId, SourceTypeEnum sourceType);

    List<SourceRefPO> getListBySourceTypeAndRefType(Long botId, SourceTypeEnum sourceType, IntentRefTypeEnum refType);

    void deleteBySourceIdList(Long botId, Collection<String> sourceIds, IntentRefTypeEnum refType);

    void batchAddSourceRef(List<SourceRefPO> handleList);

    void deleteByRefType(Long botId, IntentRefTypeEnum refType);

    void deleteSourceByRefIdAndSourceTypeAndRefType(Long botId, String refId, SourceTypeEnum sourceType, IntentRefTypeEnum refType);

    List<SourceRefPO> getVariableRefListByBotIdList(List<Long> botIdList);

    void updateRefLabelByRefId(Long botId, String variableId, String newVariableName);
}
