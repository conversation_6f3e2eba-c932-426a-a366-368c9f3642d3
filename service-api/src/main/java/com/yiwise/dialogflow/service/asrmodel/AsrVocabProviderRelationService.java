package com.yiwise.dialogflow.service.asrmodel;

import com.yiwise.dialogflow.engine.share.enums.AsrProviderEnum;
import com.yiwise.dialogflow.entity.po.asrmodel.AsrVocabProviderRelationPO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/1 15:41:38
 */
public interface AsrVocabProviderRelationService {
    List<AsrVocabProviderRelationPO> getByAsrVocabId(Long asrVocabId);

    AsrVocabProviderRelationPO getByAsrVocabIdAndProvider(Long asrVocabId, AsrProviderEnum provider);

    void delete(AsrVocabProviderRelationPO asrVocabProviderRelationPO);

    void add(AsrVocabProviderRelationPO asrVocabProviderRelationPO);
}
