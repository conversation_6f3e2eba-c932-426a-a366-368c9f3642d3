package com.yiwise.dialogflow.service;

import com.yiwise.base.model.enums.EnabledStatusEnum;
import com.yiwise.dialogflow.api.dto.response.specialAnswerConfig.SimpleSpecialAnswerConfig;
import com.yiwise.dialogflow.entity.po.SpecialAnswerConfigPO;
import com.yiwise.dialogflow.entity.query.SpecialAnswerConfigQuery;
import com.yiwise.dialogflow.entity.vo.SpecialAnswerConfigVO;
import com.yiwise.dialogflow.entity.vo.sync.BotSyncResultVO;
import com.yiwise.dialogflow.entity.vo.sync.SpecialAnswerSyncVO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface SpecialAnswerConfigService {

    List<SpecialAnswerConfigPO> initOnCreateBot(Long botId);

    SpecialAnswerConfigPO update(SpecialAnswerConfigPO config, Long userId);

    void updateActionList(SpecialAnswerConfigPO config);

    void updateEnableStats(Long botId, String id, EnabledStatusEnum state, Long userId);

    List<SpecialAnswerConfigPO> getByBotId(Long botId);

    List<SpecialAnswerConfigPO> getByBotId(Long botId, EnabledStatusEnum enabledStatus);

    List<SpecialAnswerConfigVO> queryByCondition(SpecialAnswerConfigQuery condition);

    void updateDependVariableName(Long botId, String variableId, String oldVariableName, String newVariableName);

    Map<String, String> getNameByIdList(List<String> idList);

    String getNameById(String id);

    /**
     * 添加新的特殊语境了, 同步给旧的机器人
     */
    void maintainAllBot();

    void maintainByBotId(Long botId);

    SpecialAnswerConfigPO getById(Long botId, String specialAnswerConfigId);

    SpecialAnswerConfigVO getVODetailById(Long botId, String id);

    void updateAnswerList(SpecialAnswerConfigPO specialAnswer);

    List<SpecialAnswerConfigPO> getAllAnswerList();

    BotSyncResultVO sync(SpecialAnswerSyncVO syncVO);

    void resetAllSpecialAnswerLabel(Long newBotId);

    void resetResourceReferenceInfo(Long newBotId);

    void updateAnswerListAndVariableRefInfo(Long botId, List<SpecialAnswerConfigPO> updateList, Long userId);

    List<SimpleSpecialAnswerConfig> getSimpleListByDialogFlowId(Long dialogFlowId);

    void updateBySimpleAnswerConfig(SimpleSpecialAnswerConfig simpleSpecialAnswerConfig, Long userId);

    /**
     * 判断是否开启大模型兜底对话
     */
    boolean checkEnableLLMChat(Long botId);

    boolean checkEnableLLMChat(List<SpecialAnswerConfigPO> specialAnswerConfigList);


}
