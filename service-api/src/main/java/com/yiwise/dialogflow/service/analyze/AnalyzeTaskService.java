package com.yiwise.dialogflow.service.analyze;

import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.dialogflow.entity.po.analyze.AnalyzeTaskPO;
import com.yiwise.dialogflow.entity.po.semantic.analysis.SemanticAnalysisConditionPO;
import com.yiwise.dialogflow.entity.vo.analyze.*;

/**
 * <AUTHOR>
 */
public interface AnalyzeTaskService {

    /**
     * 创建任务
     *
     * @param request form
     * @param userId  当前用户id
     */
    void create(AnalyzeTaskCreateVO request, Long userId);

    /**
     * 修改任务
     *
     * @param request form
     * @param userId  当前用户id
     */
    void update(AnalyzeTaskUpdateVO request, Long userId);

    /**
     * 删除任务
     *
     * @param request form
     */
    void delete(AnalyzeTaskDeleteVO request);

    /**
     * 分页查询任务
     *
     * @param request form
     * @return 任务列表
     */
    PageResultObject<AnalyzeTaskVO> list(AnalyzeTaskQueryVO request);

    /**
     * 预览请求参数
     *
     * @param taskId 任务
     * @return body
     */
    String preview(Long taskId);

    /**
     * 判断模板是否正在被使用
     *
     * @param templateId 模板id
     * @return T/F
     */
    Boolean isTemplateInUse(String templateId);

    /**
     * 异步调度
     */
    void asyncDispatch();

    /**
     * 接收算法回调消息
     *
     * @param request form
     */
    void callback(String request);

    /**
     * 根据分析结果模板id查询任务
     *
     * @param resultTemplateId 分析结果模板id
     * @return 分析任务
     */
    AnalyzeTaskPO getByResultTemplateId(String resultTemplateId);

    /**
     * 查询语料数量
     *
     * @param condition form
     * @return 语料数量
     */
    Long corpusSize(SemanticAnalysisConditionPO condition);
}
