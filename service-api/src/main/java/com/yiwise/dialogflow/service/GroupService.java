package com.yiwise.dialogflow.service;

import com.yiwise.dialogflow.entity.enums.GroupTypeEnum;
import com.yiwise.dialogflow.entity.po.GroupPO;
import com.yiwise.dialogflow.entity.vo.group.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/7/21
 */
public interface GroupService extends RobotResourceService {

    /**
     * 创建bot时初始化‘全部知识分组
     *
     * @param botId  botId
     * @param userId
     * @return 初始化后生成的分组实体
     */
    GroupPO initAllKnowledgeGroupOnCreateBot(Long botId, Long userId);

    /**
     * 创建bot时初始化‘全部意图分组
     *
     * @param botId  botId
     * @param userId
     * @return 初始化后生成的分组实体
     */
    GroupPO initAllIntentGroupOnCreateBot(Long botId, Long userId);

    GroupPO getRootGroup(Long botId, GroupTypeEnum type);

    /**
     * 创建分组
     *
     * @param createVO 表单
     */
    GroupPO create(GroupCreateVO createVO);

    /**
     * 根据全部入参查询单个分组
     *
     * @param botId    botId
     * @param name     分组名称严格匹配
     * @param type     类型
     * @param level    层级
     * @param parentId 上级分组id
     * @return 分组
     */
    GroupPO selectOne(Long botId, String name, GroupTypeEnum type, int level, String parentId);

    /**
     * 根据全部入参查询单个分组
     *
     * @param botId botId
     * @param id    id
     * @param type  类型
     * @return 分组
     */
    GroupPO selectOne(String id, GroupTypeEnum type, Long botId);

    /**
     * 初始化‘全部意图’分组,如果不存在的话
     *
     * @param botId  botId
     * @param userId
     * @return ‘全部意图’分组
     */
    GroupPO initAllIntentGroupIfNotExists(Long botId, Long userId);

    /**
     * 根据id查询分组
     *
     * @param id id
     * @return 分组
     */
    GroupPO getById(String id);

    /**
     * 根据id列表查询分组
     *
     * @param ids ids
     * @return 分组列表
     */
    List<GroupPO> listByIds(List<String> ids);

    /**
     * 根据botId查询全部分组
     *
     * @param botId botId
     * @return 全部分组
     */
    List<GroupPO> getListByBotId(Long botId);

    /**
     * 更新分组
     *
     * @param updateVO 表单
     */
    void update(GroupUpdateVO updateVO);

    /**
     * 分组查询
     *
     * @param botId botId
     * @param type  分组类型
     * @return 分组列表
     */
    List<GroupVO> list(GroupTypeEnum type, Long botId);

    /**
     * 删除分组
     *
     * @param deleteVO 表单
     * @return 删除结果
     */
    GroupDeleteResultVO delete(GroupDeleteVO deleteVO, Long userId);

    /**
     * 查询当前分组和全部后代分组id
     *
     * @param groupId 分组id
     * @return 当前分组及后代分组的id列表
     */
    List<String> listCurrentAndDescendantGroupId(String groupId);
}
