package com.yiwise.dialogflow.service.intent;

import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.dialogflow.entity.CheckKeywordExistsRequestVO;
import com.yiwise.dialogflow.entity.enums.SyncModeEnum;
import com.yiwise.dialogflow.entity.enums.TrainStatusEnum;
import com.yiwise.dialogflow.entity.po.DomainIntentPO;
import com.yiwise.dialogflow.entity.po.intent.IntentCorpusPO;
import com.yiwise.dialogflow.entity.po.intent.IntentPO;
import com.yiwise.dialogflow.entity.query.IntentQuery;
import com.yiwise.dialogflow.entity.vo.IdNamePair;
import com.yiwise.dialogflow.entity.vo.IntentChangeGroupVO;
import com.yiwise.dialogflow.entity.vo.IntentVO;
import com.yiwise.dialogflow.entity.vo.sync.BotSyncResultVO;
import com.yiwise.dialogflow.entity.vo.sync.IntentSyncVO;
import javaslang.Tuple2;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2022/2/15
 */
public interface IntentService {

    IntentPO save(IntentVO intentVO);

    /**
     * 批量创建单个意图
     *
     * @param botId          话术id
     * @param intentNameList 意图名称列表
     * @param groupId        分组id
     * @param userId         操作人id
     * @return 意图实体
     */
    List<IntentPO> batchCreateSingleIntent(Long botId, List<String> intentNameList, String groupId, Long userId);

    void batchInsert(List<Long> botIdList, List<DomainIntentPO> domainIntentPOList, SyncModeEnum syncMode, Long currentUserId, String targetGroupId);

    BotSyncResultVO sync(IntentSyncVO syncVO);

    Tuple2<List<? extends IntentPO>, List<? extends IntentPO>> getCreateAndUpdateIntentList(Long targetBotId, List<? extends IntentPO> intentPOList);

    List<? extends IntentPO> singleSync(Long targetBotId, SyncModeEnum syncMode, Long currentUserId, List<? extends IntentPO> intentPOList, List<IntentCorpusPO> intentCorpusPOList);

    List<IntentPO> delete(IntentQuery intentQuery, Long userId);

    PageResultObject<IntentVO> list(IntentQuery intentQuery);

    /**
     * 根据botId查询全部的意图id和名称
     *
     * @param botId 话术id
     * @return <id,name>
     */
    List<IdNamePair<String, String>> simpleList(Long botId);

    List<IntentVO> queryForList(IntentQuery intentQuery);

    IntentVO detail(String intentId);

    List<IntentVO> listWithoutPages(IntentQuery intentQuery);

    List<IntentPO> getIntentPOList(IntentQuery intentQuery);

    Optional<IntentVO> getByName(Long botId, String name);

    List<IntentPO> getAllByBotId(Long botId);

    Boolean hasIntent(Long botId);

    List<IntentPO> getByIdList(Collection<String> intentIdList);

    Map<String, String> getNameByIdList(Collection<String> intentIdList);

    void updateTrainingStatus(Long botId);

    void updateTrainingStatus(Long botId, TrainStatusEnum status);

    boolean enableTrain(Long botId);

    boolean nameExists(String intentId, Collection<String> nameList, Collection<Long> botIdList);

    Long nameExistsCount(Collection<String> nameList, Long botId);

    List<String> listIdByBotIdAndGroupIds(Long botId, List<String> groupIds);

    void changeGroup(IntentChangeGroupVO changeGroupVO);

    Map<String, Set<String>> findExistsKeyword(CheckKeywordExistsRequestVO request);

    /**
     * 关键词转移
     */
    void deleteKeyword(CheckKeywordExistsRequestVO request, Long userId);

    Map<String, String> getIntentIdNameMapWithDefault(Long botId);
}
