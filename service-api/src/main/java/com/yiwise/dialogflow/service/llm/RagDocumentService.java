package com.yiwise.dialogflow.service.llm;

import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.base.model.enums.EnabledStatusEnum;
import com.yiwise.dialogflow.entity.po.llm.RagDocumentPO;
import com.yiwise.dialogflow.entity.query.RagDocumentQueryVO;
import com.yiwise.dialogflow.entity.vo.llm.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface RagDocumentService {

    /**
     * 上传知识库文件
     *
     * @param file  支持txt、dock、docx、xls、xlsx格式
     * @param botId 话术id
     * @return 文件oss地址
     */
    String upload(MultipartFile file, Long botId);

    /**
     * 根据条件查询
     *
     * @param condition 查询条件
     * @return 文档列表
     */
    PageResultObject<RagDocumentVO> queryByCondition(RagDocumentQueryVO condition);

    /**
     * 判断是否开启大模型兜底对话
     *
     * @param botId 话术id
     * @return 是否开启
     */
    boolean checkEnableLLMChat(Long botId);

    /**
     * 创建文档
     *
     * @param request form
     * @param userId  操作人id
     */
    void createDocument(RagDocumentCreateVO request, Long userId);

    /**
     * 根据文档id查询文档
     *
     * @param docId 文档id
     * @return 文档实体
     */
    RagDocumentPO getById(String docId);

    /**
     * 处理回调
     *
     * @param request form
     */
    void callback(String request);

    /**
     * 更新启用/停用状态
     *
     * @param botId         botId
     * @param docId         文档id
     * @param enabledStatus 启用/停用状态
     * @param userId        操作人id
     */
    void updateEnabledStatus(Long botId, String docId, EnabledStatusEnum enabledStatus, Long userId);

    /**
     * 更新文档名称
     *
     * @param request form
     * @param userId  操作人id
     */
    void update(RagDocumentUpdateVO request, Long userId);

    /**
     * 删除文档
     *
     * @param request form
     * @param userId  操作人id
     */
    void delete(RagDocumentQueryVO request, Long userId);

    /**
     * 重新拆分
     *
     * @param request form
     * @param userId  操作人id
     */
    void reSplit(RagDocumentSplitRequestVO request, Long userId);

    /**
     * 重新解析
     *
     * @param botId  话术id
     * @param docId  文档id
     * @param userId 操作人id
     */
    void reParse(Long botId, String docId, Long userId);

    /**
     * 内容检索
     *
     * @param request form
     * @return 匹配结果
     */
    RagDocumentTestResultVO test(RagDocumentTestRequestVO request);

    /**
     * 导出单个文档
     *
     * @param docId 文档id
     * @return 导出文件oss地址
     */
    String export(String docId);

    /**
     * 批量导出文档
     *
     * @param request query
     * @return zip oss地址
     */
    String batchExport(RagDocumentQueryVO request);

    /**
     * 将文档的修改时间改为当前时间
     *
     * @param docId 文档id
     */
    void changeUpdateTimeToNow(String docId);

    /**
     * 根据 botId 查询文档列表
     * @param botId botId
     * @return 文档列表
     */
    List<RagDocumentPO> getByBotId(Long botId);
}