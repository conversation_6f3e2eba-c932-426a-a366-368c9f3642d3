package com.yiwise.dialogflow.service.llm;

import com.yiwise.dialogflow.entity.po.LlmLabelPO;
import com.yiwise.dialogflow.entity.vo.llm.LlmLabelQueryVO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface LlmLabelService {

    /**
     * 查询大模型分类列表
     *
     * @param request 查询参数
     * @return 分类列表
     */
    List<LlmLabelPO> list(LlmLabelQueryVO request);

    /**
     * 初始化内置分类
     *
     * @param botId  话术id
     * @param userId 操作人id
     */
    void initOnCreateBot(Long botId, Long userId);

    /**
     * 修复数据
     */
    void fixData();

    /**
     * 根据话术id修复数据
     *
     * @param botId 话术id
     */
    void fixDataByBotId(Long botId);

    /**
     * 创建分类
     *
     * @param request form
     * @param userId  操作人id
     * @return 结果
     */
    LlmLabelPO create(LlmLabelPO request, Long userId);

    /**
     * 编辑分类
     *
     * @param request form
     * @param userId  操作人id
     * @return 结果
     */
    LlmLabelPO update(LlmLabelPO request, Long userId);

    /**
     * 删除分类
     *
     * @param id     分类id
     * @param userId 操作人id
     */
    void delete(String id, Long userId);

    /**
     * 根据botId查询全部分类
     *
     * @param botId 话术id
     * @return 分类列表
     */
    List<LlmLabelPO> getAllByBotId(Long botId);

    Map<String, String> sync(List<LlmLabelPO> srcList, Long targetBotId);
}