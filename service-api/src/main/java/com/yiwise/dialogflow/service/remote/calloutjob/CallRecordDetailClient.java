package com.yiwise.dialogflow.service.remote.calloutjob;

import com.yiwise.calloutjob.api.api.CallRecordDetailApi;
import com.yiwise.dialogflow.service.remote.customer.CustomizedConfiguration;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 */
@FeignClient(value = "aicc-ai-callout-job", path = "/apiCallout/callRecordDetail", configuration = CustomizedConfiguration.class)
public interface CallRecordDetailClient extends CallRecordDetailApi {
}