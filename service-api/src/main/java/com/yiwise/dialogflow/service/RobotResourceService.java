package com.yiwise.dialogflow.service;

import com.google.common.collect.Lists;
import com.yiwise.dialogflow.entity.context.RobotResourceContext;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/6/13
 */
public interface RobotResourceService {

    /**
     * 保存到快照
     */
    void saveToSnapshot(RobotResourceContext context);

    /**
     * 校验（可选）
     */
    default void validateResource(RobotResourceContext context) {

    }

    /**
     * 从快照读取
     */
    void loadFromSnapshot(RobotResourceContext context);

    /**
     * 更新引用关系, 现在出现循环引用了, 需要二次重置依赖的引用 id
     */
    default void resetRefId(RobotResourceContext context) {

    }

    /**
     * 添加这个方法的默认实现是为了构建DAG，使所有依赖树最终都指向同一个根节点
     */
    default List<Class<? extends RobotResourceService>> dependsOn() {
        return Lists.newArrayList(DefaultRobotResourceServiceImpl.class);
    }

}
