package com.yiwise.dialogflow.service.thirdpart.douyin;

import com.yiwise.dialogflow.api.dto.request.douyin.DouyinBotInfoQueryDTO;
import com.yiwise.dialogflow.api.dto.response.douyin.DouyinBotInfoDTO;

import java.util.List;

public interface DouyinCallbackService {

    /**
     * 查询话术列表
     */
    List<DouyinBotInfoDTO> queryByCondition(DouyinBotInfoQueryDTO query);

    void callback(Long botId, Long tenantId);

    void callback(Long botId);
}
