package com.yiwise.dialogflow.service;

import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.batch.model.vo.JobStartResultVO;
import com.yiwise.dialogflow.entity.po.callout.CallDetailPO;
import com.yiwise.dialogflow.entity.po.semantic.analysis.SemanticAnalysisConditionPO;
import com.yiwise.dialogflow.entity.po.semantic.analysis.SemanticAnalysisTemplatePO;
import com.yiwise.dialogflow.entity.vo.SemanticAnalysisTemplateVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/28
 */
public interface SemanticAnalysisService {
    void save(SemanticAnalysisTemplatePO templatePO);

    void copy(String templateId, Long currentUserId);

    void delete(String templateId);

    PageResultObject<SemanticAnalysisTemplateVO> list(Integer pageNum, Integer pageSize);

    void clearCorpus(String templateId, Long currentUserId);

    JobStartResultVO exportCorpus(String templateId, Long currentUserId);

    List<CallDetailPO> selectRecords(SemanticAnalysisConditionPO conditionPO);

    SemanticAnalysisTemplatePO get(String templateId);

    Integer corpusSize(SemanticAnalysisConditionPO conditionPO);
}
