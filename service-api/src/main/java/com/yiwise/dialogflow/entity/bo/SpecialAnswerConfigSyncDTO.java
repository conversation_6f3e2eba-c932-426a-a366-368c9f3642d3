package com.yiwise.dialogflow.entity.bo;

import com.yiwise.base.common.utils.bean.DeepCopyUtils;
import com.yiwise.dialogflow.entity.po.SpecialAnswerConfigPO;
import com.yiwise.dialogflow.entity.po.intent.IntentPO;
import com.yiwise.dialogflow.entity.vo.BotVO;
import com.yiwise.dialogflow.entity.vo.sync.SpecialAnswerSyncVO;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/10/13 11:32:52
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SpecialAnswerConfigSyncDTO implements Serializable {
    /**
     * 待同步的特殊语境
     */
    SpecialAnswerConfigPO specialAnswerConfigPO;

    /**
     * 源特殊语境意图映射集合（name->List<IntentPO>）
     */
    Map<String, List<IntentPO>> sourceNameToIntentPOMap;


    /**
     * 相关的排除意图列表
     */
    Map<String, List<IntentPO>> relatedExcludeIntentListMap;

    Long targetBotId;

    /**
     * 同步参数
     */
    SpecialAnswerSyncVO syncVO;

    /**
     * 目标特殊语境映射（name->PO）
     */
    Map<String, SpecialAnswerConfigPO> targetSpecialAnswerNameToPOMap;

    /**
     * 目标特殊语境
     */
    SpecialAnswerConfigPO targetSpecialAnswerConfigPO;

    /**
     * 源botVO
     */
    BotVO sourceBotVO;

    /**
     * 源bot的变量映射map（变量id->变量名）
     */
    Map<String, String> sourceIdToVariableNameMap;

    DependentResourceBO sourceDependResource;


    public SpecialAnswerConfigPO getSpecialAnswerConfigPO() {
        return DeepCopyUtils.copyObject(specialAnswerConfigPO);
    }
}