package com.yiwise.dialogflow.entity.enums;

import com.yiwise.base.model.enums.CodeDescEnum;

public enum ExceedThresholdReplayStrategyEnum implements CodeDescEnum {
    DEFAULT_BRANCH(0, "不再播放当前节点任何话术"),
    SWITCH_ANSWER_REPLAY(1, "播放切换话术"),
    ;

    private final Integer code;

    private final String desc;

    ExceedThresholdReplayStrategyEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }
}
