package com.yiwise.dialogflow.entity.enums.llm;

import com.yiwise.base.model.enums.CodeDescEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum RagDocumentStatusEnum implements CodeDescEnum {

    PARSING(1, "解析中"),
    SUCCESS(2, "成功"),
    FAILED(3, "失败"),
    ;

    private final Integer code;
    private final String desc;

    public static boolean isSuccess(RagDocumentStatusEnum status) {
        return SUCCESS.equals(status);
    }

    public static boolean isFailed(RagDocumentStatusEnum status) {
        return FAILED.equals(status);
    }

    public static boolean isParsing(RagDocumentStatusEnum status) {
        return PARSING.equals(status);
    }
}