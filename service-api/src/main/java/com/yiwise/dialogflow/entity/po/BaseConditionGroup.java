package com.yiwise.dialogflow.entity.po;

import cn.hutool.core.collection.CollectionUtil;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.entity.bo.DependentResourceBO;
import com.yiwise.dialogflow.entity.enums.ConditionEnum;
import com.yiwise.dialogflow.entity.enums.ConditionVarTypeEnum;
import com.yiwise.dialogflow.entity.enums.VariableTypeEnum;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.Predicate;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.*;

@Data
public abstract class BaseConditionGroup implements Serializable {

    /**
     * 条件列表,外层或运算,内层与运算
     */
    private List<List<ConditionExpressionPO>> conditionList;

    private boolean isJudgeNode() {
        return this instanceof NodeConditionBranchPO;
    }

    protected void conditionGroupValidate() {
        throwExceptionIfMatch(getConditionList(), CollectionUtils::isEmpty, "条件列表不能为空");
        for (List<ConditionExpressionPO> andConditionList : getConditionList()) {
            throwExceptionIfMatch(andConditionList, CollectionUtils::isEmpty, "条件列表不能为空");
            for (ConditionExpressionPO andCondition : andConditionList) {

                throwExceptionIfMatch(andCondition, Objects::isNull, "条件表达式不能为空");
                ConditionVarTypeEnum preVarType = andCondition.getPreVarType();
                throwExceptionIfMatch(andCondition.getPreVarType(), Objects::isNull, "前置变量类型不能为空");
                ConditionEnum condition = andCondition.getCondition();
                throwExceptionIfMatch(condition, Objects::isNull, "条件不能为空");

                if (mustConfigVarId(preVarType)) {
                    throwExceptionIfMatch(andCondition.getPreVarId(), StringUtils::isBlank, "前置变量不能为空");
                }

                // 只有判断节点支持选择意图条件
                if (!isJudgeNode()) {
                    throwExceptionIfMatch(preVarType, ConditionVarTypeEnum::isIntent, "不支持配置意图条件");
                }
                if (ConditionVarTypeEnum.isIntent(preVarType)) {
                    throwExceptionIfMatch(andCondition.getIntentIdList(), CollectionUtils::isEmpty, "意图列表不能为空");
                    throwExceptionIfMatch(andCondition.getCondition(), ConditionEnum::notSupportIntent, "条件不匹配");
                } else {
                    throwExceptionIfMatch(andCondition.getCondition(), ConditionEnum::onlySupportIntent, "条件不匹配");
                }

                if (ConditionVarTypeEnum.CONSTANT.equals(preVarType)) {
                    throw new ComException(ComErrorCode.VALIDATE_ERROR, "前置变量不支持常量类型");
                }

                if (ConditionEnum.onlySupportPreValue(condition)) {
                    continue;
                }
                ConditionVarTypeEnum postVarType = andCondition.getPostVarType();
                throwExceptionIfMatch(postVarType, Objects::isNull, "后置变量类型不能为空");
                if (mustConfigVarId(postVarType)) {
                    throwExceptionIfMatch(andCondition.getPostVarId(), Objects::isNull, "后置变量不能为空");
                    continue;
                }
                if (ConditionVarTypeEnum.CONSTANT.equals(postVarType)) {
                    throwExceptionIfMatch(andCondition.getConstantStr(), StringUtils::isBlank, "后置常量不能为空");
                }
            }
        }
    }

    /**
     * 自定义变量和动态变量必须选择变量id
     */
    private static boolean mustConfigVarId(ConditionVarTypeEnum varType) {
        return ConditionVarTypeEnum.isVar(varType);
    }

    protected Set<String> acquireSwithConditionDependsVariableIdSet() {
        Set<String> answerDependsVarIdSet = new HashSet<>();
        if (CollectionUtils.isNotEmpty(getConditionList())) {
            for (List<ConditionExpressionPO> andConditionList : getConditionList()) {
                for (ConditionExpressionPO andCondition : andConditionList) {
                    if (mustConfigVarId(andCondition.getPreVarType())) {
                        answerDependsVarIdSet.add(andCondition.getPreVarId());
                    }
                    if (mustConfigVarId(andCondition.getPostVarType())) {
                        answerDependsVarIdSet.add(andCondition.getPostVarId());
                    }
                }
            }
            answerDependsVarIdSet.remove(null);
        }
        return answerDependsVarIdSet;
    }

    protected Set<String> acquireBranchDependIntentIdSet() {
        if (CollectionUtils.isEmpty(conditionList)) {
            return Collections.emptySet();
        }
        Set<String> intentIdSet = new HashSet<>();
        for (List<ConditionExpressionPO> andConditionList : conditionList) {
            if (CollectionUtil.isEmpty(andConditionList)) {
                continue;
            }
            for (ConditionExpressionPO condition : andConditionList) {
                if (ConditionVarTypeEnum.isIntent(condition.getPreVarType())) {
                    intentIdSet.addAll(condition.getIntentIdList());
                }
            }
        }
        return intentIdSet;
    }

    private static ConditionVarTypeValidator<ConditionVarTypeEnum, String, Map<String, VariableTypeEnum>> conditionVarTypeValidator =
            (conditionVarType, varId, varIdTypeMap) -> {
                if (!mustConfigVarId(conditionVarType)) {
                    return;
                }
                VariableTypeEnum varType = varIdTypeMap.get(varId);
                // 切换条件中选择的变量类型要和实际类型匹配
                if (ConditionVarTypeEnum.CUSTOM.equals(conditionVarType) && VariableTypeEnum.isCustomOrSystemVariable(varType)) {
                    return;
                }
                if (ConditionVarTypeEnum.DYNAMIC.equals(conditionVarType) && VariableTypeEnum.DYNAMIC.equals(varType)) {
                    return;
                }
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "条件设置中的变量类型和实际变量类型不一致");
            };

    protected void conditionGroupValidateWithResource(DependentResourceBO dependentResource) {
        conditionGroupValidate();
        // 切换条件中配置的变量id列表
        Set<String> answerDependsVarIdSet = acquireSwithConditionDependsVariableIdSet();
        Collection<String> notExistsVarIdList = CollectionUtils.removeAll(answerDependsVarIdSet, dependentResource.getVariableNameIdMap().values());
        if (CollectionUtils.isNotEmpty(notExistsVarIdList)){
            throw new ComException(ComErrorCode.VALIDATE_ERROR,"变量id"+notExistsVarIdList+"不存在");
        }
        // 变量校验，动态变量类型的条件只能选择动态变量，自定义变量类型的条件只能选择内置变量和自定义变量
        for (List<ConditionExpressionPO> andConditionList : conditionList) {
            for (ConditionExpressionPO condition : andConditionList) {
                conditionVarTypeValidator.apply(condition.getPreVarType(), condition.getPreVarId(), dependentResource.getVarIdTypeMap());
                conditionVarTypeValidator.apply(condition.getPostVarType(), condition.getPostVarId(), dependentResource.getVarIdTypeMap());
            }
        }
        // 校验判断节点分支中配置的意图是否存在
        Collection<String> notExistsIntentIds = CollectionUtils.subtract(acquireBranchDependIntentIdSet(), dependentResource.getIntentIdNameMap().keySet());
        if (CollectionUtils.isNotEmpty(notExistsIntentIds)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "意图" + notExistsIntentIds + "不存在");
        }
    }

    protected  <T> void throwExceptionIfMatch(T src, Predicate<T> predicate, String errorMessage){
        if (predicate.evaluate(src)){
            throw new ComException(ComErrorCode.VALIDATE_ERROR,errorMessage);
        }
    }

    private interface ConditionVarTypeValidator<T,U,X> {

        void apply(T t, U u, X x);
    }
}
