package com.yiwise.dialogflow.entity.vo.audio;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class TtsComposeProgressVO {
    Long botId;
    String botName;
    Integer totalCount;
    Integer completedCount;
    /**
     * 总录音进度
     */
    Integer percent;
    /**
     * 真人录音进度
     */
    Integer manMadePercent;
    /**
     * tts录音进度
     */
    Integer ttsPercent;
    Boolean failed;
    Boolean running;
    public Boolean getCompleted() {
        return percent >= 100;
    }
}
