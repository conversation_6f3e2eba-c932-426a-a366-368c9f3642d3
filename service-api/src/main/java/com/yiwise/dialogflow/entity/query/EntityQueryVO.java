package com.yiwise.dialogflow.entity.query;

import com.yiwise.base.model.bean.vo.AbstractQueryVO;
import com.yiwise.dialogflow.entity.enums.EntityTypeEnum;
import lombok.Data;

import java.util.List;

@Data
public class EntityQueryVO extends AbstractQueryVO {
    /**
     * botId
     */
    Long botId;

    /**
     * 实体名称
     */
    String name;

    /**
     * 实体类型
     */
    EntityTypeEnum type;

    List<String> entityIdList;
}
