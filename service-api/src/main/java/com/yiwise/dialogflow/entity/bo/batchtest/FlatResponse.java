package com.yiwise.dialogflow.entity.bo.batchtest;

import com.yiwise.dialogflow.engine.share.enums.AnswerSourceEnum;
import com.yiwise.dialogflow.engine.share.enums.RepeatAnswerPlayStrategyEnum;
import lombok.Data;

@Data
public class FlatResponse {
    String answer;

    String answerTemplate;

    RepeatAnswerPlayStrategyEnum answerPlayStrategy;

    AnswerSourceEnum answerSource;

    String label;

    Boolean wait;

    Boolean hangup;

    Boolean exception;

    Boolean uninterrupted;

    String exceptionMsg;

    String debugLog;

}
