package com.yiwise.dialogflow.entity.vo.audio.request;

import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class BatchAdjustVolumeRequestVO {

    @NotNull(message = "botId不能为空")
    Long botId;
    String answerText;
    @NotEmpty(message = "urlList不能为空")
    List<String> urlList;
    @NotNull(message = "volume不能为空")
    @Range(min = 0, max = 200, message = "音量值不合法, 最大值200, 最小值0")
    Integer volume;
}
