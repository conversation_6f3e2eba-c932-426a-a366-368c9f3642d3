package com.yiwise.dialogflow.entity.enums;

import com.yiwise.base.model.enums.CodeDescEnum;

public enum EntityTypeEnum implements CodeDescEnum {
    STANDARD(0, "标准实体"),
    REGEX(1, "正则实体"),
    SYSTEM(2, "系统实体"),
    LARGE_MODEL(3, "大模型实体"),
    ;
    EntityTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    final Integer code;
    final String desc;


    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }
}
