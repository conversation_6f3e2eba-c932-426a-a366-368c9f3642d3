package com.yiwise.dialogflow.entity.vo.audio.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/12/21
 */
@Data
public class PublicAudioUpdateRequestVO implements Serializable {

    /**
     * id
     */
    @NotBlank(message = "id不能为空")
    private String id;

    /**
     * 新的url
     */
    @NotBlank(message = "url不能为空")
    private String url;

    /**
     * 音量
     */
    @NotNull(message = "volume不能为空")
    private Integer volume;

    /**
     * 时长
     */
    @NotNull(message = "duration不能为空")
    private Integer duration;
}