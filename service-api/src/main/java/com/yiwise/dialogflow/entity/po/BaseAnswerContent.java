package com.yiwise.dialogflow.entity.po;

import com.yiwise.base.common.text.TextPlaceholderElement;
import com.yiwise.base.common.text.TextPlaceholderTypeEnum;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.engine.share.common.AnswerPlaceholderSplitter;
import com.yiwise.dialogflow.entity.bo.DependentResourceBO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class BaseAnswerContent extends BaseConditionGroup implements DialogResource, Labelled, Serializable {

    /**
     * 答案的标签, bot内唯一
     */
    String label;

    /**
     * 答案文本
     */
    String text;

    Boolean isDefault;

    /**
     * 是否启用变量参数
     */
    private Boolean enableVarCondition;

    public void validParam() {
        if (StringUtils.isBlank(text)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "答案文本不能为空");
        }
        if (BooleanUtils.isTrue(enableVarCondition)) {
            super.conditionGroupValidate();
        }
    }

    public String getUniqueId() {
        return StringUtils.isNotBlank(label) ? label : text;
    }

    @Override
    protected Set<String> acquireSwithConditionDependsVariableIdSet() {
        Set<String> answerDependsVarIdSet = new HashSet<>();
        // 自定义变量必须是当前bot变量库中配置的变量
        if (BooleanUtils.isTrue(enableVarCondition)) {
            answerDependsVarIdSet.addAll(super.acquireSwithConditionDependsVariableIdSet());
        }
        return answerDependsVarIdSet;
    }

    @Override
    public void validWithResource(DependentResourceBO resource) {
        validParam();
        if (BooleanUtils.isTrue(enableVarCondition)) {
            super.conditionGroupValidateWithResource(resource);
        }
        AnswerPlaceholderSplitter splitter = new AnswerPlaceholderSplitter(text, false);
        splitter.getPlaceholderList().forEach(placeholder -> {
            if (!resource.getVariableNameIdMap().containsKey(placeholder)) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, String.format("变量[%s]不存在", placeholder));
            }
        });
    }

    public Set<String> calDependsVariableIdSet(DependentResourceBO resource) {
        AnswerPlaceholderSplitter splitter = new AnswerPlaceholderSplitter(text, false);
        Set<String> result = new HashSet<>();
        result.addAll(splitter.getPlaceholderList().stream()
                .map(resource.getVariableNameIdMap()::get)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet()));
        result.addAll(acquireSwithConditionDependsVariableIdSet());
        return result;
    }

    public boolean updateVariableName(String oldName, String newName) {
        if (StringUtils.isBlank(text)) {
            return false;
        }
        AnswerPlaceholderSplitter splitter = new AnswerPlaceholderSplitter(text, false);
        StringBuilder sb = new StringBuilder();
        boolean updated = false;
        for (TextPlaceholderElement textPlaceholder : splitter.getTextPlaceholderList()) {
            if (TextPlaceholderTypeEnum.PLACE_HOLDER.equals(textPlaceholder.getType())) {
                if (oldName.equals(textPlaceholder.getValue())) {
                    sb.append(String.format("${%s}", newName));
                    updated = true;
                } else {
                    sb.append(String.format("${%s}", textPlaceholder.getValue()));
                }
            } else {
                sb.append(textPlaceholder.getValue());
            }
        }
        text = sb.toString();
        return updated;
    }

    @Override
    public String toString() {
        return String.format("AnswerContent(label=%s, text=%s)", label, text);
    }
}
