package com.yiwise.dialogflow.entity.bo.stats;

import com.yiwise.dialogflow.engine.share.enums.AnswerSourceEnum;
import com.yiwise.dialogflow.engine.share.response.AnswerLocateBO;
import com.yiwise.dialogflow.engine.share.response.AnswerResult;
import lombok.AccessLevel;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;
import lombok.experimental.FieldDefaults;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

@Getter
@EqualsAndHashCode
@ToString
@FieldDefaults(level = AccessLevel.PROTECTED)
public class AnswerUniqueKey {
    String answerTemplate;
    AnswerSourceEnum answerSource;
    String stepId;
    String nodeId;
    String knowledgeId;
    String specialAnswerConfigId;

    public String getAnswerTemplate() {
        return StringUtils.trimToEmpty(answerTemplate);
    }

    public static AnswerUniqueKey from(AnswerResult answer) {
        AnswerLocateBO answerLocate = answer.getLocate();
        AnswerUniqueKey key = new AnswerUniqueKey();
        key.answerTemplate = StringUtils.trimToEmpty(answer.getTemplate());
        key.answerSource = answerLocate.getAnswerSource();
        if (Objects.nonNull(answerLocate.getAnswerSource())) {
            switch (answerLocate.getAnswerSource()) {
                case KNOWLEDGE:
                    key.knowledgeId = answerLocate.getKnowledgeId();
                    break;
                case STEP:
                    key.stepId = answerLocate.getStepId();
                    key.nodeId = answerLocate.getNodeId();
                    break;
                case SPECIAL_ANSWER:
                    key.specialAnswerConfigId = answerLocate.getSpecialAnswerConfigId();
                    break;
                default:
                    break;
            }
        }
        return key;
    }
    public static AnswerUniqueKey from(String answerTemplate, AnswerLocateBO answerLocate) {
        AnswerUniqueKey key = new AnswerUniqueKey();
        key.answerTemplate = StringUtils.trimToEmpty(answerTemplate);
        key.answerSource = answerLocate.getAnswerSource();
        if (Objects.nonNull(answerLocate.getAnswerSource())) {
            switch (answerLocate.getAnswerSource()) {
                case KNOWLEDGE:
                    key.knowledgeId = answerLocate.getKnowledgeId();
                    break;
                case STEP:
                    key.stepId = answerLocate.getStepId();
                    key.nodeId = answerLocate.getNodeId();
                    break;
                case SPECIAL_ANSWER:
                    key.specialAnswerConfigId = answerLocate.getSpecialAnswerConfigId();
                    break;
                default:
                    break;
            }
        }
        return key;
    }
}
