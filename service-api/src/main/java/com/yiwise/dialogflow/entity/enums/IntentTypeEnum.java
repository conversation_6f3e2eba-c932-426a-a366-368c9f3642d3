package com.yiwise.dialogflow.entity.enums;

import com.yiwise.base.model.enums.CodeDescEnum;

/**
 * <AUTHOR>
 * @date 2022/2/14
 */
public enum IntentTypeEnum implements CodeDescEnum {
    /**
     * 单个意图
     */
    SINGLE(0, "单个意图"),
    /**
     * 组合意图
     */
    COMPOSITE(1, "组合意图"),
    ;

    final Integer code;
    final String desc;
    IntentTypeEnum(Integer code, String desc ) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }
}
