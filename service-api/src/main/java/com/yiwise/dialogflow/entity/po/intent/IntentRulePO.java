package com.yiwise.dialogflow.entity.po.intent;

import com.yiwise.base.model.bean.po.BaseTimeUserIdPO;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.entity.bo.DependentResourceBO;
import com.yiwise.dialogflow.entity.enums.IntentLevelEnum;
import com.yiwise.dialogflow.entity.enums.SpecialIntentRuleTypeEnum;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> @ yiwise . com>
 * @version v0.1 2022/3/30
 * @class <code>IntentRulePO</code>
 * @see
 * @since JDK1.8
 */
@Data
@Document(collection = "botIntentRule")
public class IntentRulePO extends BaseTimeUserIdPO implements Serializable {
    public static final String COLLECTION_NAME = "botIntentRule";
    public static final String SORT_ATTRIBUTE = "matchOrder";

    /**
     * 规则id
     */
    private String id;
    /**
     * 默认特殊规则
     */
    private SpecialIntentRuleTypeEnum specialIntentRuleType;
    /**
     * 意向等级
     */
    private IntentLevelEnum intentLevel;
    /**
     * 意向等级标签
     */
    private Integer intentLevelTagDetailCode;
    /**
     * 判断条件列表
     */
    private List<IntentRuleConditionPO> conditionList = new LinkedList<>();
    /**
     * 规则排序
     */
    private Long matchOrder = 0L;
    /**
     * 话术id
     */
    @Indexed
    private Long botId;

    public void validOrThrow(DependentResourceBO dependentResource) {
        if (CollectionUtils.isNotEmpty(conditionList)) {
            conditionList.forEach(IntentRuleConditionPO -> IntentRuleConditionPO.validOrThrow(dependentResource));
        } else {
            if (Objects.isNull(specialIntentRuleType)) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "意向规则条件列表不能为空");
            }
        }

    }
}
