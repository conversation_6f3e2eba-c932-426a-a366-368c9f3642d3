package com.yiwise.dialogflow.entity.vo.stats;


import lombok.Data;

import java.io.Serializable;

@Data
public class KnowledgeStatsVO implements Serializable {
    /**
     * 命中次数指标
     */
    CommonPercentVO reach = new CommonPercentVO();

    /**
     * 命中通话指标
     */
    CommonPercentVO reachCall = new CommonPercentVO();

    /**
     * 客户主动挂机指标
     */
    CommonPercentVO customerHangup = new CommonPercentVO();

    /**
     * 拒绝指标
     */
    CommonPercentVO decline = new CommonPercentVO();

    public int getReachCount() {
        return reach.getCount();
    }

    public double getReachPercent() {
        return reach.getPercent();
    }

    public String getReachDisplayPercent() {
        return reach.getDisplayPercent();
    }

    public int getReachCallCount() {
        return reachCall.getCount();
    }

    public double getReachCallPercent() {
        return reachCall.getPercent();
    }

    public String getReachCallDisplayPercent() {
        return reachCall.getDisplayPercent();
    }

    public int getCustomerHangupCount() {
        return customerHangup.getCount();
    }

    public double getCustomerHangupPercent() {
        return customerHangup.getPercent();
    }

    public String getCustomerHangupDisplayPercent() {
        return customerHangup.getDisplayPercent();
    }

    public int getDeclineCount() {
        return decline.getCount();
    }

    public double getDeclinePercent() {
        return decline.getPercent();
    }

    public String getDeclineDisplayPercent() {
        return decline.getDisplayPercent();
    }
}
