package com.yiwise.dialogflow.entity.po;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@Document(collection = SmsGenerateResultPO.COLLECTION_NAME)
@CompoundIndexes(
        @CompoundIndex(name = "idx_botId_seq", def = "{botId:1, seq:1}")
)
public class SmsGenerateResultPO {

    public static final String COLLECTION_NAME = "smsGenerateResult";

    /**
     * 主键
     */
    @Id
    private String id;

    /**
     * 话术id
     */
    private Long botId;

    /**
     * 序号
     */
    private Integer seq;

    /**
     * 短信内容
     */
    private String content;

    /**
     * 是否置顶
     */
    private Boolean isPinned;

    /**
     * 置顶时间
     */
    private LocalDateTime pinTime;
}
