package com.yiwise.dialogflow.entity.bo;

import com.yiwise.dialogflow.entity.po.BotSpeechConfigPO;
import com.yiwise.dialogflow.entity.vo.sync.BasicSyncVO;
import com.yiwise.dialogflow.entity.vo.sync.SpeechConfigSyncVO;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/10/26 09:26:25
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class BotConfigSyncDTO implements Serializable {
    /**
     * 目标botId
     */
    Long targetBotId;

    /**
     * 源bot设置信息
     */
    BotSpeechConfigPO sourceBotSpeechConfig;

    /**
     * 当前用户id
     */
    Long currentUserId;

    /**
     * 同步参数
     */
    BasicSyncVO syncVO;
}