package com.yiwise.dialogflow.entity.vo.audio;

import com.yiwise.dialogflow.engine.share.response.AnswerLocateBO;
import com.yiwise.dialogflow.entity.enums.IntentPropertiesEnum;
import com.yiwise.dialogflow.entity.vo.SimpleIntentVO;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
@Data
public class TreeifyAnswerAudioVO {
    IntentPropertiesEnum intentProperties;

    List<SimpleIntentVO> simpleIntentList;

    List<AnswerAudioWrapVO> answerAudioList;

    List<TreeifyAnswerAudioVO> childList;

    /**
     * id, 没什么用, 前端需要后端生成一个唯一id
     */
    String id;

    String displayLabel;

    public TreeifyAnswerAudioVO() {
        answerAudioList = new ArrayList<>();
        childList = new ArrayList<>();
        simpleIntentList = new ArrayList<>();
    }

    public IntentPropertiesEnum getIntentProperties() {
        if (CollectionUtils.isEmpty(simpleIntentList)) {
            return IntentPropertiesEnum.NEUTRAL;
        }
        return simpleIntentList.get(0).getIntentProperties();
    }

    public String getDisplayLabel() {
        if (StringUtils.isNotEmpty(displayLabel)) {
            return displayLabel;
        }
        if (CollectionUtils.isEmpty(answerAudioList)) {
            return "";
        }
        Optional<AnswerLocateBO> locateOpt = answerAudioList.stream()
                .map(AnswerAudioWrapVO::getLocate)
                .filter(Objects::nonNull)
                .findFirst();
        if (locateOpt.isPresent()) {
            return locateOpt.get().getDisplayLabelName();
        }
        return "";
    }

    public String getId() {
        if (StringUtils.isBlank(id)) {
            id = new ObjectId().toHexString();
        }
        return id;
    }
}
