package com.yiwise.dialogflow.entity.po.intent;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.entity.bo.DependentResourceBO;
import com.yiwise.dialogflow.entity.enums.DialStatusEnum;
import com.yiwise.dialogflow.entity.enums.DialogFlowConditionOperationTypeEnum;
import com.yiwise.dialogflow.entity.enums.DialogFlowConditionTypeEnum;
import com.yiwise.dialogflow.entity.po.DialogFlowExtraRuleConditionNodePO;
import com.yiwise.dialogflow.entity.po.KnowledgeNamePO;
import com.yiwise.dialogflow.pattern.PatternEnhance;
import com.yiwise.dialogflow.utils.ParseUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

import static com.yiwise.dialogflow.entity.enums.DialogFlowConditionTypeEnum.*;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class IntentRuleConditionPO implements Serializable {
    private DialogFlowConditionTypeEnum type;
    private DialogFlowConditionOperationTypeEnum operation;
    private Integer number;
    private List<Integer> intentLevelIdList;
    private List<DialStatusEnum> dialStatusList;
    private List<String> keywords;

    private DialogFlowConditionOperationTypeEnum subOperation;

    @JsonIgnore
    private transient List<PatternEnhance> keywordPatterns;

    /**
     * 描述列表
     */
    private List<String> descList;

    /**
     * 大模型分类id
     */
    private String llmLabelId;

    /**
     * 问答知识类表
     */
    private List<KnowledgeNamePO> robotKnowledgeList;

    /**
     * 流程节点可多选
     */
    private List<DialogFlowExtraRuleConditionNodePO> nodeList;

    /**
     * 独立对话流
     */
    private List<String> stepList;

    /**
     * 特殊语境id
     */
    private String specialAnswerId;

    /**
     * 实体id列表
     */
    private List<String> entityIdList;

    /**
     * DialogFlowConditionTypeEnum.TRIGGER_INTENT 条件时,触发的意图id列表
     */
    private List<String> intentIdList;

    /**
     * 变量id列表
     */
    private List<String> variableIdList;

    public DialogFlowConditionOperationTypeEnum getSubOperation() {
        if (subOperation == null && HIT_INTENT.equals(type) && DialogFlowConditionOperationTypeEnum.ANY_HIT.equals(operation)) {
            return DialogFlowConditionOperationTypeEnum.GREATER_OR_EQUAL;
        }
        return subOperation;
    }

    public Integer getNumber() {
        if (number == null && HIT_INTENT.equals(type) && DialogFlowConditionOperationTypeEnum.ANY_HIT.equals(operation)) {
            return 1;
        }
        return number;
    }

    public List<String> getRobotKnowledgeIdList() {
        if (CollectionUtils.isNotEmpty(robotKnowledgeList)) {
            return robotKnowledgeList.stream().map(KnowledgeNamePO::getId).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    public void validOrThrow(DependentResourceBO dependentResource) {
        if (Objects.isNull(getType())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "条件类型不能为空");
        }

        Set<DialogFlowConditionTypeEnum> requireNumberTypeSet = new HashSet<>();
        requireNumberTypeSet.add(MAIN_STEP_FINISH_PERCENTAGE);
        requireNumberTypeSet.add(BUSINESS_KNOWLEDGE_TRIGGER_COUNT);
        requireNumberTypeSet.add(DECLINE_TRIGGER_COUNT);
        requireNumberTypeSet.add(DEFINITIVE_TRIGGER_COUNT);
        requireNumberTypeSet.add(DIALOG_ROUND_COUNT);
        requireNumberTypeSet.add(DIALOG_DURATION);
        requireNumberTypeSet.add(ACTUAL_DIALOG_DURATION);
        requireNumberTypeSet.add(CUSTOMER_SAY_WORD_COUNT);
        requireNumberTypeSet.add(ALGORITHM_ACTIVITY_INTENT_LEVEL);
        requireNumberTypeSet.add(ALGORITHM_PRIVATE_DOMAIN_INTENT_LEVEL);
        requireNumberTypeSet.add(ALGORITHM_PRIVATE_DOMAIN_INTENT_LEVEL_PASSIVE);
        requireNumberTypeSet.add(ALGORITHM_EDUCATION_ACTIVITY_INTENT_LEVEL);
        requireNumberTypeSet.add(EFFECTIVE_CHAT_ROUNDS);

        Set<DialogFlowConditionTypeEnum> requireKeywordsTypeSet = new HashSet<>();
        requireKeywordsTypeSet.add(DIALOG_CONTENT);
        requireKeywordsTypeSet.add(LAST_CUSTOMER_CONTENT);
        requireKeywordsTypeSet.add(LAST_AI_CONTENT);

        Set<DialogFlowConditionTypeEnum> requireNodeIdListTypeSet = new HashSet<>();
        requireNodeIdListTypeSet.add(TRIGGER_PROCESS_NODE);
        requireNodeIdListTypeSet.add(STEP_NODE_FINISH_PERCENTAGE);

        Set<DialogFlowConditionTypeEnum> requestStepListTypeSet = new HashSet<>();
        requestStepListTypeSet.add(TRIGGER_DEPENDENCE_DIALOGFLOW);

        Set<DialogFlowConditionTypeEnum> requireEntityTypeSet = new HashSet<>();
        requireEntityTypeSet.add(ENTITY_COLLECT);
        Set<DialogFlowConditionTypeEnum> requirVarTypeSet = new HashSet<>();
        requirVarTypeSet.add(DYNAMIC_VARIABLE);

        Set<DialogFlowConditionTypeEnum> requireDescTypeSet = new HashSet<>();
        requireDescTypeSet.add(LLM_BUILT_IN_TAG);
        requireDescTypeSet.add(LLM_CUSTOM_TAG);

        if (requireDescTypeSet.contains(getType())) {
            if (CollectionUtils.isEmpty(getDescList())) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, String.format("条件[%s]下描述不能为空", getType().getDesc()));
            }
        }

        if (requireNumberTypeSet.contains(getType())) {
            if (Objects.isNull(getNumber())) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, String.format("条件[%s]下number不能为空", getType().getDesc()));
            }
        }

        if (requireKeywordsTypeSet.contains(getType())) {
            if (CollectionUtils.isEmpty(getKeywords())) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, String.format("条件[%s]下关键词不能为空", getType().getDesc()));
            }
            getKeywords().forEach(keyword -> {
                if (ParseUtil.invalid(keyword)) {
                    throw new ComException(ComErrorCode.VALIDATE_ERROR, String.format("条件[%s]下关键词[%s]不合法", getType().getDesc(), keyword));
                }
            });
        }

        if (requireNodeIdListTypeSet.contains(getType())) {
            if (CollectionUtils.isEmpty(getNodeList())) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, String.format("条件[%s]下话术节点不能为空", getType().getDesc()));
            }
            List<DialogFlowExtraRuleConditionNodePO> existList = getNodeList().stream()
                    .filter(item -> dependentResource.getStepIdNameMap().containsKey(item.getStepIndex())
                            && dependentResource.getNodeIdNameMap().containsKey(item.getNodeIndex()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(existList)) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, String.format("条件[%s]下话术节点不能为空", getType().getDesc()));
            }
        }

        if (requestStepListTypeSet.contains(getType())) {
            if (CollectionUtils.isEmpty(getStepList())) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, String.format("条件[%s]下话术流程不能为空", getType().getDesc()));
            }
            List<String> existList = getStepList().stream().filter(item -> dependentResource.getStepIdNameMap().containsKey(item)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(existList)) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, String.format("条件[%s]下话术流程不能为空", getType().getDesc()));
            }
        }

        if (LLM_LABEL.equals(getType())) {
            if (StringUtils.isBlank(getLlmLabelId())) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, String.format("条件[%s]下大模型分类不能为空", getType().getDesc()));
            }
            if (!dependentResource.getLlmLabelId2NameMap().containsKey(getLlmLabelId())) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, String.format("条件[%s]下大模型分类[%s]不存在", getType().getDesc(), getLlmLabelId()));
            }
        }

        // 问答知识列表
        if (TRIGGER_ROBOT_KNOWLEDGE.equals(getType()) || KNOWLEDGE_FINISH_PERCENTAGE.equals(getType())) {
            if (CollectionUtils.isEmpty(getRobotKnowledgeIdList())) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, String.format("条件[%s]下问答知识列表不能为空", getType().getDesc()));
            }
            List<String> existKnowledgeIdList = getRobotKnowledgeIdList().stream()
                    .filter(item -> dependentResource.getKnowledgeIdNameMap().containsKey(item))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(existKnowledgeIdList)) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, String.format("条件[%s]下问答知识列表不能为空", getType().getDesc()));
            }
        }
        if (DIALOG_STATUS.equals(getType())) {
            if (CollectionUtils.isEmpty(getDialStatusList())) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, String.format("条件[%s]下通话状态不能为空", getType().getDesc()));
            }
        }
        if (INTENT_TAG.equals(getType())) {
            if (CollectionUtils.isEmpty(getIntentLevelIdList())) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, String.format("条件[%s]列表不能为空", getType().getDesc()));
            }
        }

        if (HIT_INTENT.equals(getType())) {
            if (CollectionUtils.isEmpty(getIntentIdList())) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, String.format("条件[%s]下意图id列表不能为空", getType().getDesc()));
            } else {
                List<String> intentIdList = getIntentIdList().stream()
                        .filter(intentId -> dependentResource.getIntentIdNameMap().containsKey(intentId))
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(intentIdList)) {
                    throw new ComException(ComErrorCode.VALIDATE_ERROR, String.format("条件[%s]下意图id列表不能为空", getType().getDesc()));
                }
            }
            if (DialogFlowConditionOperationTypeEnum.ANY_HIT.equals(getOperation())) {
                if (Objects.isNull(getSubOperation())) {
                    throw new ComException(ComErrorCode.VALIDATE_ERROR, String.format("条件[%s]下任意一个命中操作不能为空", getType().getDesc()));
                }
                if (!Arrays.asList(DialogFlowConditionOperationTypeEnum.GREATER_OR_EQUAL, DialogFlowConditionOperationTypeEnum.LESS_OR_EQUAL, DialogFlowConditionOperationTypeEnum.CONSECUTIVE)
                        .contains(getSubOperation())) {
                    throw new ComException(ComErrorCode.VALIDATE_ERROR, String.format("条件[%s]下任意一个命中操作类型异常", getType().getDesc()));
                }
                if (Objects.isNull(getNumber())) {
                    throw new ComException(ComErrorCode.VALIDATE_ERROR, String.format("条件[%s]下任意一个命中数量不能为空", getType().getDesc()));
                }
            }
        }

        if (requireEntityTypeSet.contains(getType())) {
            if (CollectionUtils.isEmpty(getEntityIdList())) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, String.format("条件[%s]下实体不能为空", getType().getDesc()));
            } else {
                List<String> existList = getEntityIdList().stream()
                        .filter(item -> dependentResource.getEntityId2NameMap().containsKey(item))
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(existList)) {
                    throw new ComException(ComErrorCode.VALIDATE_ERROR, String.format("条件[%s]下实体不能为空", getType().getDesc()));
                }
            }
        }
        if (requirVarTypeSet.contains(getType())) {
            if (CollectionUtils.isEmpty(getVariableIdList())) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, String.format("条件[%s]下变量不能为空", getType().getDesc()));
            } else {
                List<String> existList = getVariableIdList().stream()
                        .filter(item -> dependentResource.getVariableIdNameMap().containsKey(item))
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(existList)) {
                    throw new ComException(ComErrorCode.VALIDATE_ERROR, String.format("条件[%s]下变量不能为空", getType().getDesc()));
                }
            }
        }
    }
}
