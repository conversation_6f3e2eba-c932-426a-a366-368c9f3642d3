package com.yiwise.dialogflow.entity.po;

import com.alibaba.excel.util.StringUtils;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.common.ApplicationConstant;
import com.yiwise.dialogflow.entity.bo.DependentResourceBO;
import com.yiwise.dialogflow.entity.enums.NodeTypeEnum;
import com.yiwise.dialogflow.entity.enums.StepTypeEnum;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

@Data
public class EntityAssignConfigPO extends NodeAssignConfigPO implements Serializable {

    /**
     * 赋值动作
     */
    private List<VarAssignActionItemPO> assignActionList;

    /**
     * 在调整回当前节点后, 继续信息收集
     */
    private Boolean enableCollectOnPullback;


    @Override
    public List<String> getDependentEntityIdList() {
        if (CollectionUtils.isEmpty(assignActionList)) {
            return new ArrayList<>();
        }

        return assignActionList.stream()
                .map(VarAssignActionItemPO::getEntityId)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
    }

    @Override
    public List<String> getDependIntentIdList() {
        return new ArrayList<>();
    }

    @Override
    public List<String> getDependVariableIdList() {
        if (CollectionUtils.isEmpty(assignActionList)) {
            return new ArrayList<>();
        }

        return assignActionList.stream()
                .map(VarAssignActionItemPO::getVariableId)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
    }

    @Override
    public void mappingIntentId(Map<String, String> oldIntentId2newIntentIdMap) {

    }

    @Override
    public void mappingVariableId(Map<String, String> oldId2newIdMap) {
        if (CollectionUtils.isEmpty(assignActionList)) {
            return;
        }
        assignActionList.forEach(item -> item.setVariableId(oldId2newIdMap.get(item.getVariableId())));
    }

    @Override
    public void mappingEntityId(Map<String, String> oldId2newIdMap) {
        if (CollectionUtils.isEmpty(assignActionList)) {
            return;
        }
        assignActionList.forEach(item -> item.setEntityId(oldId2newIdMap.get(item.getEntityId())));
    }

    @Override
    public void validateConfig(StepPO step, DependentResourceBO resource, DialogBaseNodePO node) {
        super.validateConfig(step, resource, node);
        if (CollectionUtils.isEmpty(assignActionList)) {
            return;
        }
        for (VarAssignActionItemPO item : assignActionList) {
            if (StringUtils.isBlank(item.getVariableId())) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "动态变量赋值配置不完整, 变量不能为空");
            }
            if (StringUtils.isBlank(item.getEntityId())) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "动态变量赋值配置不完整, 实体不能为空");
            }
        }
        if (BooleanUtils.isTrue(getEnableCollectOnPullback())
                && Objects.nonNull(step)
                && StepTypeEnum.MAIN.equals(step.getType())) {
            if (!(node instanceof DialogChatNodePO)) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, String.format("节点动态变量赋值开启了[跳转后返回，若赋值失败则重复本节点], 但是节点类型不是[%s]", NodeTypeEnum.CHAT.getDesc()));
            }
            DialogChatNodePO chatNode = (DialogChatNodePO) node;
            if (CollectionUtils.isNotEmpty(chatNode.getSelectIntentIdList())
                    && !chatNode.getSelectIntentIdList().contains(ApplicationConstant.COLLECT_SUCCESS_INTENT_ID)) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "节点动态变量赋值开启了[跳转后返回，若赋值失败则重复本节点], 但是没有选择采集成功分支");
            }
        }
    }

    @Override
    public String toDisplayString(DependentResourceBO resource) {
        if (CollectionUtils.isEmpty(assignActionList)) {
            return "";
        }

        String text = this.getAssignActionList().stream()
                .map(item -> {
                    String entityName = resource.getEntityId2NameMap().getOrDefault(item.getEntityId(), "未知");
                    String variableName = resource.getVariableIdNameMap().getOrDefault(item.getVariableId(), "未知");
                    return String.format("%s保存到变量:%s", entityName, variableName);
                }).collect(Collectors.joining("; "));
        return String.format("【实体抽取结果: %s, 跳转后返回，若赋值失败则重复本节点:%s】",
                text, BooleanUtils.isTrue(this.getEnableCollectOnPullback()) ? "已启用": "未启用");
    }

}
