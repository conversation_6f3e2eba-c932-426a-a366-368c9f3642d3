package com.yiwise.dialogflow.entity.bo.analyze;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AnalyzeTemplateWhiteData implements Serializable {

    List<String> data;

    @JsonProperty("is_builtin")
    Boolean isBuiltin;

    String father;
}
