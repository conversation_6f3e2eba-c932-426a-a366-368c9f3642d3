package com.yiwise.dialogflow.entity.po.botgenerate;

import com.yiwise.base.model.bean.po.BaseTimeUserIdPO;
import com.yiwise.dialogflow.entity.enums.BotGenerateRecordStatusEnum;
import lombok.Data;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;

/**
 * bot生成记录
 */
@Data
@Document(collection = BotGenerateRecordPO.COLLECTION_NAME)
@CompoundIndexes(
        @CompoundIndex(name = "idx_templateId", def = "{templateId: 1}")
)
public class BotGenerateRecordPO extends BaseTimeUserIdPO {
    public static final String COLLECTION_NAME = "botGenerateRecord";

    private String id;

    /**
     * 标题, 就是模板的名称
     */
    private String title;

    /**
     * 创建人, 就是生成记录
     */
    private String author;

    /**
     * 生成的botId
     */
    private Long generateBotId;

    /**
     * 模板id
     */
    private String templateId;

    /**
     * 提交给算法侧的参数
     */
    private String params;

    /**
     * 算法侧返回的结果
     */
    private String originResult;

    private BotGenerateRecordStatusEnum status;

    private String failReason;

    private String completedPercent;

    private LocalDateTime completeTime;

}
