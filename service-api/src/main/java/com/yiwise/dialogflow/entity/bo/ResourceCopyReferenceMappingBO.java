package com.yiwise.dialogflow.entity.bo;

import com.yiwise.dialogflow.entity.enums.ConditionVarTypeEnum;
import com.yiwise.dialogflow.entity.po.*;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Consumer;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Data
public class ResourceCopyReferenceMappingBO {
    public final Map<String, String> knowledgeIdMapping = new HashMap<>();
    public final Map<String, String> stepIdMapping = new HashMap<>();
    public final Map<String, String> nodeIdMapping = new HashMap<>();
    public final Map<String, String> intentIdMapping = new HashMap<>();
    public final Map<String, String> variableIdMapping = new HashMap<>();
    public final Map<String, String> specialAnswerIdMapping = new HashMap<>();
    public final Map<String, String> groupIdMapping = new HashMap<>();
    public final Map<String, String> entityIdMapping = new HashMap<>();
    public final Map<String, String> entityConfigIdMapping = new HashMap<>();
    public final Map<String, String> answerUrlMapping = new ConcurrentHashMap<>();
    public final Map<String, String> llmStepConfigIdMapping = new ConcurrentHashMap<>();
    public final Map<String, String> llmLabelIdMapping = new ConcurrentHashMap<>();

    public String mapVarId(String srcVarId){
        return Optional.ofNullable(srcVarId).map(variableIdMapping::get).orElse(null);
    }

    public String mapIntentId(String intentId) {
        return Optional.ofNullable(intentId).map(intentIdMapping::get).orElse(null);
    }

    private ResourceCopyReferenceMappingBO baseMapId(Supplier<String> oldVariableIdSupplier,
                                                     Consumer<String> newVariableIdUpdater,
                                                     Map<String, String> idMapping) {
        String oldVarId = oldVariableIdSupplier.get();
        if (StringUtils.isBlank(oldVarId)) {
            newVariableIdUpdater.accept(null);
        } else {
            newVariableIdUpdater.accept(idMapping.getOrDefault(oldVariableIdSupplier.get(), null));
        }
        return this;
    }

    public ResourceCopyReferenceMappingBO mapVarId(Supplier<String> oldVariableIdSupplier,
                                                   Consumer<String> newVariableIdUpdater) {
        return baseMapId(oldVariableIdSupplier, newVariableIdUpdater, variableIdMapping);
    }

    public ResourceCopyReferenceMappingBO mapEntityId(Supplier<String> oldVariableIdSupplier,
                                                      Consumer<String> newVariableIdUpdater) {
        return baseMapId(oldVariableIdSupplier, newVariableIdUpdater, entityIdMapping);
    }

    public ResourceCopyReferenceMappingBO mapAssignConfig(ConstantAssignConfigPO constantAssign,
                                                          EntityAssignConfigPO entityAssign,
                                                          OriginInputAssignConfigPO originInputAssign) {
        if (Objects.nonNull(constantAssign)) {
            constantAssign.mappingVariableId(variableIdMapping);
        }
        if (Objects.nonNull(entityAssign)) {
            entityAssign.mappingVariableId(variableIdMapping);
            entityAssign.mappingEntityId(entityIdMapping);
        }
        if (Objects.nonNull(originInputAssign)) {
            originInputAssign.mappingVariableId(variableIdMapping);
            originInputAssign.mappingEntityId(entityIdMapping);
            originInputAssign.mappingIntentId(intentIdMapping);
        }
        return this;
    }
    public ResourceCopyReferenceMappingBO mapAssignConfig(KnowledgeAssignConfigPO assignConfig) {
        if (Objects.nonNull(assignConfig)) {
            mapVarId(assignConfig::getVariableId, assignConfig::setVariableId);
            mapEntityId(assignConfig::getEntityId, assignConfig::setEntityId);
        }
        return this;
    }

    public Set<String> mapVarSet(Set<String> srcSet){
        if (CollectionUtils.isNotEmpty(srcSet)){
            return srcSet.stream().map(this::mapVarId)
                    .filter(Objects::nonNull).collect(Collectors.toSet());
        }
        return Collections.emptySet();
    }

    /**
     * 更新答案中的变量id
     */
    public void mapConditionGroupVarId(BaseConditionGroup conditionGroup) {
        mapConditionGroupVarId(conditionGroup.getConditionList());
    }

    private void mapConditionGroupVarId(List<List<ConditionExpressionPO>> conditionList) {
        // 更新条件列表关联的变量id
        if (CollectionUtils.isNotEmpty(conditionList)){
            for (List<ConditionExpressionPO> andConditionList : conditionList) {
                for (ConditionExpressionPO condition : andConditionList) {
                    condition.setPreVarId(mapVarId(condition.getPreVarId()));
                    condition.setPostVarId(mapVarId(condition.getPostVarId()));
                    List<String> intentIdList = condition.getIntentIdList();
                    if (CollectionUtils.isNotEmpty(intentIdList)) {
                        condition.setIntentIdList(intentIdList.stream().map(this::mapIntentId).filter(Objects::nonNull).collect(Collectors.toList()));
                    }
                }
            }
        }
    }

    public void mapConditionBranch(NodeConditionBranchPO branch) {
        mapConditionGroupVarId(branch);
        if (BooleanUtils.isTrue(branch.getEnableAction()) && CollectionUtils.isNotEmpty(branch.getActionList())) {
            for (BranchActionPO branchAction : branch.getActionList()) {
                branchAction.setVarId(mapVarId(branchAction.getVarId()));
                if (ConditionVarTypeEnum.isVar(branchAction.getVarType())) {
                    branchAction.setValue(mapVarId(branchAction.getValue()));
                }
            }
        }
    }

    public void mappingMismatch(Mismatch mismatch) {
        mismatch.mapMismatch(stepIdMapping, knowledgeIdMapping, specialAnswerIdMapping);
    }

    public void mappingUninterrupted(Uninterrupted uninterrupted) {
        uninterrupted.mapUninterrupted(stepIdMapping, knowledgeIdMapping, specialAnswerIdMapping, intentIdMapping);
    }

    public void mappingLlmStepVariableAssign(LlmStepVariableAssign assign) {
        assign.mapLlmStepVariableAssign(variableIdMapping);
    }
}
