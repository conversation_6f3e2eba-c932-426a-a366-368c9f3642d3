package com.yiwise.dialogflow.entity.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2023/8/8
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class IntentRuleActionSourceIdNamePair extends IdNamePair<Long,String> {
    /**
     * 黑名单是否同步
     */
    private Boolean sharedCustomers;

    public IntentRuleActionSourceIdNamePair(Long id, String name, Boolean sharedCustomers) {
        super(id, name);
        this.sharedCustomers = sharedCustomers;
    }

    public static IntentRuleActionSourceIdNamePair of(Long id, String name, Boolean sharedCustomers) {
        return new IntentRuleActionSourceIdNamePair(id, name, sharedCustomers);
    }
}
