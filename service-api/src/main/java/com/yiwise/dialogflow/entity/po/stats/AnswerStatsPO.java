package com.yiwise.dialogflow.entity.po.stats;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@Data
public class AnswerStatsPO extends BaseStatsPO {
    String answerTemplate;
    String answerSource;
    String answerTemplateHash;
    String stepId;
    String nodeId;
    String knowledgeId;
    String specialAnswerConfigId;
    int totalHangupCount;
    int declineCount;
    int declineCallCount;

    public String getAnswerTemplate() {
        return StringUtils.trimToEmpty(answerTemplate);
    }
}
