package com.yiwise.dialogflow.entity.po;

import com.yiwise.base.model.bean.po.BaseTimeUserIdPO;
import com.yiwise.dialogflow.entity.po.asrmodel.AsrErrorCorrectionDetailPO;
import com.yiwise.dialogflow.entity.po.intent.*;
import com.yiwise.dialogflow.entity.po.llm.RagDocumentPO;
import com.yiwise.dialogflow.entity.po.llm.RagDocumentSegmentPO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.Id;

import java.util.*;

/**
 * 只保存原始数据, 这样后期在前端对快照进行预览的时候, 就不需要进行转换了, 直接就可以返回给前端进行展示
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class RobotSnapshotPO extends BaseTimeUserIdPO {

    public static final String COLLECTION_NAME = "robotSnapshot";

    public static final String TEXT_TRAIN_COLLECTION_NAME = "robotSnapshot_textTrain";

    // 名字未统一
    public static final String SPEECH_TRAIN_COLLECTION_NAME = "robotSnapshot_speechTrain";

    @Id
    String id;

    /**
     * 租户id, 可以用来表示该快照是绑定给哪个租户时创建的
     * 因为变量绑定客户属性的需求, 快照现在也和租户关联了的
     */
    Long tenantId;

    Long botId;

    Integer version;

    /**
     * 是否发布
     */
    Boolean isPublish;

    /**
     * bot
     */
    BotPO bot;

    /**
     * 意图列表
     */
    List<IntentPO> intentList;

    /**
     * 意图语料列表
     */
    List<IntentCorpusPO> intentCorpusList;

    /**
     * 流程列表
     */
    List<StepPO> stepList;

    /**
     * 知识库列表
     */
    List<KnowledgePO> knowledgeList;

    /**
     * 变量
     */
    List<VariablePO> variableList;

    /**
     * 画布节点
     */
    List<DialogBaseNodePO> nodeList;

    /**
     * 特殊语境配置列表
     */
    List<SpecialAnswerConfigPO> specialAnswerConfigList;

    /**
     * 分组列表
     */
    List<GroupPO> groupList;

    /**
     * 机器人录音配置
     */
    BotAudioConfigPO botAudioConfig;

    BotConfigPO botConfig;
    /**
     * 意图配置
     */
    IntentConfigPO intentConfig;

    /**
     * 答案和录音映射列表
     */
    List<AnswerAudioMappingPO> answerAudioMappingList;

    List<IntentRulePO> intentRuleList;

    /**
     * 动作配置列表
     */
    List<IntentRuleActionPO> intentRuleActionList;

    Map<Integer, String> intentLevelDetailCode2NameMap;

    /**
     * 资源关联列表
     */
    List<SourceRefPO> sourceRefList;

    /**
     * asr纠错配置
     */
    AsrErrorCorrectionDetailPO asrErrorCorrectionDetail;

    /**
     * 已验证音频
     */
    Boolean validatedAudio;

    /**
     * 已验证动作规则
     */
    Boolean validatedActionRule;

    /**
     * 已验证外部资源, 短信, 标签等
     */
    Boolean validatedOutsideResource;

    /**
     * bot变量绑定的数据
     */
    List<VariableBindPO> variableBindList;

    /**
     * 非文本的客户属性名称
     */
    List<String> nonTextAttributeNameList;

    /**
     * 已使用的变量名列表
     */
    Set<String> usedVariableNameSet;

    /**
     * 所有实体列表
     */
    List<BaseEntityPO> entityList;

    /**
     * 可以播放的变量名列表(所有答案中用到的变量名, 不区分是自定义变量还是动态变量)
     */
    Set<String> playableVariableNameSet;

    EntityCollectConfigPO entityCollectConfig;

    public BotAudioConfigPO getBotAudioConfig() {
        if (Objects.nonNull(botConfig)) {
            return botConfig.getAudioConfig();
        }
        return botAudioConfig;
    }

    /**
     * 模板变量名称-默认值映射
     */
    Map<String, String> templateVarNameValueMap;

    /**
     * 算法标签列表
     */
    List<AlgorithmLabelPO> algorithmLabelList;

    /**
     * 文档知识列表
     */
    List<RagDocumentPO> ragDocumentList;

    /**
     * 文档知识分段列表
     */
    List<RagDocumentSegmentPO> ragDocumentSegmentList;

    /**
     * 大模型流程配置列表
     */
    List<LlmStepConfigPO> llmStepConfigList;

    /**
     * 大模型分类列表
     */
    List<LlmLabelPO> llmLabelList;
}
