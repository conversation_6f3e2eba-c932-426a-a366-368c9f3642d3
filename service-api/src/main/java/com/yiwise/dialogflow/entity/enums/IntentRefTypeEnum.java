package com.yiwise.dialogflow.entity.enums;

import com.yiwise.base.model.enums.CodeDescEnum;

/**
 * 资源关联类型
 * <AUTHOR>
 * @date 2022/2/14
 */
public enum IntentRefTypeEnum implements CodeDescEnum {
    STEP(1, "流程"),
    NODE(2, "节点"),
    KNOWLEDGE(3, "知识"),
    SPECIAL_ANSWER(4, "特殊语境"),
    COMPOSITE_INTENT(5, "组合意图"),

    /**
     * 目前仅延迟挂机特殊语境用到
     */
    SPECIAL_ANSWER_EXCLUDE(6, "特殊语境不关联意图"),
    ENTITY(7, "实体"),
    GLOBAL_ENTITY_COLLECT(8, "全局采集"),
    JUDGE_NODE_BRANCH(9, "判断节点分支"),
    ORIGIN_INPUT(10, "原话采集"),
    SKIP_CONDITION(11, "跳过条件"),
    LLM_STEP_CONFIG(12, "大模型流程配置"),

    SINGLE_INTENT(13, "意图"),
    TEMPLATE_VARIABLE(14, "模板变量"),
    ;

    final String desc;
    final Integer code;
    IntentRefTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }
}
