package com.yiwise.dialogflow.entity.po.asrmodel;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yiwise.base.model.bean.po.BaseTimeUserIdPO;
import com.yiwise.dialogflow.entity.enums.AsrSourceConcurrencyRecordTypeEnum;
import lombok.Data;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2022/10/27 11:35:51
 */
@Data
@Document(collection = "asrSourceConcurrencyRecord")
public class AsrSourceConcurrencyRecordPO implements Serializable {
    public static final String COLLECTION_NAME = "asrSourceConcurrencyRecord";

    /**
     * 记录id
     */
    @Indexed
    private String asrSourceConcurrencyRecordId;

    /**
     * 资源id
     */
    private Long sourceId;

    /**
     * 调用次数
     */
    private Integer count;

    /**
     * 资源类型
     */
    private AsrSourceConcurrencyRecordTypeEnum sourceType;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    protected LocalDate createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    protected LocalDate updateTime;

}