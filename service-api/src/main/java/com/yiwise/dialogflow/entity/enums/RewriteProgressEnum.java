package com.yiwise.dialogflow.entity.enums;

import com.yiwise.base.model.enums.CodeDescEnum;

/**
 * bot答案改写进度
 * 1. 已获取所有待改写的答案列表
 * 2. 已请求算法的实体识别接口
 * 3. 答案改写中
 * 4. 答案改写完成
 */
public enum RewriteProgressEnum implements CodeDescEnum {
    READY(0, "准备中"),
    ANALYZED(1, "已分析完成"),
    REWRITING(2, "答案改写中"),
    REWRITE(3, "答案已改写完成"),
    APPLY(4, "已应用"),
    DONE(5, "已完成")
    ;

    final Integer code;
    final String desc;
    RewriteProgressEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }
}
