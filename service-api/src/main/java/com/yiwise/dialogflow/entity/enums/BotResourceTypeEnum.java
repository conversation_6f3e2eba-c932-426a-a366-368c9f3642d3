package com.yiwise.dialogflow.entity.enums;

import com.yiwise.base.model.enums.CodeDescEnum;

public enum BotResourceTypeEnum implements CodeDescEnum {
    MAIN_STEP(0, "主流程"),
    INDEPENDENT_STEP(1, "独立流程流程"),
    KNOWLEDGE(2, "知识"),
    SPECIAL_ANSWER_CONFIG(3,"特殊语境"),
    INTENT(4, "意图"),
    VARIABLE(5, "变量"),
    AUDIO(6, "音频"),
    AUDIO_CONFIG(7, "音频设置"),
    SPEECH_CONFIG(8, "语音设置"),
    INTENT_LEVEL_RULE(9, "意向等级规则"),
    INTENT_ACTION_RULE(10,"意向动作规则"),
    NODE(11,"节点"),
    INTENT_MODEL(12, "意图模型"),
    BOT_BIND_INFO(13, "绑定归属"),
    ENTITY(14, "实体库"),
    ENTITY_COLLECT_CONFIG(15, "实体库"),
    RAG_DOC(16, "文档知识"),
    LLM_STEP(17, "大模型流程"),
    TRANSFER_ARTIFICIAL(18, "转人工"),

    TIP(99, "提示"),

    ;

    Integer code;
    String desc;
    BotResourceTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }
}
