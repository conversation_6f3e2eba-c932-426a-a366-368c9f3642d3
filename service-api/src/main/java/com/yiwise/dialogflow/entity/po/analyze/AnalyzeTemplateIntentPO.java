package com.yiwise.dialogflow.entity.po.analyze;

import com.yiwise.dialogflow.entity.enums.AnalyzeTemplateIntentCategoryEnum;
import com.yiwise.dialogflow.entity.enums.AnalyzeTemplateIntentTypeEnum;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 */
@Data
@Document(collection = AnalyzeTemplateIntentPO.COLLECTION_NAME)
@CompoundIndexes(
        @CompoundIndex(name = "idx_templateId_name", def = "{templateId:1, name: 1}")
)
public class AnalyzeTemplateIntentPO {

    public static final String COLLECTION_NAME = "analyzeTemplateIntent";

    @Id
    private String id;

    /**
     * 模板id
     */
    private String templateId;

    /**
     * 意图名称
     */
    private String name;

    /**
     * 意图类型
     */
    private AnalyzeTemplateIntentTypeEnum type;

    /**
     * 意图分类
     */
    private AnalyzeTemplateIntentCategoryEnum category;

    /**
     * 上级意图id
     */
    private String parentIntentId;

    /**
     * 意图中的语料数量
     */
    private Long corpusSize;

    /**
     * 源意图id
     */
    private String srcIntentId;
}
