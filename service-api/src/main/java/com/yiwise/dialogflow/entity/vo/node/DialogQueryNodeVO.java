package com.yiwise.dialogflow.entity.vo.node;

import com.yiwise.dialogflow.engine.share.DialogQueryNodeHttpParamInfo;
import com.yiwise.dialogflow.entity.enums.QueryNodeApiTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.http.HttpMethod;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DialogQueryNodeVO extends DialogChatNodeVO implements Serializable {

    /**
     * 请求方式,GET POST
     */
    HttpMethod httpMethod;

    /**
     * 接口地址
     */
    String url;

    /**
     * query
     */
    List<DialogQueryNodeHttpParamInfo> queryList;

    /**
     * header
     */
    List<DialogQueryNodeHttpParamInfo> headerList;

    /**
     * body
     */
    String body;

    /**
     * <动态变量id,jsonPath表达式>
     */
    Map<String, String> resMap;

    /**
     * 超时时间，单位秒
     */
    Integer timeout;

    /**
     * 记录失败或者超时结果的动态变量id
     */
    String errorVarId;

    /**
     * 接口类型, 外部/内部接口
     */
    QueryNodeApiTypeEnum apiType;

    /**
     * 内置api名称
     */
    String builtInApiName;
}
