package com.yiwise.dialogflow.entity.vo.smsGenerate;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SmsGenerateDeleteVO implements Serializable {

    /**
     * 话术id
     */
    @NotNull(message = "botId不能为空")
    Long botId;

    /**
     * id列表
     */
    List<String> idList;

    /**
     * 删除全部,兼容前端
     */
    Boolean deleteAll;
}
