package com.yiwise.dialogflow.entity.query.botgenerate;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yiwise.base.model.bean.vo.AbstractQueryVO;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class BotGenerateTemplateQueryVO extends AbstractQueryVO {

    /**
     * 搜索内容
     */
    private String search;

    /**
     * 开始更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd", iso = DateTimeFormat.ISO.DATE)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate beginUpdateTime;

    /**
     * 结束更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd", iso = DateTimeFormat.ISO.DATE)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate endUpdateTime;

    /**
     * 开始创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd", iso = DateTimeFormat.ISO.DATE)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate beginCreateTime;

    /**
     * 结束创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd", iso = DateTimeFormat.ISO.DATE)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate endCreateTime;

    /**
     * 创建人id列表
     */
    private List<Long> createUserIdList;
}
