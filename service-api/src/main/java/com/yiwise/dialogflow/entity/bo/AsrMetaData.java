package com.yiwise.dialogflow.entity.bo;

import com.yiwise.dialogflow.entity.po.BotPO;
import com.yiwise.dialogflow.entity.po.asrmodel.*;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/11/1
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AsrMetaData implements Serializable {

    BotPO botPO;

    AsrProviderPO asrProviderPO;

    AsrVocabDetailPO asrVocabDetailPO;

    AsrSelfLearningDetailPO asrSelfLearningDetailPO;

    AsrErrorCorrectionDetailPO asrErrorCorrectionDetailPO;

    AsrVocabProviderRelationPO asrVocabProviderRelationPO;

    AsrSelfLearningProviderRelationPO asrSelfLearningProviderRelationPO;

    /**
     * 反应灵敏度
     */
    Integer maxSentenceSilence;

    /**
     * 反应灵敏度等级
     */
    Integer maxSentenceSilenceLevel;

    private Boolean enableAsrDelayStart;

    private Double asrDelayStartSeconds;

}
