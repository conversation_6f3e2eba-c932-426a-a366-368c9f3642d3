package com.yiwise.dialogflow.entity.dto.llmchat;

import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Data
public class LLMChatResponse {
    /**
     * 响应状态码, 0: 成功, 否则失败
     */
    int code;

    /**
     * 响应的文本内容
     */
    String response;

    /**
     * 模型是否回答
     */
    Boolean isResponded;

    /**
     * 流程是否完成, 用来标识大模型流程任务完成状态
     */
    Boolean isFinished;

    List<ResponseTool> tools;

    public List<ResponseTool> getTools() {
        if (CollectionUtils.isEmpty(tools)) {
            return tools;
        }
        return tools.stream()
                .filter(item -> item.getName().startsWith("tool-"))
                .collect(Collectors.toList());
    }

    Map<String, String> collectInfo;

    public Map<String, String> getCollectInfo() {
        if (MapUtils.isEmpty(collectInfo)) {
            return collectInfo;
        }
        Map<String, String> tmp = new HashMap<>();
        collectInfo.forEach((k, v) -> {
            if (StringUtils.isNotBlank(k) && StringUtils.isNotBlank(v)) {
                tmp.put(k, v);
            }
        });
        collectInfo = tmp;
        return collectInfo;
    }

    String trackInfo;

    boolean isLastResponse;
}
