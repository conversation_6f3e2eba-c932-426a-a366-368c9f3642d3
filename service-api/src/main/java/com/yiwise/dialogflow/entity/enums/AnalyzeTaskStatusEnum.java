package com.yiwise.dialogflow.entity.enums;

import com.yiwise.base.model.enums.CodeDescEnum;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public enum AnalyzeTaskStatusEnum implements CodeDescEnum {

    /**
     * 排队中
     */
    QUEUED(0, "排队中"),

    /**
     * 进行中
     */
    RUNNING(1, "进行中"),

    /**
     * 已完成
     */
    FINISHED(2, "已完成"),

    /**
     * 失败
     */
    FAILED(3, "失败"),
    ;

    private final Integer code;
    private final String desc;

    AnalyzeTaskStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static Boolean isFailed(AnalyzeTaskStatusEnum status) {
        return FAILED.equals(status);
    }

    public static Boolean isRunning(AnalyzeTaskStatusEnum status) {
        return RUNNING.equals(status);
    }
}
