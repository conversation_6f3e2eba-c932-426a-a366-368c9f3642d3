package com.yiwise.dialogflow.entity.po;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

/**
 * 实体提取配置(全局)
 */
@Data
@Document(EntityCollectConfigPO.COLLECTION_NAME)
public class EntityCollectConfigPO {

    public static final String COLLECTION_NAME = "entityCollectConfig";

    /**
     * 实体id
     */
    @Id
    private String id;

    /**
     * botId
     */
    private Long botId;

    /**
     * 开启全局采集
     */
    private Boolean enableGlobalCollect;

    /**
     * 全局采集实体和变量的映射关系列表
     */
    private List<EntityVariableMapping> entityVariableMappingList;

    @Data
    public static class EntityVariableMapping {

        /**
         * 实体id
         */
        String entityId;

        /**
         * 变量id
         */
        String variableId;

        /**
         * 实体名称
         */
        String entityName;

        /**
         * 变量名称
         */
        String variableName;
    }

}
