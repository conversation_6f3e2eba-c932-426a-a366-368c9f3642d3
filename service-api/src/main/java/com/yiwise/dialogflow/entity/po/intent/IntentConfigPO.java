package com.yiwise.dialogflow.entity.po.intent;

import com.yiwise.base.model.bean.po.BaseTimeUserIdPO;
import com.yiwise.dialogflow.common.MongoCollectionNameCenter;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 * @date 2022/2/14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@FieldDefaults(level = AccessLevel.PRIVATE)
@Document(collection = MongoCollectionNameCenter.INTENT_CONFIG)
public class IntentConfigPO extends BaseTimeUserIdPO {

    /**
     * 意图ID
     */
    @Id
    String id;

    /**
     * 话术ID
     */
    @Indexed(unique = true)
    Long botId;

    /**
     * 算法开关
     */
    Boolean enableAlgorithm;

    /**
     * 算法介入字数
     */
    Integer algorithmInterventionCount;

    /**
     * 算法阈值上限
     */
    Double algorithmUpperThreshold;

    /**
     * 算法阈值下限
     */
    Double algorithmLowerThreshold;
}
