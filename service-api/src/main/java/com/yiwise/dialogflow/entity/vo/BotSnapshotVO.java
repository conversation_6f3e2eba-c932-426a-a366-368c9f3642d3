package com.yiwise.dialogflow.entity.vo;

import com.yiwise.dialogflow.entity.bo.SnapshotInvalidFailItemMsg;
import com.yiwise.dialogflow.entity.enums.AuditStatusEnum;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> @ yiwise . com>
 * @version v0.1 2022/7/14
 * @class <code>BotSnapshotVO</code>
 * @see
 * @since JDK1.8
 */
@Data
public class BotSnapshotVO {
    private AuditStatusEnum auditStatusEnum;
    private Long botId;
    private String errorMsg;
    private List<SnapshotInvalidFailItemMsg> snapshotInvalidFailItemMsgList;

    /**
     * 是否需要绑定变量
     */
    private Boolean needBindVariable;

    /**
     * 变量绑定信息列表
     */
    private List<VariableBindInfoVO> variableBindInfoList;

}
