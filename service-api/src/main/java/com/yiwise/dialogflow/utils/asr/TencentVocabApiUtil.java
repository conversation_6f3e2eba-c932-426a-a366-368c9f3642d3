package com.yiwise.dialogflow.utils.asr;

import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.asr.v20190614.AsrClient;
import com.tencentcloudapi.asr.v20190614.models.*;
import com.yiwise.base.common.utils.PropertyLoaderUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/11/1 16:48:11
 */
public class TencentVocabApiUtil {
    private static final Logger logger = LoggerFactory.getLogger(TencentVocabApiUtil.class);


    private static final String SecretId = PropertyLoaderUtils.getProperty("tencent.asr.secretId");
    private static final String SecretKey = PropertyLoaderUtils.getProperty("tencent.asr.secretKey");
    private static Credential cred;
    private static String endPoint = "asr.tencentcloudapi.com";

    static {
        cred = new Credential(SecretId, SecretKey);
    }

    public static void delete(String vocabId) {
        try {
            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint(endPoint);
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);
            AsrClient client = new AsrClient(cred, "", clientProfile);
            DeleteAsrVocabRequest req = new DeleteAsrVocabRequest();
            req.setVocabId(vocabId);
            DeleteAsrVocabResponse resp = client.DeleteAsrVocab(req);
            logger.debug("【腾讯】删除热词成功，vocabId:{}", vocabId);
        } catch (TencentCloudSDKException e) {
            logger.error("【腾讯】删除热词失败,vocabId:{},错误:{}", vocabId, e.getLocalizedMessage());
        }
    }

    public static void update(String vocabId, List<String> content) {
        try {
            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint(endPoint);
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);
            AsrClient client = new AsrClient(cred, "", clientProfile);
            UpdateAsrVocabRequest req = new UpdateAsrVocabRequest();
            req.setVocabId(vocabId);
            req.setWordWeights(listToHotWord(content));
            UpdateAsrVocabResponse resp = client.UpdateAsrVocab(req);
            logger.debug("【腾讯】更新热词成功,vocabId:{}", vocabId);
        } catch (TencentCloudSDKException e) {
            logger.error("【腾讯】更新热词失败,vocabId:{},错误:{}", vocabId, e);
        }
    }

    public static String create(String name, String description, List<String> content) {
        try {
            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint(endPoint);
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);
            AsrClient client = new AsrClient(cred, "", clientProfile);
            CreateAsrVocabRequest req = new CreateAsrVocabRequest();
            req.setName(name);
            req.setDescription(description);
            req.setWordWeights(listToHotWord(content));
            CreateAsrVocabResponse resp = client.CreateAsrVocab(req);
            if (Objects.nonNull(resp)) {
                return resp.getVocabId();
            }
            logger.debug("【腾讯】创建热词成功,name:{}", name);
        } catch (TencentCloudSDKException e) {
            logger.error("【腾讯】创建热词失败,name:{},错误:{}", name, e);
        }
        return null;
    }

    private static HotWord[] listToHotWord(List<String> content) {
        if (CollectionUtils.isNotEmpty(content)) {
            HotWord[] hotWords = new HotWord[content.size()];
            for (int i = 0; i < content.size(); i++) {
                String[] split = content.get(i).split("：");
                HotWord hotWord = new HotWord();
                hotWord.setWord(split[0]);
                hotWord.setWeight(Long.valueOf(split[1]));
                hotWords[i] = hotWord;
            }
            return hotWords;
        } else {
            return null;
        }
    }
}