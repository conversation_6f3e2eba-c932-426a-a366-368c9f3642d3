package com.yiwise.dialogflow.utils.asr;

import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.asr.v20190614.AsrClient;
import com.tencentcloudapi.asr.v20190614.models.*;
import com.yiwise.base.common.utils.PropertyLoaderUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Objects;

/**
 * sdk参考：https://console.cloud.tencent.com/api/explorer?Product=asr&Version=2019-06-14&Action=ModifyCustomizationState
 *
 * <AUTHOR>
 * @date 2022/11/2 16:44:09
 */
public class TencentSelfLearningApiUtil {

    private static final Logger logger = LoggerFactory.getLogger(TencentSelfLearningApiUtil.class);


    private static final String SecretId = PropertyLoaderUtils.getProperty("tencent.asr.secretId");
    private static final String SecretKey = PropertyLoaderUtils.getProperty("tencent.asr.secretKey");
    private static Credential cred;
    private static String endPoint = "asr.tencentcloudapi.com";
    private static String modelType = "8k";

    static {
        cred = new Credential(SecretId, SecretKey);
    }

    public static String create(String modelName, String tencentModelName, String url) {
        try {
            if (StringUtils.isNotEmpty(url) && !url.contains("https://")) {
                url = "https://" + url;
            }
            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint(endPoint);
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);
            AsrClient client = new AsrClient(cred, "", clientProfile);
            CreateCustomizationRequest req = new CreateCustomizationRequest();
            req.setModelName(tencentModelName);
            req.setTextUrl(url);
            req.setModelType(modelType);
            CreateCustomizationResponse resp = client.CreateCustomization(req);
            logger.debug("【腾讯】创建自学习模型：{}，成功", modelName);
            return resp.getModelId();
        } catch (TencentCloudSDKException e) {
            logger.error("【腾讯】创建自学习模型:{},出错：{}", modelName, e.getLocalizedMessage());
        }
        return null;
    }

    public static void update(String modelId, String url) {
        try {
            if (StringUtils.isNotEmpty(url) && !url.contains("https://")) {
                url = "https://" + url;
            }
            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint(endPoint);
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);
            AsrClient client = new AsrClient(cred, "", clientProfile);
            ModifyCustomizationRequest req = new ModifyCustomizationRequest();
            req.setModelId(modelId);
            req.setModelType(modelType);
            req.setTextUrl(url);
            ModifyCustomizationResponse resp = client.ModifyCustomization(req);
            logger.debug("【腾讯】更新自学习模型成功,modelId:{}", modelId);
        } catch (TencentCloudSDKException e) {
            logger.error("【腾讯】更新自学习模型出错：modelId:{},错误:{}", modelId, e.getLocalizedMessage());
        }
    }

    /**
     * @param modelId
     * @param status(-1线下；1上线)
     */
    public static void updateStatus(String modelId, Long status) {
        ModifyCustomizationStateResponse resp = null;
        try {
            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint(endPoint);
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);
            AsrClient client = new AsrClient(cred, "", clientProfile);
            ModifyCustomizationStateRequest req = new ModifyCustomizationStateRequest();
            req.setModelId(modelId);
            req.setToState(status);
            resp = client.ModifyCustomizationState(req);
            if (Objects.nonNull(status) && status == 1l) {
                logger.debug("【腾讯】上线自学习模型成功，modelId:{}", modelId);
            } else if (Objects.nonNull(status) && status == -1l) {
                logger.debug("【腾讯】下线自学习模型成功，modelId:{}", modelId);
            }
        } catch (TencentCloudSDKException e) {
            logger.error("【腾讯】更新自学习模型状态失败,modelId:{},错误：{}", modelId, e.getLocalizedMessage());
        }
    }

    public static void delete(String modelId) {
        try {
            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint(endPoint);
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);
            AsrClient client = new AsrClient(cred, "", clientProfile);
            DeleteCustomizationRequest req = new DeleteCustomizationRequest();
            req.setModelId(modelId);
            DeleteCustomizationResponse resp = client.DeleteCustomization(req);
            logger.debug("【腾讯】删除自学习模型成功,modelId:{}", modelId);
        } catch (TencentCloudSDKException e) {
            logger.error("【腾讯】删除自学习模型失败,modelId:{},错误:{}", modelId, e.getLocalizedMessage());
        }
    }

    public static Model[] list(Integer pageSize) {
        try {
            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint(endPoint);
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);
            AsrClient client = new AsrClient(cred, "", clientProfile);
            GetCustomizationListRequest req = new GetCustomizationListRequest();
            req.setLimit(Long.valueOf(pageSize));
            GetCustomizationListResponse resp = client.GetCustomizationList(req);
            return resp.getData();
        } catch (TencentCloudSDKException e) {
            logger.error("【腾讯】查询自学习模型错误：{}", e.getLocalizedMessage());
        }
        return null;
    }
}