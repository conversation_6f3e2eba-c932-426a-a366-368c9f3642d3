package com.yiwise.dialogflow.engine.share.action;

import com.yiwise.dialogflow.engine.share.enums.ActionScopeEnum;
import com.yiwise.dialogflow.engine.share.enums.ActionTypeEnum;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class WaitAction extends ChatAction {

    /**
     * 用户等待无应答时长
     */
    final private Integer userSilenceMs;

    public WaitAction() {
        this(null);
    }

    public WaitAction(Integer userSilenceMs) {
        setScope(ActionScopeEnum.INTERACTION);
        setType(ActionTypeEnum.WAIT);
        this.userSilenceMs = userSilenceMs;
    }

}
