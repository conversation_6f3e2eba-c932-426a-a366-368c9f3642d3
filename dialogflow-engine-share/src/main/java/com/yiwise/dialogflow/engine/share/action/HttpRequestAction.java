package com.yiwise.dialogflow.engine.share.action;

import com.yiwise.dialogflow.engine.share.QueryNodeApiTestReq;
import com.yiwise.dialogflow.engine.share.enums.ActionScopeEnum;
import com.yiwise.dialogflow.engine.share.enums.ActionTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class HttpRequestAction extends ChatAction {

    private QueryNodeApiTestReq httpRequestInfo;

    private String httpRequestId;

    public HttpRequestAction(String httpRequestId, QueryNodeApiTestReq req, Map<String, String> varIdValueMap, Map<String, String> varNameValueMap) {
        setScope(ActionScopeEnum.INTERACTION);
        setType(ActionTypeEnum.HTTP_REQUEST);
        this.httpRequestInfo = req;
        this.httpRequestId = httpRequestId;
        httpRequestInfo.setVarIdValueMap(varIdValueMap);
        httpRequestInfo.setVarNameValueMap(varNameValueMap);
    }
}
