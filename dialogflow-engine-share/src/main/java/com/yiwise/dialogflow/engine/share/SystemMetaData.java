package com.yiwise.dialogflow.engine.share;

import lombok.Data;

import java.util.List;

@Data
public class SystemMetaData {

    /**
     * 运营商在通话前 N 秒增加的提示音正则匹配列表
     */
    private List<String> operatorPromptAudioRegexList;

    private List<String> operatorPromptAudioPreRegexList;

    private List<String> v2OperatorPromptAudioRegexList;
    /**
     * 仅在前 N 秒才检测信息, 如果是 < 1 则不检测
     */
    private Integer checkPromptAudioBeforeSeconds;

    private List<Long> disablePromptAudioCheckTenantIdList;

    private Integer resetTimeoutMs;

    private Integer userInputMergeIntervalMs;

    private Boolean defaultUseV2PromptAudioCheck;

    private List<Long> useV2PropmtAudioCheckTenantIdList;

    /**
     * 运营商提示音检测过程中, 如果命中这些正则, 即使可以同时命中提示音检测的正则或者当前处于暂停播放状态, 都需要送给对话引擎
     * 由对话引擎来处理
     */
    private List<String> promptAudioExcludeAiAssistantRegexList;
}
