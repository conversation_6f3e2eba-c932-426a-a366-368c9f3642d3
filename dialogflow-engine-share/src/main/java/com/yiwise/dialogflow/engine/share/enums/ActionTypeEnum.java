package com.yiwise.dialogflow.engine.share.enums;

import com.yiwise.base.model.enums.CodeDescEnum;

/**
 * <AUTHOR>
 */
public enum ActionTypeEnum implements CodeDescEnum {
    JUMP(0, "跳转"),
    HANGUP(1, "挂机"),
    WAIT(2, "等待用户应答"),
    PAUSE_PLAY(3, "暂停录音播放"),
    UNINTERRUPTED(4, "不可打断"),
    IGNORE_INPUT(5, "忽略用户输入"),
    ADD_WECHAT(6, "外呼中主动加微"),
    SWITCH_TO_HUMAN_SERVICE(7, "转人工"),
    WAIT_USER_SAY_FINISH(8, "等待用户说完"),
    HTTP_REQUEST(9, "调用Http接口"),
    SEND_SMS(10, "发送短信"),
    LLM_REQUEST(11, "大模型请求"),
    RESUME_PLAY(12, "继续播放"),
    HUMAN_INTERVENTION(13, "人工介入"),
    ;
    final String desc;
    final Integer code;
    ActionTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }
}
