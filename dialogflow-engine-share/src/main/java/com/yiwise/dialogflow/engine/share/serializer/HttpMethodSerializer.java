package com.yiwise.dialogflow.engine.share.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import org.springframework.http.HttpMethod;

import java.io.IOException;

public class HttpMethodSerializer extends JsonSerializer<HttpMethod> {
    @Override
    public void serialize(HttpMethod httpMethod, JsonGenerator gen, SerializerProvider provider)
            throws IOException {
        gen.writeString(httpMethod.name());
    }
}
