package com.yiwise.dialogflow.engine.share.enums;

import com.yiwise.base.model.enums.CodeDescEnum;

/**
 * <AUTHOR>
 */

public enum AnswerSourceEnum implements CodeDescEnum {
    STEP(1, "流程"),
    KNOWLEDGE(2, "问答知识"),
    SPECIAL_ANSWER(3, "特殊语境"),
    LLM_STEP(4, "大模型流程")
    ;

    Integer code;
    String desc;

    AnswerSourceEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }
}