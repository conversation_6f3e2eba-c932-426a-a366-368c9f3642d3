package com.yiwise.dialogflow.engine.share.enums;

import com.yiwise.base.model.enums.CodeDescEnum;

public enum RobotSnapshotUsageTargetEnum implements CodeDescEnum {
    CALL_OUT(0, "外呼"),
    SPEECH_TEST(1, "语音训练测试"),
    TEXT_TEST(2, "文本训练测试")
    ;

    private int code;
    private String desc;

    RobotSnapshotUsageTargetEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    public static boolean isText(RobotSnapshotUsageTargetEnum usageTarget) {
        return usageTarget == TEXT_TEST;
    }

    public static boolean isSpeech(RobotSnapshotUsageTargetEnum usageTarget) {
        return usageTarget == SPEECH_TEST || usageTarget == CALL_OUT;
    }

    public static boolean isTrainTest(RobotSnapshotUsageTargetEnum usageTarget) {
        return usageTarget == SPEECH_TEST || usageTarget == TEXT_TEST;
    }
}
