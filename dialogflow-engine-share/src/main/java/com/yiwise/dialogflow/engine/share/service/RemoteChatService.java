package com.yiwise.dialogflow.engine.share.service;

import com.yiwise.base.model.annotation.auth.InnerOnly;
import com.yiwise.base.model.annotation.auth.NoLogin;
import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.dialogflow.engine.share.CallDataInfo;
import com.yiwise.dialogflow.engine.share.IntentLevelAnalysisResult;
import com.yiwise.dialogflow.engine.share.request.ChatRequest;
import com.yiwise.dialogflow.engine.share.request.CreateSessionRequest;
import com.yiwise.dialogflow.engine.share.request.HttpRequest;
import com.yiwise.dialogflow.engine.share.response.ChatResponse;
import com.yiwise.dialogflow.engine.share.response.SessionInfo;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Map;

public interface RemoteChatService {
    @InnerOnly
    @NoLogin
    @PostMapping("/apiBot/v3/chatService/createSession")
    ResultObject<SessionInfo> createSession(@RequestBody CreateSessionRequest request);

    @InnerOnly
    @NoLogin
    @PostMapping("/apiBot/v3/chatService/processRequest")
    ResultObject<ChatResponse> processRequest(@RequestBody ChatRequest request);


    @InnerOnly
    @NoLogin
    @PostMapping("/apiBot/v3/chatService/analysis")
    ResultObject<IntentLevelAnalysisResult> analysis(@RequestBody CallDataInfo callDataInfo);

    @InnerOnly
    @NoLogin
    @PostMapping("/apiBot/v3/chatService/analysisInCall")
    ResultObject<IntentLevelAnalysisResult> analysisInCall(@RequestBody CallDataInfo callDataInfo);

    @InnerOnly
    @NoLogin
    @PostMapping("/apiBot/v3/chatService/httpRequest")
    ResultObject<Map<String, String>> httpRequest(@RequestBody HttpRequest req);
}
