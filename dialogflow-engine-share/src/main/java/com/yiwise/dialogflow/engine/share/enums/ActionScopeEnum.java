package com.yiwise.dialogflow.engine.share.enums;

import com.yiwise.base.model.enums.CodeDescEnum;

/**
 * action作用范围, 分为在引擎内的传递和传递到交互层的action
 * <AUTHOR>
 */
public enum ActionScopeEnum implements CodeDescEnum {

    ENGINE(0, "引擎内"),
    INTERACTION(1, "交互层");

    Integer code;
    String desc;
    ActionScopeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }
}
