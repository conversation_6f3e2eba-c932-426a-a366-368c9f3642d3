<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.yiwise.dialogflow</groupId>
        <artifactId>aicc-platform-dialogflow-web</artifactId>
        <version>2.24.4-RELEASE</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>dialogflow-chat-client</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>


    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-commons</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-context</artifactId>
        </dependency>
        <dependency>
            <groupId>com.netflix.ribbon</groupId>
            <artifactId>ribbon-loadbalancer</artifactId>
            <version>2.3.0</version>
        </dependency>
        <dependency>
            <groupId>com.netflix.ribbon</groupId>
            <artifactId>ribbon-core</artifactId>
            <version>2.3.0</version>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>2.7</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yiwise.middleware</groupId>
            <artifactId>Middleware-ObjectStorage</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yiwise.dialogflow</groupId>
            <artifactId>dialogflow-engine-share</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yiwise.dialogflow</groupId>
            <artifactId>dialogflow-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webflux</artifactId>
<!--            <version>5.2.5.RELEASE</version>-->
        </dependency>
<!--        <dependency>-->
<!--            <groupId>io.projectreactor.netty</groupId>-->
<!--            <artifactId>reactor-netty</artifactId>-->
<!--            <version>0.9.6.RELEASE</version>-->
<!--        </dependency>-->
    </dependencies>
</project>