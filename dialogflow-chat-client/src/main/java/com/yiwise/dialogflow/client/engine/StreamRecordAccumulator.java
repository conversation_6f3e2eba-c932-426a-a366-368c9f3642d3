package com.yiwise.dialogflow.client.engine;

import com.yiwise.dialogflow.client.listener.CallDetailListener;
import com.yiwise.dialogflow.client.model.engine.SimpleAiSayResultInfo;
import com.yiwise.dialogflow.client.model.engine.SimpleUserSayResultInfo;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayDeque;
import java.util.List;
import java.util.Queue;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * 用来实现对大模型生成的流式答案进行累加处理， 多个分段合并为一个结果
 * 因为底层是异步入库的，所以在话术这边处理，等待大模型生成的分段数据完成/超时 才会通过回调传给外呼引擎
 */
@Slf4j
@Data
class StreamRecordAccumulator {

    private final Supplier<List<CallDetailListener>> callDetailListenerSupplier;
    Queue<StreamRecord> queue = new ArrayDeque<>();

    volatile StreamRecord lastAiRecord;

    StreamRecordAccumulator(Supplier<List<CallDetailListener>> supplier) {
        this.callDetailListenerSupplier = supplier;
    }
    void enqueueAndTryCallback(StreamRecord record) {
        queue.add(record);
        tryCallback();
    }

    void tryCallback() {
        if (queue.isEmpty()) {
            return;
        }

        while (true) {
            StreamRecord first = queue.peek();
            if (first == null) {
                return;
            }
            if (first.wait && first.timeoutMs > System.currentTimeMillis()) {
                return;
            }
            StreamRecord record = queue.poll();
            if (record == first) {
                doCallback(record);
            }
        }
    }

    private void doCallback(StreamRecord record) {
        if (record.isAiRecord) {
            if (CollectionUtils.isNotEmpty(callDetailListenerSupplier.get())) {
                String text = String.join("", record.getAnswerList());
                List<String> debugLogList = record.getDebugLogList().stream().distinct().collect(Collectors.toList());
                List<String> simpleDebugLogList = record.getSimpleDebugLogList().stream().distinct().collect(Collectors.toList());
                if (StringUtils.isNotBlank(text)) {
                    for (CallDetailListener callDetailListener : callDetailListenerSupplier.get()) {
                        try {
                            SimpleAiSayResultInfo simpleUserSayResultInfo = new SimpleAiSayResultInfo();
                            simpleUserSayResultInfo.setSequence(record.getIndex());
                            simpleUserSayResultInfo.setAnswer(text);
                            simpleUserSayResultInfo.setDebugLogList(debugLogList);
                            simpleUserSayResultInfo.setSimpleDebugLogList(simpleDebugLogList);
                            callDetailListener.createAiSayRecord(simpleUserSayResultInfo);
                        } catch (Exception e) {
                            log.warn("创建ai侧联系历史失败", e);
                        }
                    }
                }
            }
        } else {
            if (CollectionUtils.isNotEmpty(callDetailListenerSupplier.get())) {
                for (CallDetailListener callDetailListener : callDetailListenerSupplier.get()) {
                    try {
                        callDetailListener.createUserSayRecord(record.getSimpleUserSayResultInfo());
                    } catch (Exception e) {
                        log.warn("创建用户侧联系历史失败", e);
                    }
                }
            }
        }
    }

    StreamRecord createAiSayRecord(int playIndex,
                                   String answerId,
                                   String answerText,
                                   boolean finish,
                                   List<String> debugLogList,
                                   List<String> simpleDebugLogList) {
        if (answerText == null) {
            answerText = "";
        }

        // 防止重复上一句拼接多余的答案
        if (lastAiRecord != null && !lastAiRecord.wait) {
            lastAiRecord = null;
        }

        StreamRecord lastRecord = lastAiRecord;

        if (lastRecord != null
                && StringUtils.equals(lastRecord.getAnswerId(), answerId)
                && isInQueue(lastRecord)) {
            lastRecord.getAnswerList().add(answerText);
            if (CollectionUtils.isNotEmpty(debugLogList)) {
                lastRecord.getDebugLogList().addAll(debugLogList);
            }
            if (CollectionUtils.isNotEmpty(simpleDebugLogList)) {
                lastRecord.getSimpleDebugLogList().addAll(simpleDebugLogList);
            }
            if (finish) {
                lastRecord.setWait(false);
                tryCallback();
            }
            return lastAiRecord;
        }

        if (lastRecord != null && isInQueue(lastRecord)) {
            // 已经切换新的文本了
            lastRecord.setWait(false);
        }

        StreamRecord streamRecord = new StreamRecord();
        streamRecord.setAnswerId(answerId);
        streamRecord.setIndex(playIndex);
        streamRecord.setAiRecord(true);
        streamRecord.setWait(!finish);
        streamRecord.getAnswerList().add(answerText);
        streamRecord.setTimeoutMs(System.currentTimeMillis() + 5000);
        if (CollectionUtils.isNotEmpty(debugLogList)) {
            streamRecord.getDebugLogList().addAll(debugLogList);
        }
        if (CollectionUtils.isNotEmpty(simpleDebugLogList)) {
            streamRecord.getSimpleDebugLogList().addAll(simpleDebugLogList);
        }
        this.lastAiRecord = streamRecord;
        enqueueAndTryCallback(streamRecord);
        return streamRecord;
    }

    boolean isInQueue(StreamRecord record) {
        return queue.contains(record);
    }

    void createUserSayRecord(SimpleUserSayResultInfo userSayResultInfo) {
        StreamRecord streamRecord = new StreamRecord();
        streamRecord.setSimpleUserSayResultInfo(userSayResultInfo);

        enqueueAndTryCallback(streamRecord);
    }
}
