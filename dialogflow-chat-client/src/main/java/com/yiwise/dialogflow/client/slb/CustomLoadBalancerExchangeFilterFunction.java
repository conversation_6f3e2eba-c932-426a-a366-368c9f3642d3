package com.yiwise.dialogflow.client.slb;

import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.cloud.client.loadbalancer.LoadBalancerClient;
import org.springframework.cloud.netflix.ribbon.RibbonLoadBalancerClient;
import org.springframework.http.HttpStatus;
import org.springframework.web.reactive.function.client.ClientRequest;
import org.springframework.web.reactive.function.client.ClientResponse;
import org.springframework.web.reactive.function.client.ExchangeFilterFunction;
import org.springframework.web.reactive.function.client.ExchangeFunction;
import reactor.core.publisher.Mono;

import java.net.URI;

@Slf4j
public class CustomLoadBalancerExchangeFilterFunction  implements ExchangeFilterFunction {

    private final LoadBalancerClient loadBalancerClient;

    public CustomLoadBalancerExchangeFilterFunction(LoadBalancerClient loadBalancerClient) {
        this.loadBalancerClient = loadBalancerClient;
    }

    @NotNull
    @Override
    public Mono<ClientResponse> filter(ClientRequest request, @NotNull ExchangeFunction next) {
        URI originalUrl = request.url();
        String serviceId = originalUrl.getHost();
        if(serviceId == null) {
            String msg = String.format("Request URI does not contain a valid hostname: %s", originalUrl.toString());
            log.warn(msg);
            return Mono.just(ClientResponse.create(HttpStatus.BAD_REQUEST).body(msg).build());
        }
        ServiceInstance instance;
        if (this.loadBalancerClient instanceof RibbonLoadBalancerClient) {
//            instance = ((RibbonLoadBalancerClient)this.loadBalancerClient).choose(serviceId, request.attributes());
            instance = this.loadBalancerClient.choose(serviceId);
        } else {
            instance = this.loadBalancerClient.choose(serviceId);
        }

        if(instance == null) {
            String msg = String.format("Load balancer does not contain an instance for the service %s", serviceId);
            log.warn(msg);
            return Mono.just(ClientResponse.create(HttpStatus.SERVICE_UNAVAILABLE).body(msg).build());
        }
        log.debug("选中的服务:{}:{}", instance.getHost(), instance.getPort());
        URI uri = this.loadBalancerClient.reconstructURI(instance, originalUrl);
        ClientRequest newRequest = ClientRequest.create(request.method(), uri)
                .headers(headers -> headers.addAll(request.headers()))
                .cookies(cookies -> cookies.addAll(request.cookies()))
                .attributes(attributes -> attributes.putAll(request.attributes()))
                .body(request.body())
                .build();
        return next.exchange(newRequest);
    }

}
