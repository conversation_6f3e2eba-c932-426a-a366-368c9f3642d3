package com.yiwise.dialogflow.client.engine;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class UserSayInfo {
    String text;
    String originText;
    long beginTime;
    long endTime;
    boolean merged;

    boolean necessaryMerge;

    volatile boolean ignore;

    List<UserSayInfo> originInfoList;

    public UserSayInfo() {
        originInfoList = new ArrayList<>();
    }

    public UserSayInfo(String text, long begin, long end) {
        this();
        this.text = text;
        this.originText = text;
        this.beginTime = begin;
        this.endTime = end;
    }
}
