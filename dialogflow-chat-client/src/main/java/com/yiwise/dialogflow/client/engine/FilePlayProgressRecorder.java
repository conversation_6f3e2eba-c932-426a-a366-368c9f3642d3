package com.yiwise.dialogflow.client.engine;

import com.yiwise.dialogflow.client.config.DialogEngineConstant;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class FilePlayProgressRecorder {

    private final Map<String, Integer> fileSizeMap = new ConcurrentHashMap<>();
    private final Map<String, Integer> filePlayMap = new ConcurrentHashMap<>();


    public void reset() {
        fileSizeMap.clear();
        filePlayMap.clear();
    }

    /**
     * 获取文件总大小
     *
     * @param fileName 播放的文件的名称
     * @return 文件的大小
     */
    public Integer getFileSize(String fileName) {
        if (StringUtils.isEmpty(fileName)) {
            return 0;
        }
        return fileSizeMap.getOrDefault(fileName, 0);
    }

    /**
     * 获取文件已播放的大小
     *
     * @param fileName 播放的文件的名称
     * @return 文件已播放的大小
     */
    public Integer getFilePlay(String fileName) {
        if (StringUtils.isEmpty(fileName)) {
            return 0;
        }
        return filePlayMap.getOrDefault(fileName, 0);
    }

    /**
     * 计算文件的播放进度  100 表示播放完
     *
     * @param fileName 播放的文件的名称
     * @return 播放进度（100为单位1）
     */
    public Double getFilePlayProgress(String fileName) {
        Integer fileSize = getFileSize(fileName);
        Integer filePlay = getFilePlay(fileName);
        double progress = fileSize.equals(0) ? 0 : (filePlay * 100.0 / fileSize);
        return progress > 100 ? 100 : progress;
    }

    /**
     * 设置文件的大小
     *
     * @param fileName 播放的文件的名称
     * @param size     文件大小
     */
    public void setFileSize(String fileName, Integer size) {
        fileSizeMap.put(fileName, size);
    }

    /**
     * 增加文件的大小
     *
     * @param fileName 播放的文件的名称
     * @param size     文件大小
     */
    public void addFileSize(String fileName, Integer size) {
        Integer preSize = fileSizeMap.get(fileName);
        fileSizeMap.put(fileName, preSize == null ? size : preSize + size);
    }

    /**
     * 获取文件播放的时长ms数
     *
     * @param fileName 播放的文件的名称
     */
    public Integer getFilePlayDurationProgress(String fileName) {
        Integer filePlay = getFilePlay(fileName);
        return filePlay / DialogEngineConstant.MONO_FILE_LENGTH_PER_SECOND;
    }
    /**
     * 获取文件播放的时长
     *
     * @param fileName 播放的文件的名称
     */
    public Integer getFilePlayDuration(String fileName) {
        Integer fileSize = getFileSize(fileName);
        return fileSize / DialogEngineConstant.MONO_FILE_LENGTH_PER_SECOND;
    }

    /**
     * 重置文件的播放进度
     *
     * @param fileName 播放的文件的名称
     */
    public void resetFilePlay(String fileName) {
        if (StringUtils.isEmpty(fileName)) {
            return;
        }
        filePlayMap.put(fileName, 0);
    }

    /**
     * 更新文件的播放进度
     *
     * @param fileName 播放的文件的名称
     * @param size     本次播放的大小
     */
    public void addOrUpdateFilePlayProgress(String fileName, Integer size) {
        Integer playedSize = getFilePlay(fileName);
        playedSize += size;
        filePlayMap.put(fileName, playedSize);
    }
}
