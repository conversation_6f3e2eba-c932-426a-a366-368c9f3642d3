package com.yiwise.dialogflow.client.engine;

import com.yiwise.dialogflow.client.config.DialogEngineConstant;
import com.yiwise.dialogflow.client.listener.AudioPlayEventListener;
import com.yiwise.dialogflow.engine.share.response.AnswerResult;
import com.yiwise.middleware.tts.model.ChatHistoryItem;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.sound.sampled.AudioSystem;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Consumer;

@Slf4j
public class DefaultAudioPlayManager implements AudioPlayManager {

    private final List<AudioPlayEventListener> listenerList;

    protected final FilePlayProgressRecorder filePlayProgressRecorder;

    private volatile InputStream currentPlayInputStream;

    private volatile String currentPlayFile;

    private volatile String answerId;

    private volatile long lastAiPlayTime;

    private volatile long lastPlayEndTime;

    private volatile boolean pause;

    private volatile boolean isPlayFinish = false;

    private volatile boolean isPlay = false;

    private final Map<String, String> answerAudioFilePathMap = new ConcurrentHashMap<>();

    private final AudioManager audioManager;

    private volatile int index;

    public DefaultAudioPlayManager(AudioManager audioManager) {
        this.audioManager = audioManager;
        this.listenerList = new ArrayList<>();
        this.filePlayProgressRecorder = new FilePlayProgressRecorder();
    }

    @Override
    public void reset() {
        log.debug("reset audioPlayManager");
        listenerList.clear();
        filePlayProgressRecorder.reset();
        currentPlayInputStream = null;
        currentPlayFile = null;
        answerId = null;
        lastAiPlayTime = 0;
        lastPlayEndTime = 0;
        pause = false;
        isPlayFinish = false;
        answerAudioFilePathMap.clear();
        index = 0;
    }

    @Override
    public byte[] readAudioFrame() {
        onReadData();
        byte[] buffer = new byte[DialogEngineConstant.PACKET_BUFFER_SIZE];
        if (!isPlayFinish && currentPlayInputStream != null) {
            if (pause) {
                return buffer;
            }
            readAndUpdateInputStream(buffer);
            //第一次调用执行onAiPlayBegin(),最后一次调用执行onAiPlayEnd()
            if (!isPlay) {
                log.debug("录音开始播放处理，index:{}, fileName:{}", index, currentPlayFile);
                onAiPlayBegin();
                isPlay = true;
            }
            if (currentPlayInputStream == null) {
                log.debug("录音播放完成后处理, fileName:{}", currentPlayFile);
                onAiPlayEnd();
                isPlay = false;
            }
        }
        return buffer;
    }

    @Override
    public boolean isPlaying() {
        return Objects.nonNull(currentPlayInputStream);
    }

    @Override
    public int playAudio(AnswerResult answer) {
        if (isPlaying()) {
            pause();
        }

        index ++;
        pause = false;
        //获取语音文件地址
        String filePath = audioManager.getAudioLocalPath(answer);
        //设置语音播放流
        answerAudioFilePathMap.put(answer.getId(), filePath);
        settingAudioStream(answer, filePath);
        return index;
    }

    @Override
    public void pause() {
        if (!pause) {
            log.info("暂停录音播放");
        }
        if (getCurrentAnswerPlayPercent() >= 100d) {
            log.info("当前答案:{}, 已播放完成，不需要暂停", answerId);
            return;
        }
        pause = true;
        onAiPlayPause();
    }

    @Override
    public void stop() {
        log.info("逻辑终止当前答案播放, 主要是在等待用户应答时, 能够触发 userSilence 事件");
        pause = true;
        isPlayFinish = false;
        lastPlayEndTime = System.currentTimeMillis();
    }

    @Override
    public void resume() {
        if (pause) {
            log.info("恢复录音播放");
        }
        pause = false;
    }

    @Override
    public void resume(String answerId) {
        log.info("恢复指定答案录音播放,answerId:{}", answerId);
        if (answerId.equals(this.answerId)) {
            resume();
            return;
        }
        if (pause) {
            log.info("恢复录音播放");
        }
        pause = false;
        Integer filePlaySize = filePlayProgressRecorder.getFilePlay(answerId);
        String filePath = answerAudioFilePathMap.get(answerId);
        if (Objects.nonNull(filePlaySize) && StringUtils.isNotBlank(filePath)) {
            resumeAudioStream(answerId, filePath, filePlaySize);
        } else {
            log.error("恢复录音播放失败，answerId:{},filePlaySize:{},filePath:{}", answerId, filePlaySize, filePath);
        }
    }

    @Override
    public double getCurrentAnswerPlayPercent() {
        return getAnswerPlayPercentByAnswerId(answerId);
    }

    @Override
    public double getAnswerPlayPercentByAnswerId(String answerId) {
        return filePlayProgressRecorder.getFilePlayProgress(answerId);
    }

    @Override
    public int getCurrentAnswerPlayTime() {
        return getAnswerPlayTimeByAnswerId(answerId);
    }

    @Override
    public int getAnswerPlayTimeByAnswerId(String answerId) {
        return filePlayProgressRecorder.getFilePlayDurationProgress(answerId);
    }

    @Override
    public long getLastPlayTime() {
        return lastAiPlayTime;
    }

    @Override
    public long getLastPlayEndTime() {
        return lastPlayEndTime;
    }

    @Override
    public void registerListener(AudioPlayEventListener listener) {
        listenerList.add(listener);
    }

    @Override
    public String getLastPlayAnswerId() {
        return answerId;
    }

    private void readAndUpdateInputStream(byte[] buffer) {
        try {
            if (currentPlayInputStream.read(buffer) < 0) {
                currentPlayInputStream.close();
                currentPlayInputStream = null;
                isPlayFinish = true;
                lastPlayEndTime = System.currentTimeMillis();
                filePlayProgressRecorder.addOrUpdateFilePlayProgress(answerId, DialogEngineConstant.PACKET_BUFFER_SIZE);
            } else {
                lastAiPlayTime = System.currentTimeMillis();
                //更新已播录音时间
                filePlayProgressRecorder.addOrUpdateFilePlayProgress(answerId, DialogEngineConstant.PACKET_BUFFER_SIZE);
                // 最后一个语音包，延迟更新进度
                if (filePlayProgressRecorder.getFilePlayProgress(answerId) >= 100) {
                    filePlayProgressRecorder.addOrUpdateFilePlayProgress(answerId, -1 * DialogEngineConstant.PACKET_BUFFER_SIZE);
                }
            }
        } catch (IOException e) {
            log.error("读取当前语音流失败，fileName:{}", currentPlayFile, e);
        }
    }

    private void settingAudioStream(AnswerResult answer, String filePath) {
        try {
            log.debug("设置播放语音流，index:{}, filePath:{},answerId:{},realAnswer:{}", index, filePath, answer.getId(), answer.getRealAnswer());
            currentPlayFile = filePath;
            answerId = answer.getId();
            currentPlayInputStream = obtainInputStream(currentPlayFile);
            lastAiPlayTime = System.currentTimeMillis();
            lastPlayEndTime = 0;
            isPlay = false;
            isPlayFinish = false;
            filePlayProgressRecorder.resetFilePlay(answerId);
            filePlayProgressRecorder.setFileSize(answerId, currentPlayInputStream.available());
        } catch (IOException e) {
            log.error("获取录音文件语音流失败，id:{},realAnswer:{},filePath:{}", answer.getId(), answer.getRealAnswer(), filePath, e);
        }
    }

    private void resumeAudioStream(String answerId, String filePath, int playedSize) {
        try {
            log.debug("恢复语音流，filePath:{},answerId:{}, playedSize:{}", filePath, answerId, playedSize);
            currentPlayFile = filePath;
            this.answerId = answerId;
            currentPlayInputStream = obtainInputStream(currentPlayFile);
            lastAiPlayTime = System.currentTimeMillis();
            lastPlayEndTime = 0;
            isPlayFinish = false;
            filePlayProgressRecorder.setFileSize(answerId, currentPlayInputStream.available());
            currentPlayInputStream.skip(playedSize);
            filePlayProgressRecorder.resetFilePlay(answerId);
            filePlayProgressRecorder.addOrUpdateFilePlayProgress(answerId, playedSize);
        } catch (IOException e) {
            log.error("获取录音文件语音流失败，id:{}, filePath:{}", answerId, filePath, e);
        }
    }

    private InputStream obtainInputStream(String filePath) throws IOException {
        try {
            return AudioSystem.getAudioInputStream(new File(filePath));
        } catch (Exception e) {
            return Files.newInputStream(Paths.get(filePath));
        }
    }

    private void onAiPlayBegin() {
        for (AudioPlayEventListener listener : listenerList) {
            try {
                listener.onAiPlayBegin(index);
            } catch (Exception e) {
                log.error("处理异常", e);
            }
        }
    }

    private void onAiPlayPause() {
        for (AudioPlayEventListener listener : listenerList) {
            try {
                listener.onPause(index);
            } catch (Exception e) {
                log.error("处理异常", e);
            }
        }
    }

    private void onAiPlayResume() {
        for (AudioPlayEventListener listener : listenerList) {
            try {
                listener.onResume(index);
            } catch (Exception e) {
                log.error("处理异常", e);
            }
        }
    }

    private void onAiPlayEnd() {
        for (AudioPlayEventListener listener : listenerList) {
            try {
                listener.onAiPlayEnd(index);
            } catch (Exception e) {
                log.error("处理异常", e);
            }
        }
    }

    private void onReadData() {
        emitterEvent(AudioPlayEventListener::onReadData);
    }

    private void emitterEvent(Consumer<AudioPlayEventListener> consumer) {
        if (CollectionUtils.isEmpty(listenerList)) {
            return;
        }
        List<AudioPlayEventListener> listenerCopy = new ArrayList<>(listenerList);
        for (AudioPlayEventListener audioPlayEventListener : listenerCopy) {
            try {
                consumer.accept(audioPlayEventListener);
            } catch (Exception e) {
                log.error("处理异常", e);
            }
        }
    }

    @Override
    public void onDynamicChanged(Map<String, String> dynamicVarMap) {
        audioManager.doOnDynamicVarMapMayChanged(dynamicVarMap);
    }

    @Override
    public void replay() {
        resumeAudioStream(answerId, currentPlayFile, 0);
        resume();
    }

    @Override
    public int appendAnswer(AnswerResult answer, List<ChatHistoryItem> list) {
        log.warn("不支持的操作");
        return index;
    }

    @Override
    public int getCurrentIndex() {
        return index;
    }

    @Override
    public void onHangup() {
        if (currentPlayInputStream != null) {
            try {
                currentPlayInputStream.close();
            } catch (IOException e) {
                log.error("关闭录音文件流失败", e);
            }
        }
        currentPlayInputStream = null;
    }

    @Override
    public String getCurrentAnswerPlayedContent() {
        return null;
    }

    @Override
    public String getAnswerPlayedContent(String answerId) {
        return "";
    }

    @Override
    public boolean isPause() {
        return pause;
    }
}
