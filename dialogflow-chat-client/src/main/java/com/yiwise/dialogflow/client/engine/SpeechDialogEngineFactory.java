package com.yiwise.dialogflow.client.engine;

import com.yiwise.base.common.thread.ApplicationExecutor;
import com.yiwise.dialogflow.engine.share.BotMetaData;
import com.yiwise.dialogflow.engine.share.response.SessionInfo;
import com.yiwise.dialogflow.client.listener.SystemMetaDataHelper;
import com.yiwise.dialogflow.engine.share.SystemMetaData;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;

import java.util.Objects;

public class SpeechDialogEngineFactory {

    public static SpeechDialogEngine createEngine(BotMetaData botMetaData,
                                           SessionInfo sessionInfo,
                                           AudioPlayManager audioPlayManager,
                                           ApplicationExecutor executor,
                                           DTMFManager dtmfManager) {
        SystemMetaData systemMetaData = SystemMetaDataHelper.getSystemMetaData().orElse(null);
        if (Objects.nonNull(systemMetaData)) {
            if (BooleanUtils.isTrue(systemMetaData.getDefaultUseV2PromptAudioCheck())) {
                return new PromptAudioCheckProxyEngineV2(null, botMetaData, sessionInfo, audioPlayManager, executor, dtmfManager);
            }
        }

        return new PromptAudioCheckProxyEngine(null, botMetaData, sessionInfo, audioPlayManager, executor, dtmfManager);
    }

    public static SpeechDialogEngine createEngine(Long tenantId,
                                                  BotMetaData botMetaData,
                                                  SessionInfo sessionInfo,
                                                  AudioPlayManager audioPlayManager,
                                                  ApplicationExecutor executor,
                                                  DTMFManager dtmfManager) {
        SystemMetaData systemMetaData = SystemMetaDataHelper.getSystemMetaData().orElse(null);
        if (Objects.nonNull(systemMetaData)) {
            if (BooleanUtils.isTrue(systemMetaData.getDefaultUseV2PromptAudioCheck())) {
                return new PromptAudioCheckProxyEngineV2(tenantId, botMetaData, sessionInfo, audioPlayManager, executor, dtmfManager);
            }
            if (CollectionUtils.isNotEmpty(systemMetaData.getUseV2PropmtAudioCheckTenantIdList())
                    && systemMetaData.getUseV2PropmtAudioCheckTenantIdList().contains(tenantId)) {
                return new PromptAudioCheckProxyEngineV2(tenantId, botMetaData, sessionInfo, audioPlayManager, executor, dtmfManager);
            }
        }
        return new PromptAudioCheckProxyEngine(tenantId, botMetaData, sessionInfo, audioPlayManager, executor, dtmfManager);
    }
}
