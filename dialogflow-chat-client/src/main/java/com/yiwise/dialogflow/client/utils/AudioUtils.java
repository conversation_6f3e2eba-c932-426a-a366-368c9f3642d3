package com.yiwise.dialogflow.client.utils;

import com.yiwise.base.common.utils.file.MyFileUtils;
import lombok.extern.slf4j.Slf4j;

import javax.sound.sampled.AudioFileFormat;
import javax.sound.sampled.AudioInputStream;
import javax.sound.sampled.AudioSystem;
import java.io.File;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.io.SequenceInputStream;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Slf4j
public class AudioUtils {
    public static String combinedAudios(List<String> audioPathList, String fileName) {
        List<InputStream> allStreamList = new ArrayList<>();
        try {
            BufferedInputStream tmpStream = new BufferedInputStream(Files.newInputStream(Paths.get(audioPathList.get(0))));
            allStreamList.add(tmpStream);
            AudioInputStream audio1 = AudioSystem.getAudioInputStream(tmpStream);
            allStreamList.add(audio1);
            AudioInputStream audio2;
            for (int i = 1; i < audioPathList.size(); i++) {
                try {
                    tmpStream = new BufferedInputStream(Files.newInputStream(Paths.get(audioPathList.get(i))));
                    allStreamList.add(tmpStream);
                    audio2 = AudioSystem.getAudioInputStream(tmpStream);
                    allStreamList.add(audio2);
                    audio1 = new AudioInputStream(
                            new SequenceInputStream(audio1, audio2), audio1.getFormat(), audio1.getFrameLength() + audio2.getFrameLength()
                    );
                    allStreamList.add(audio1);
                } catch (Exception e) {
                    log.error("合成音频出错, 出错音频[{}]", audioPathList.get(i), e);
                }
            }
            File file = new File(fileName);
            MyFileUtils.createNewFile(file);
            AudioSystem.write(audio1, AudioFileFormat.Type.WAVE, file);
            return fileName;
        } catch (Exception e) {
            log.error("多个音频合成一个出错, 期望音频 = [{}]", fileName, e);
            return audioPathList.get(0);
        } finally {
            for (InputStream stream : allStreamList) {
                try {
                    stream.close();
                } catch (IOException e) {
                    log.error("关闭音频流出错", e);
                }
            }
        }
    }

    public static int getWavAudioFileSize(File file) {
        BufferedInputStream bufferedInputStream = null;
        AudioInputStream audioInputStream = null;
        try {
            bufferedInputStream = new BufferedInputStream(Files.newInputStream(file.toPath()));
            audioInputStream = AudioSystem.getAudioInputStream(bufferedInputStream);
            return audioInputStream.available();
        } catch (Exception e) {
            log.error("获取音频文件大小出错, 文件[{}]", file.getAbsolutePath(), e);
            return getWavAudioFileSize2(file);
        } finally {
            try {
                if (bufferedInputStream != null) {
                    bufferedInputStream.close();
                }
            } catch (IOException e) {
                log.error("关闭音频流出错", e);
            }
            try {
                if (audioInputStream != null) {
                    audioInputStream.close();
                }
            } catch (IOException e) {
                log.error("关闭音频流出错", e);
            }
        }
    }

    public static byte[] read(RandomAccessFile accessFile, int pos, int length) throws IOException {
        accessFile.seek(pos);
        byte[] result = new byte[length];
        for (int i = 0; i < length; i++) {
            result[i] = accessFile.readByte();
        }
        return result;
    }

    private static int getWavAudioFileSize2(File file) {
        int length = (int) file.length();
        try {
            // 获取音频文件的真正的音频内容
            // 读取header 中的 length
            try (RandomAccessFile randomAccessFile = new RandomAccessFile(file, "r")){
                int tmpLength = toInt(read(randomAccessFile, 40, 4));
                if (tmpLength > 320) {
                    return Math.min((int) file.length(), tmpLength);
                }
                return (int) file.length();
            }
        } catch (Exception e) {
            log.error("获取音频文件大小出错, 文件[{}]", file.getAbsolutePath(), e);
        }
        return length;
    }

    public static int toInt(byte[] b) {
        return (((b[3] & 0xff) << 24) + ((b[2] & 0xff) << 16) + ((b[1] & 0xff) << 8) + ((b[0] & 0xff)));
    }

    public static void main(String[] args) throws Exception {

        System.out.println(getWavAudioFileSize2(new File("/Users/<USER>/Downloads/c8b22fc323529e86_1743573832048.wav")));
    }
}
