package com.yiwise.dialogflow.client.engine;

import com.yiwise.dialogflow.engine.share.response.AnswerResult;
import com.yiwise.middleware.tts.model.ChatHistoryItem;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

public interface AudioManager {

    void prepare(Map<String, String> properties,
                 Function<Long, Map<String, Map<String, String>>> getUserVariableByRecordIdFunc,
                 Function<Long, Map<String, String>> getFamilyVariableAudioFunc,
                 boolean multiThread);

    default void prepare(Map<String, String> properties,
                 Function<Long, Map<String, Map<String, String>>> getUserVariableByRecordIdFunc,
                 Function<Long, Map<String, String>> getFamilyVariableAudioFunc) {
        prepare(properties, getUserVariableByRecordIdFunc, getFamilyVariableAudioFunc, false);
    }

    default void prepare(Map<String, String> properties,
                 Function<Long, Map<String, Map<String, String>>> getUserVariableByRecordIdFunc) {
        prepare(properties, getUserVariableByRecordIdFunc, (recordId) -> new HashMap<>());
    }

    default void prepare(Map<String, String> properties) {
        prepare(properties, (recordUserId) -> new HashMap<>());
    }

    String getAudioLocalPath(AnswerResult answer);

    List<AudioFragment> getAudioFragments(AnswerResult answer);

    default List<AudioFragment> createAudioFragments(AnswerResult answer) {
        return createAudioFragments(answer, false, null);
    }

    default List<AudioFragment> createAudioFragments(AnswerResult answer, boolean streaming, List<ChatHistoryItem> historyList) {
        return Collections.emptyList();
    }

    void doOnDynamicVarMapMayChanged(Map<String, String> newMap);
}
