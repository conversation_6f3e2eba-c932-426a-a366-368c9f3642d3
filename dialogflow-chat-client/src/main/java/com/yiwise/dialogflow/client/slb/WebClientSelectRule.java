package com.yiwise.dialogflow.client.slb;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.reactive.function.client.WebClient;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Random;
import java.util.concurrent.TimeUnit;

/**
 * WebClient 自定义选择规则
 * 类似于 ChatServerSelectRule，为 WebClient 提供负载均衡和缓存功能
 * 
 * <AUTHOR>
 */
@Slf4j
public class WebClientSelectRule {

    private final Cache<String, String> callRecordSelectClientCache = CacheBuilder.newBuilder()
            .expireAfterAccess(10, TimeUnit.MINUTES)
            .maximumSize(100_000)
            .build();

    private final List<WebClient> availableClients;
    private final Random random = new Random();

    public WebClientSelectRule(List<WebClient> availableClients) {
        this.availableClients = availableClients;
        if (availableClients == null || availableClients.isEmpty()) {
            throw new IllegalArgumentException("Available clients cannot be null or empty");
        }
    }

    /**
     * 根据上下文选择 WebClient
     */
    public WebClient choose(BotChatServerSelectContext context) {
        Optional<String> callTaskIdOpt = getCallTaskId(context);
        if (!isChatRequest(context) || !callTaskIdOpt.isPresent()) {
            WebClient client = doChoose();
            log.info("不是对话接口或callTaskId为null, client={}", getClientInfo(client));
            return client;
        }
        
        String callTaskId = callTaskIdOpt.get();
        Optional<WebClient> cacheClient = selectFromCache(callTaskId);
        boolean switchClient = isSwitchClient(context);
        
        if (cacheClient.isPresent()) {
            if (switchClient) {
                log.info("callTaskId={}对应的对话服务已经选择, 但是需要切换服务重试, client={}", 
                        callTaskId, getClientInfo(cacheClient.get()));
                String cacheClientInfo = getClientInfo(cacheClient.get());
                WebClient client = null;
                int retry = 0;
                int maxRetry = 10;
                do {
                    retry++;
                    if (retry > maxRetry) {
                        break;
                    }
                    client = doChoose();
                } while (Objects.isNull(client) || StringUtils.equals(cacheClientInfo, getClientInfo(client)));
                
                if (Objects.nonNull(client)) {
                    flushCache(callTaskId, client);
                    log.info("select client from rule, switchClient=true, callTaskId={}, client={}", 
                            callTaskId, getClientInfo(client));
                    return client;
                }
            }
            log.info("select client from cache, callTaskId={}, client={}", 
                    callTaskId, getClientInfo(cacheClient.get()));
            return cacheClient.get();
        }
        
        WebClient client = doChoose();
        flushCache(callTaskId, client);
        log.info("select client from rule, callTaskId={}, client={}", 
                callTaskId, getClientInfo(client));
        return client;
    }

    private WebClient doChoose() {
        if (availableClients.size() == 1) {
            return availableClients.get(0);
        }
        int index = random.nextInt(availableClients.size());
        return availableClients.get(index);
    }

    private void flushCache(String callTaskId, WebClient client) {
        if (Objects.nonNull(client)) {
            callRecordSelectClientCache.put(callTaskId, getClientInfo(client));
        }
    }

    private Optional<String> getCallTaskId(BotChatServerSelectContext context) {
        if (Objects.isNull(context) || StringUtils.isBlank(context.getTaskId()) || 
            "null".equals(context.getTaskId()) || "0".equals(context.getTaskId())) {
            return Optional.empty();
        }
        return Optional.ofNullable(context.getTaskId());
    }

    private boolean isChatRequest(BotChatServerSelectContext context) {
        return Objects.nonNull(context);
    }

    private Optional<WebClient> selectFromCache(String callTaskId) {
        String clientInfo = callRecordSelectClientCache.getIfPresent(callTaskId);
        if (StringUtils.isBlank(clientInfo)) {
            return Optional.empty();
        }
        
        Optional<WebClient> result = availableClients.stream()
                .filter(item -> clientInfo.equals(getClientInfo(item)))
                .findFirst();
        
        if (!result.isPresent()) {
            log.info("检测到缓存中的client已不可用, 重新选择服务, clientInfo:{}", clientInfo);
        }
        return result;
    }

    private boolean isSwitchClient(BotChatServerSelectContext context) {
        return Objects.nonNull(context) && BooleanUtils.isTrue(context.getSwitchServerRetry());
    }

    private String getClientInfo(WebClient client) {
        // 使用 WebClient 的 hashCode 作为标识
        // 在实际使用中，可以根据需要自定义标识逻辑
        return String.valueOf(client.hashCode());
    }
}
