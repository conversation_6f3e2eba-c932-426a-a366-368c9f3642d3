package com.yiwise.dialogflow.client.engine;

import com.yiwise.dialogflow.engine.share.response.AnswerResult;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

/**
 * 用于文本训练测试的AudioManager, 不会真正下载音频
 */
@Slf4j
public class TextTestAudioManager implements AudioManager {

    @Override
    public void prepare(Map<String, String> properties,
                        Function<Long, Map<String, Map<String, String>>> getUserVariableByRecordIdFunc,
                        Function<Long, Map<String, String>> getFamilyVariableAudioFunc,
                        boolean multiThread) {
        log.info("TextTestAudioManager prepares:{}", properties);
    }

    @Override
    public String getAudioLocalPath(AnswerResult answer) {
        log.info("TextTestAudioManager getAudioLocalPath:{}", answer);
        return null;
    }

    @Override
    public List<AudioFragment> getAudioFragments(AnswerResult answer) {
        return new ArrayList<>();
    }

    @Override
    public void doOnDynamicVarMapMayChanged(Map<String, String> newMap) {
        log.info("TextTestAudioManager doOnDynamicVarMapMayChanged:{}", newMap);
    }
}
