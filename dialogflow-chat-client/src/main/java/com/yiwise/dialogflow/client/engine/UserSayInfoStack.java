package com.yiwise.dialogflow.client.engine;

import org.apache.commons.collections4.CollectionUtils;

import java.util.LinkedList;
import java.util.Objects;
import java.util.Optional;

public class UserSayInfoStack {
    LinkedList<UserSayInfo> list = new LinkedList<>();

    Optional<UserSayInfo> pop() {
        if (CollectionUtils.isEmpty(list)) {
            return Optional.empty();
        }
        return Optional.of(list.pop());
    }

    Optional<UserSayInfo> peek() {
        if (CollectionUtils.isEmpty(list)) {
            return Optional.empty();
        }
        return Optional.ofNullable(list.peek());
    }

    void push(UserSayInfo info) {
        if (Objects.nonNull(info)) {
            list.push(info);
        }
    }
}
