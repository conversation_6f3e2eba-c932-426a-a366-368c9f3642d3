package com.yiwise.dialogflow.client.asr;

import com.yiwise.dialogflow.client.engine.SpeechDialogEngine;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
public abstract class AbstractAsrResultFilter implements AsrResultFilter {

    protected final SpeechDialogEngine speechDialogEngine;

    AtomicInteger skipFrameCount = new AtomicInteger(0);

    AbstractAsrResultFilter(SpeechDialogEngine speechDialogEngine) {
        this.speechDialogEngine = speechDialogEngine;
    }

    @Override
    public void processUserSay(String userSayText, boolean userSayFinish, int beginTime, int endTime) {
        speechDialogEngine.realProcessUserSay(userSayText, userSayFinish, beginTime, endTime);
    }

    @Override
    public void onSentenceBegin() {

    }

    @Override
    public void writeData(short[] data) {

    }

    @Override
    public void waitingAsrInitSkipAudioData(int length) {
        skipFrameCount.incrementAndGet();
    }

    @Override
    public int getWaitAsrInitSkipAudioData() {
        return skipFrameCount.get();
    }

    @Override
    public void setWaitAsrInitSkipAudioData(int value) {
        log.debug("set skipFrameCount:{}", value);
        skipFrameCount.set(value);
    }

}
