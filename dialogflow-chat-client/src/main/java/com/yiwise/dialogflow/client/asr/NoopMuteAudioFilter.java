package com.yiwise.dialogflow.client.asr;

import java.util.Collections;
import java.util.List;

public class NoopMuteAudioFilter extends AbstractAudioFilter {

    @Override
    public List<FilterResult> filter(byte[] bytes, int offset, int length) {
        FilterResult result = new FilterResult();
        result.setData(bytes);
        result.setOffset(offset);
        result.setLength(length);
        return Collections.singletonList(result);
    }

    @Override
    public List<AudioVoiceInfo> getFilterAudioInfo() {
        return Collections.emptyList();
    }

    @Override
    public int getTotalSkipDuration() {
        return 0;
    }

    @Override
    protected int doConvert(int sendTime) {
        return sendTime;
    }


}
