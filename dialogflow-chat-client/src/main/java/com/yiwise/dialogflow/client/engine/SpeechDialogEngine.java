package com.yiwise.dialogflow.client.engine;

import com.yiwise.dialogflow.client.listener.CallDetailListener;
import com.yiwise.dialogflow.client.listener.HangupEventListener;
import com.yiwise.dialogflow.client.listener.UserVoiceEventListener;
import com.yiwise.dialogflow.engine.share.CallDataInfo;
import com.yiwise.dialogflow.engine.share.IntentLevelAnalysisResult;

import java.util.Collection;
import java.util.List;
import java.util.function.Consumer;

public interface SpeechDialogEngine extends UserVoiceEventListener {

    void init();

    void enter();

    void onSentenceBegin();
    /**
     * 处理用户输入
     */
    void processUserSay(String userSayText, boolean userSayFinish, int beginTime, int endTime);

    void realProcessUserSay(String userSayText, boolean userSayFinish, int beginTime, int endTime);

    /**
     * 注册对话详情监听
     */
    void registerCallDetailListener(CallDetailListener listener);

    void registerHangupEventListener(HangupEventListener hangupEventListener);

    String getSessionContextContent();

    /**
     * 分析意向等级等
     */
    IntentLevelAnalysisResult intentLevelAnalysis(CallDataInfo callDataInfo);

    /**
     * 通话中分析意向等级
     */
    IntentLevelAnalysisResult intentLevelAnalysisInCall(CallDataInfo callDataInfo);

    void release(String reason);

    /**
     * 注册加微回调
     */
    void registerAddWechatCallback(Runnable addWechatCallback);

    /**
     * 注册转人工执行器
     * @param switchToHumanServiceExecutor 转人工执行器
     */
    void registerSwitchToHumanServiceExecutor(Runnable switchToHumanServiceExecutor);

    void registerHumanInterventionExecutor(Runnable humanInterventionExecutor);

    /**
     * 注册发送短信消费者, 执行实际的发送短信逻辑,
     * @param sendSmsConsumer 发送短信消费者
     */
    void registerSendSmsConsumer(Consumer<Collection<Long>> sendSmsConsumer);

    void onUserHangup();

    void onAiHangup();

    void finishByError();

    void finishByMatchPromptAudio();

    List<String> getAllUserSayContentList();

    String getLastAnswerId();

    void waitingAsrInitSkipAudioData(int length);

    void writeData(short[] shorts);

    void userSilence();
}
