package com.yiwise.dialogflow.client.engine;

import com.yiwise.dialogflow.client.listener.AudioPlayEventListener;
import com.yiwise.dialogflow.engine.share.response.AnswerResult;
import com.yiwise.middleware.tts.model.ChatHistoryItem;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

/**
 * 用于文本训练测试的AudioPlayManager, 不会真正播放音频
 */
@Slf4j
public class TextTestAudioPlayManager implements AudioPlayManager {

    private final List<AudioPlayEventListener> listenerList = new ArrayList<>();

    private AnswerResult currentAnswer;

    private boolean pause;

    private List<AnswerResult> answerList = new ArrayList<>();

    private Map<String, Integer> answerId2PercentMap = new HashMap<>();

    private volatile int index;

    public void setCurrentAnswerPlayPercent(int percent) {
        log.info("setCurrentAnswerPlayPercent:{}", percent);
        if (Objects.nonNull(currentAnswer)) {
            answerId2PercentMap.put(currentAnswer.getId(), percent);
        } else {
            log.warn("currentAnswer is null");
        }
    }

    @Override
    public void reset() {

    }

    @Override
    public byte[] readAudioFrame() {
        return new byte[0];
    }

    @Override
    public boolean isPlaying() {
        return false;
    }

    @Override
    public int playAudio(AnswerResult answer) {
        log.info("TextTestAudioPlayManager playAudio:{}", answer);
        currentAnswer = answer;
        index ++;
        answerList.clear();
        answerList.add(answer);
        return index;
    }

    @Override
    public void pause() {
        if (!pause) {
            log.info("暂停录音播放");
        } else {
            log.info("已经暂停录音播放");
        }
        pause = true;
    }

    @Override
    public void stop() {
        pause = false;
    }

    @Override
    public void resume() {
        if (pause) {
            log.info("恢复录音播放");
        } else {
            log.info("未暂停录音播放, 无须恢复");
        }
        pause = false;
    }

    @Override
    public void resume(String answerId) {
        log.info("恢复指定答案录音播放,answerId:{}, currentAnswer:{}", answerId, currentAnswer);
        resume();
    }

    @Override
    public double getCurrentAnswerPlayPercent() {
        if (Objects.isNull(currentAnswer)) {
            return 0;
        }
        return getAnswerPlayPercentByAnswerId(currentAnswer.getId());
    }

    @Override
    public double getAnswerPlayPercentByAnswerId(String answerId) {
        return answerId2PercentMap.getOrDefault(currentAnswer.getId(), 0);
    }

    @Override
    public int getCurrentAnswerPlayTime() {
        return (int) (getCurrentAnswerPlayPercent() * 200);
    }

    @Override
    public int getAnswerPlayTimeByAnswerId(String answerId) {
        return (int) (getAnswerPlayPercentByAnswerId(answerId) * 200);
    }

    @Override
    public long getLastPlayTime() {
        return System.currentTimeMillis();
    }

    @Override
    public long getLastPlayEndTime() {
        return System.currentTimeMillis();
    }

    @Override
    public void registerListener(AudioPlayEventListener listener) {
        listenerList.add(listener);
    }

    @Override
    public String getLastPlayAnswerId() {
        return currentAnswer.getId();
    }

    @Override
    public void onDynamicChanged(Map<String, String> dynamicVarMap) {
        log.info("onDynamicChanged:{}", dynamicVarMap);
    }

    @Override
    public void replay() {
        setCurrentAnswerPlayPercent(0);
        resume();
    }

    @Override
    public int appendAnswer(AnswerResult answer, List<ChatHistoryItem> list) {
        answerList.add(answer);
        return index;
    }

    @Override
    public int getCurrentIndex() {
        return index;
    }

    @Override
    public void onHangup() {

    }

    @Override
    public String getCurrentAnswerPlayedContent() {
        StringBuilder sb = new StringBuilder();
        for (AnswerResult answerResult : answerList) {
            if (StringUtils.isNotBlank(answerResult.getRealAnswer())) {
                sb.append(answerResult.getRealAnswer());
            }
        }
        return sb.toString();
    }

    @Override
    public String getCurrentAnswerFullContent() {
        StringBuilder sb = new StringBuilder();
        for (AnswerResult answerResult : answerList) {
            if (StringUtils.isNotBlank(answerResult.getRealAnswer())) {
                sb.append(answerResult.getRealAnswer());
            }
        }
        return sb.toString();
    }

    @Override
    public int getPlayedAnswerIndex() {
        return index;
    }

    @Override
    public boolean isPause() {
        return pause;
    }

    @Override
    public String getAnswerPlayedContent(String answerId) {
        return "";
    }
}
