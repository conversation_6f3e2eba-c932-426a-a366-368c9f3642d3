package com.yiwise.dialogflow.client.utils;


/**
 * Created by 昌夜 on 18/12/25.
 */
public class AutoAnswerRobotDetector {
    public static int THRESHOLD_INTERVAL_HIGH = 100;
    public static int THRESHOLD_INTERVAL_LOW = 2000;
    public static int THRESHOLD_DURATION = 8000;

    int startTime;
    int duration;

    public AutoAnswerRobotDetector() {
        startTime = 0;
        duration = 0;
    }

    /**
     * 检测到对端是自动应答的，返回true
     * @param sentenceStart
     * @param sentenceEnd
     * @return
     */
    public boolean isAutoAnswer(int sentenceStart, int sentenceEnd) {
        int d = sentenceEnd - sentenceStart;
        if(d > THRESHOLD_DURATION && sentenceStart < THRESHOLD_INTERVAL_LOW) {
            return true;
        }
        if ((sentenceStart - startTime) < THRESHOLD_INTERVAL_HIGH) {
            duration += d;
            startTime = sentenceEnd;
        }
        return duration > THRESHOLD_DURATION;
    }
}