package com.yiwise.dialogflow.client.engine;

import com.yiwise.dialogflow.engine.share.enums.AnswerSourceEnum;
import lombok.Data;

@Data
public class InterruptConfig {

    volatile AnswerSourceEnum answerSource = AnswerSourceEnum.STEP;

    volatile boolean interrupt = true;

    volatile boolean uninterrupted = false;

    volatile int threshold = 100;

    volatile boolean needTryReplyOnUninterrupted;
}
