package com.yiwise.dialogflow.client.engine;

import com.yiwise.dialogflow.client.model.engine.SimpleUserSayResultInfo;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class StreamRecord {
    int index;
    String answerId;
    boolean wait;
    long timeoutMs;
    boolean isAiRecord;
    // ai侧联系历史会追加到 answerList 里
    List<String> answerList = new ArrayList<>();
    // 用户侧
    SimpleUserSayResultInfo simpleUserSayResultInfo;

    List<String> debugLogList = new ArrayList<>();
    List<String> simpleDebugLogList = new ArrayList<>();
}
