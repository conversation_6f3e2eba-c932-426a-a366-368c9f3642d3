package com.yiwise.dialogflow.aop;


import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Slf4j
@Aspect
@Component
@Order(1)
public class AsyncWebLogAspect extends CommonAsyncWebLogAspect {

    public AsyncWebLogAspect() {
        log.info("AsyncWebLogAspect init");
    }

    @Pointcut("@within(org.springframework.web.bind.annotation.RestController) && execution(reactor.core.publisher.Mono com.yiwise.dialogflow.chatcontroller..*.*(..)) ")
    public void restController() {
    }

    @Around("(restController())")
    public Object doWebSocketControllerAroundMethod(ProceedingJoinPoint pig) throws Throwable {
        return doRestControllerAroundMethod(pig);
    }
}
