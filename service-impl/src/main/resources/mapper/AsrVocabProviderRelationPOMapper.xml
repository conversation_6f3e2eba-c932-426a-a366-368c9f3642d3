<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 4.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yiwise.dialogflow.mapper.AsrVocabProviderRelationPOMapper">
    <resultMap id="BaseResultMap" type="com.yiwise.dialogflow.entity.po.asrmodel.AsrVocabProviderRelationPO">
        <id column="asr_vocob_provider_relation_id" property="asrVocabProviderRelationId" jdbcType="BIGINT"/>
        <result column="asr_vocab_id" property="asrVocabId" jdbcType="BIGINT"/>
        <result column="provider" property="provider" jdbcType="TINYINT"
                typeHandler="com.yiwise.middleware.mysql.handler.CodeDescEnumHandler"/>
        <result column="vocabulary_id" property="vocabularyId" jdbcType="VARCHAR"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <select id="getByAsrVocabId" resultMap="BaseResultMap">
        select *
        from asr_vocab_provider_relation
        where asr_vocab_id = #{asrVocabId}
    </select>

    <select id="getByAsrVocabIdAndProvider" resultMap="BaseResultMap">
        select *
        from asr_vocab_provider_relation
        where asr_vocab_id = #{asrVocabId}
          and provider = #{provider,typeHandler=com.yiwise.middleware.mysql.handler.CodeDescEnumHandler}
    </select>

</mapper>