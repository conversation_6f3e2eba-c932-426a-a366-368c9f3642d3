package com.yiwise.dialogflow.thread;

import com.yiwise.base.common.thread.ApplicationExecutor;
import com.yiwise.base.common.thread.decorator.MDCDecoratorCallable;
import com.yiwise.base.common.thread.decorator.MDCDecoratorRunnable;
import com.yiwise.middleware.mysql.aop.DataSourceInfo;
import com.yiwise.middleware.mysql.aop.DynamicDataSourceAspect;
import com.yiwise.middleware.mysql.aop.DynamicDataSourceContextHolder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.*;

/**
 * 带有数据源继承功能的异步任务执行工具
 */
public class DynamicDataSourceApplicationExecutor extends ApplicationExecutor {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    public DynamicDataSourceApplicationExecutor(ScheduledExecutorService scheduledThread, ThreadPoolExecutor threadPool) {
        super(scheduledThread, threadPool);
    }

    @Override
    public void startThreadPoolStatusMonitor(Runnable runnable, long initialDelay, long period, TimeUnit unit) {
        DataSourceInfo dataSourceInfo = DynamicDataSourceContextHolder.getDataSourceInfo();

        scheduledThread.scheduleAtFixedRate(() -> {
            if (dataSourceInfo != null) {
                try {
                    DynamicDataSourceAspect.setDataSource("ThreadPoolStatusMonitor", dataSourceInfo);

                    doStartThreadPoolStatusMonitor(runnable);
                } finally {
                    DynamicDataSourceAspect.restoreDataSource("ThreadPoolStatusMonitor");
                }
            } else {
                doStartThreadPoolStatusMonitor(runnable);
            }
        }, initialDelay, period, unit);
    }

    @Override
    public void execute(String taskTitle, Runnable runnable) {
        logger.debug("提交任务===" + taskTitle);

        String parentName = super.getThreadName(Thread.currentThread().getName());
        DataSourceInfo dataSourceInfo = DynamicDataSourceContextHolder.getDataSourceInfo();

        threadPool.execute(new MDCDecoratorRunnable(() -> {
            if (dataSourceInfo != null) {
                try {
                    DynamicDataSourceAspect.setDataSource(taskTitle, dataSourceInfo);
                    super.doExecute(taskTitle, parentName, runnable);
                } finally {
                    DynamicDataSourceAspect.restoreDataSource(taskTitle);
                }
            } else {
                super.doExecute(taskTitle, parentName, runnable);
            }
        }));
    }

    public <T> Future<T> submit(String taskTitle, Callable<T> callable) {
        logger.debug("提交任务===" + taskTitle);

        String parentName = super.getThreadName(Thread.currentThread().getName());
        DataSourceInfo dataSourceInfo = DynamicDataSourceContextHolder.getDataSourceInfo();

        return threadPool.submit(new MDCDecoratorCallable<>(() -> {
            if (dataSourceInfo != null) {
                try {
                    DynamicDataSourceAspect.setDataSource(taskTitle, dataSourceInfo);
                    return doExecute(taskTitle, parentName, callable);
                } finally {
                    DynamicDataSourceAspect.restoreDataSource(taskTitle);
                }
            } else {
                return doExecute(taskTitle, parentName, callable);
            }
        }));
    }

    protected <T> T doExecute(String taskTitle, String parentName, Callable<T> callable) {
        Thread.currentThread().setName(this.getThreadName(Thread.currentThread().getName()) + "--" + parentName);
        long startTime = System.currentTimeMillis();
        logger.debug("{}====开始执行. 线程池状态: Active={}, InQueue={}.", taskTitle, this.threadPool.getActiveCount(), this.threadPool.getQueue().size());

        try {
            return callable.call();
        } catch (Throwable var10) {
            logger.error("[LogHub_Warn]{}====执行出错. 线程池状态: Active={}, InQueue={}.", taskTitle, this.threadPool.getActiveCount(), this.threadPool.getQueue().size(), var10);
        } finally {
            logger.debug("[LogHub]{}====结束执行, 异步执行耗时{}ms. 线程池状态: Active={}, InQueue={}.", taskTitle, System.currentTimeMillis() - startTime, this.threadPool.getActiveCount(), this.threadPool.getQueue().size());
        }
        return null;
    }
}

