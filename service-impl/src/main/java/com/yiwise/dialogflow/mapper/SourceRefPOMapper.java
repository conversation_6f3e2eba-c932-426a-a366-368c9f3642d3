package com.yiwise.dialogflow.mapper;

import com.yiwise.dialogflow.entity.bo.SourceRefBO;
import com.yiwise.dialogflow.entity.enums.IntentRefTypeEnum;
import com.yiwise.dialogflow.entity.po.SourceRefPO;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR> @ yiwise . com>
 * @version v0.1 2022/7/20
 * @class <code>SourceRefPOMapper</code>
 * @see
 * @since JDK1.8
 */
public interface SourceRefPOMapper extends Mapper<SourceRefPO> {

    void deleteSourceByParentRefId(@Param("botId") Long botId, @Param("parentRefId") String parentRefId, @Param("parentRefType") Integer parentRefType);

    void deleteSourceByParentRefIdList(@Param("botId") Long botId, @Param("parentRefIdList") List<String> parentRefIdList, @Param("parentRefType") Integer parentRefType);

    void deleteSourceByRefId(@Param("botId") Long botId, @Param("refId") String id);

    void deleteSourceByRefIds(@Param("botId") Long botId, @Param("list") List<String> refIdList);

    void deleteByRefIdsAndSourceType(@Param("botId") Long botId, @Param("list") List<String> refIdList, @Param("sourceType") Integer sourceType);

    void deleteSourceByRefIdList(@Param("botId") Long botId, @Param("list") List<String> refIdList, @Param("refType") Integer refType);

    void deleteSourceByRefIdAndSourceTypeList(@Param("botId") Long botId, @Param("list") List<String> refIdList, @Param("refType") Integer refType, @Param("sourceType") Integer sourceType);

    List<SourceRefPO> getIntentByRefIdList(@Param("botId") Long botId, @Param("list") List<String> refIdList, @Param("refType") Integer code);

    List<SourceRefPO> getBySourceIdList(@Param("botId") Long botId,
                                        @Param("list") List<String> sourceIdList,
                                        @Param("sourceType") Integer sourceType);

    List<SourceRefPO> getAllByBotId(@Param("botId") Long botId);

    List<SourceRefPO> getListBySourceTypeAndRefType(@Param("botId") Long botId,
                                                    @Param("sourceType") Integer sourceTypeCode,
                                                    @Param("refTypeCode") Integer refTypeCode);

    void deleteBySourceIdList(@Param("botId") Long botId, @Param("list") Collection<String> sourceIds, @Param("refType") IntentRefTypeEnum refType);

    void batchAddSourceRef(@Param("p") SourceRefBO param, @Param("sourceType") Integer sourceType, @Param("refType") Integer refType, @Param("parentRefType") Integer parentRefType);

    void batchAddSourceRefList(@Param("list") List<SourceRefPO> list);

    List<SourceRefPO> getListByBotIdListAndSourceType(@Param("botIdList") List<Long> botIdList, @Param("sourceType") Integer sourceType);

    void deleteByRefType(@Param("botId") Long botId, @Param("refType") Integer refType);

    void updateRefLabelByRefId(@Param("botId") Long botId, @Param("refId") String refId, @Param("refLabel") String refLabel);
}
