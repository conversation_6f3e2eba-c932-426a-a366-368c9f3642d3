package com.yiwise.dialogflow.mapper;

import com.yiwise.dialogflow.entity.po.SessionDetailIntentInfoPO;
import javaslang.Tuple2;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/12/16
 */
public interface SessionDetailIntentInfoPOMapper extends Mapper<SessionDetailIntentInfoPO> {
    void batchInsert(@Param("list") List<SessionDetailIntentInfoPO> list);

    List<SessionDetailIntentInfoPO> listBySessionIdSeqList(@Param("list") List<Tuple2<String,Integer>> list);
}
