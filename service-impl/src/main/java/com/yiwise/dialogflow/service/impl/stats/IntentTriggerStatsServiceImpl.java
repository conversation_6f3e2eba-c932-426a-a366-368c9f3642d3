package com.yiwise.dialogflow.service.impl.stats;

import com.yiwise.dialogflow.common.MongoCollectionNameCenter;
import com.yiwise.dialogflow.engine.share.enums.AnswerSourceEnum;
import com.yiwise.dialogflow.entity.bo.stats.BotStatsAnalysisResult;
import com.yiwise.dialogflow.entity.po.stats.IntentTriggerStatsPO;
import com.yiwise.dialogflow.entity.query.BaseStatsQuery;
import com.yiwise.dialogflow.service.CallStatsMongoService;
import com.yiwise.dialogflow.service.stats.IntentTriggerStatsService;
import com.yiwise.dialogflow.utils.BotStatsUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class IntentTriggerStatsServiceImpl implements IntentTriggerStatsService {

    @Resource
    private MongoTemplate readMongoTemplate;

    @Resource
    private CallStatsMongoService callStatsMongoService;

    @Override
    public List<IntentTriggerStatsPO> queryIntentTriggerStats(Long botId, BaseStatsQuery condition) {
        long beginEpochHour = BotStatsUtil.toEpochHour(BotStatsUtil.getBeginDateTimeOrDefault(condition.getBeginTime()));
        long endEpochHour = BotStatsUtil.toEpochHour(BotStatsUtil.getEndDateTimeOrDefault(condition.getEndTime()));
        List<AggregationOperation> aggregationOperationList = new ArrayList<>();
        aggregationOperationList.add(BotStatsUtil.generateCommonEpochHourCondition(beginEpochHour, endEpochHour));
        aggregationOperationList.add(Aggregation.match(Criteria.where("botId").is(botId)));
        BotStatsUtil.generateTenantCondition(condition).ifPresent(aggregationOperationList::add);
        BotStatsUtil.generateCallJobCondition(condition).ifPresent(aggregationOperationList::add);

        AggregationOperation groupOperation = Aggregation.group( "targetType", "targetId")
                .sum("count").as("count")
                .sum("callCount").as("callCount")
                .first("botId").as("botId")
                .first("targetType").as("targetType")
                .first("targetId").as("targetId");

        aggregationOperationList.add(groupOperation);
        Aggregation aggregation = Aggregation.newAggregation(aggregationOperationList);

        return readMongoTemplate.aggregate(aggregation, MongoCollectionNameCenter.INTENT_TRIGGER_STATS, IntentTriggerStatsPO.class)
                .getMappedResults();
    }

    @Override
    public List<IntentTriggerStatsPO> queryIntentTriggerKnowledgeStats(Long botId, List<String> knowledgeIdList, BaseStatsQuery condition) {
        long beginEpochHour = BotStatsUtil.toEpochHour(BotStatsUtil.getBeginDateTimeOrDefault(condition.getBeginTime()));
        long endEpochHour = BotStatsUtil.toEpochHour(BotStatsUtil.getEndDateTimeOrDefault(condition.getEndTime()));
        List<AggregationOperation> aggregationOperationList = new ArrayList<>();
        aggregationOperationList.add(BotStatsUtil.generateCommonEpochHourCondition(beginEpochHour, endEpochHour));
        aggregationOperationList.add(Aggregation.match(Criteria.where("botId").is(botId)));
        aggregationOperationList.add(Aggregation.match(Criteria.where("targetType").is(AnswerSourceEnum.KNOWLEDGE.name())));
        if (CollectionUtils.isNotEmpty(knowledgeIdList)) {
            aggregationOperationList.add(Aggregation.match(Criteria.where("targetId").in(knowledgeIdList)));
        }
        BotStatsUtil.generateTenantCondition(condition).ifPresent(aggregationOperationList::add);
        BotStatsUtil.generateCallJobCondition(condition).ifPresent(aggregationOperationList::add);


        AggregationOperation groupOperation = Aggregation.group( "targetType", "targetId")
                .sum("count").as("count")
                .sum("callCount").as("callCount")
                .first("botId").as("botId")
                .first("targetType").as("targetType")
                .first("targetId").as("targetId");

        aggregationOperationList.add(groupOperation);
        Aggregation aggregation = Aggregation.newAggregation(aggregationOperationList);

        return readMongoTemplate.aggregate(aggregation, MongoCollectionNameCenter.INTENT_TRIGGER_STATS, IntentTriggerStatsPO.class)
                .getMappedResults();
    }

    @Override
    public List<IntentTriggerStatsPO> queryIntentTriggerSpecialAnswerStats(Long botId, BaseStatsQuery condition) {
        long beginEpochHour = BotStatsUtil.toEpochHour(BotStatsUtil.getBeginDateTimeOrDefault(condition.getBeginTime()));
        long endEpochHour = BotStatsUtil.toEpochHour(BotStatsUtil.getEndDateTimeOrDefault(condition.getEndTime()));
        List<AggregationOperation> aggregationOperationList = new ArrayList<>();
        aggregationOperationList.add(BotStatsUtil.generateCommonEpochHourCondition(beginEpochHour, endEpochHour));
        aggregationOperationList.add(Aggregation.match(Criteria.where("botId").is(botId)));
        aggregationOperationList.add(Aggregation.match(Criteria.where("targetType").is(AnswerSourceEnum.SPECIAL_ANSWER.name())));
        BotStatsUtil.generateTenantCondition(condition).ifPresent(aggregationOperationList::add);
        BotStatsUtil.generateCallJobCondition(condition).ifPresent(aggregationOperationList::add);


        AggregationOperation groupOperation = Aggregation.group( "targetType", "targetId")
                .sum("count").as("count")
                .sum("callCount").as("callCount")
                .first("botId").as("botId")
                .first("targetType").as("targetType")
                .first("targetId").as("targetId");

        aggregationOperationList.add(groupOperation);
        Aggregation aggregation = Aggregation.newAggregation(aggregationOperationList);

        return readMongoTemplate.aggregate(aggregation, MongoCollectionNameCenter.INTENT_TRIGGER_STATS, IntentTriggerStatsPO.class)
                .getMappedResults();
    }

    @Override
    public void saveIntentTriggerStats(BotStatsAnalysisResult analysisResult) {
        if (MapUtils.isEmpty(analysisResult.getIntentTriggerCountMap())) {
            return;
        }
        analysisResult.getIntentTriggerCountMap().forEach((k, v) -> {
            if (v.get() > 0) {
                Query query = BotStatsUtil.generateCommonQuery(analysisResult);
                query.addCriteria(Criteria.where("targetType").is(k.getTargetType().name()));
                query.addCriteria(Criteria.where("targetId").is(k.getTargetId()));
                Update update = new Update();
                update.inc("count", v.get());
                update.inc("callCount", 1);
                callStatsMongoService.updateMongoDataUsingCache(MongoCollectionNameCenter.INTENT_TRIGGER_STATS, query, update);
            }
        });
    }
}
