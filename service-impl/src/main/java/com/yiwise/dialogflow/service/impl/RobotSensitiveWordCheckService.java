package com.yiwise.dialogflow.service.impl;

import com.yiwise.dialogflow.engine.share.enums.AnswerSourceEnum;
import com.yiwise.dialogflow.engine.share.response.AnswerLocateBO;
import com.yiwise.dialogflow.entity.bo.SnapshotInvalidFailItemMsg;
import com.yiwise.dialogflow.entity.bo.SnapshotValidateConfigBO;
import com.yiwise.dialogflow.entity.context.RobotResourceContext;
import com.yiwise.dialogflow.entity.enums.BotResourceTypeEnum;
import com.yiwise.dialogflow.entity.vo.audio.BaseAnswerContentVO;
import com.yiwise.dialogflow.service.AnswerManagerService;
import com.yiwise.dialogflow.service.DefaultRobotValidateServiceAdaptor;
import com.yiwise.dialogflow.service.remote.DialogFlowSensitiveWordsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class RobotSensitiveWordCheckService extends DefaultRobotValidateServiceAdaptor {
    @Resource
    private DialogFlowSensitiveWordsService sensitiveWordsService;

    @Resource
    private AnswerManagerService answerManagerService;

    @Override
    public void validateResource(RobotResourceContext context) {
        SnapshotValidateConfigBO validateConfig = context.getValidateConfig();
        // 默认检查敏感词
        List<BaseAnswerContentVO> allAnswerList = answerManagerService.getAnswerFromSnapshot(context.getSnapshot());
        context.getInvalidMsgList().addAll(checkSensitiveWords(allAnswerList));
    }

    private List<SnapshotInvalidFailItemMsg> checkSensitiveWords(List<BaseAnswerContentVO> newAnswerList) {
        if (CollectionUtils.isEmpty(newAnswerList)) {
            return Collections.emptyList();
        }
        //查询敏感词
        log.debug("获取敏感词列表");
        List<List<String>> wordList = sensitiveWordsService.getSensitiveWordsList();
        if (CollectionUtils.isEmpty(wordList)) {
            return Collections.emptyList();
        }
        List<String> wordSet = new ArrayList<>();
        for (List<String> itemSet : wordList) {
            wordSet.addAll(itemSet);
        }
        List<SnapshotInvalidFailItemMsg> failList = new ArrayList<>();
        for (BaseAnswerContentVO baseAnswerContent : newAnswerList) {
            for (String word : wordSet) {
                if (baseAnswerContent.getText().contains(word)) {
                    String content = String.format("答案[%s]包含敏感词:[%s]", baseAnswerContent.getText(), word);
                    SnapshotInvalidFailItemMsg msg = createFromAnswerLocate(content, baseAnswerContent.getLocate());
                    failList.add(msg);
                }
            }
        }
        return failList;
    }

    private SnapshotInvalidFailItemMsg createFromAnswerLocate(String failContent, AnswerLocateBO locate) {
        SnapshotInvalidFailItemMsg.SnapshotInvalidFailItemMsgBuilder builder = SnapshotInvalidFailItemMsg.builder();
        builder.failMsg(failContent);
        switch (locate.getAnswerSource()) {
            case STEP:
                builder.resourceType(BotResourceTypeEnum.NODE)
                        .resourceId(locate.getStepId())
                        .resourceName(locate.getStepName())
                        .resourceLabel(locate.getStepLabel())
                        .nodeId(locate.getNodeId())
                        .nodeLabel(locate.getNodeLabel())
                        .nodeName(locate.getNodeName());
                break;
            case SPECIAL_ANSWER:
                builder.resourceType(BotResourceTypeEnum.SPECIAL_ANSWER_CONFIG)
                        .resourceId(locate.getSpecialAnswerConfigId())
                        .resourceName(locate.getSpecialAnswerConfigName())
                        .resourceLabel(locate.getSpecialAnswerConfigLabel());
                break;
            case KNOWLEDGE:
                builder.resourceType(BotResourceTypeEnum.KNOWLEDGE)
                        .resourceId(locate.getKnowledgeId())
                        .resourceName(locate.getKnowledgeName())
                        .resourceLabel(locate.getKnowledgeLabel());
                break;
            default:
                break;
        }

        return builder.build();
    }


    private BotResourceTypeEnum convert(AnswerSourceEnum answerSource) {
        switch (answerSource) {
            case KNOWLEDGE: return BotResourceTypeEnum.KNOWLEDGE;
            case SPECIAL_ANSWER: return BotResourceTypeEnum.SPECIAL_ANSWER_CONFIG;
            case STEP: return BotResourceTypeEnum.MAIN_STEP;
        }
        return null;
    }

    private String generateLocateInfo(AnswerLocateBO locate) {
        if (Objects.isNull(locate) || Objects.isNull(locate.getAnswerSource())) {
            return "";
        }
        return locate.getDisplayName();
    }
}
