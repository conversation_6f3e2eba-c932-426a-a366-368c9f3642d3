package com.yiwise.dialogflow.service.impl.stats;

import com.yiwise.base.common.utils.collection.MyCollectionUtils;
import com.yiwise.dialogflow.common.MongoCollectionNameCenter;
import com.yiwise.dialogflow.entity.bo.stats.BotStatsAnalysisResult;
import com.yiwise.dialogflow.entity.enums.NodeJumpTargetTypeEnum;
import com.yiwise.dialogflow.entity.po.*;
import com.yiwise.dialogflow.entity.po.intent.IntentPO;
import com.yiwise.dialogflow.entity.po.stats.StepNodeJumpStatsPO;
import com.yiwise.dialogflow.entity.query.BaseStatsQuery;
import com.yiwise.dialogflow.entity.query.StepNodeQuery;
import com.yiwise.dialogflow.entity.vo.stats.CommonPercentVO;
import com.yiwise.dialogflow.entity.vo.stats.NodeJumpStatsVO;
import com.yiwise.dialogflow.service.*;
import com.yiwise.dialogflow.service.intent.IntentService;
import com.yiwise.dialogflow.service.stats.AnswerStatsService;
import com.yiwise.dialogflow.service.stats.StepNodeStatsService;
import com.yiwise.dialogflow.utils.BotStatsUtil;
import com.yiwise.middleware.mongodb.ext.MongoTemplateExt;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class StepNodeStatsServiceImpl implements StepNodeStatsService {

    @Resource
    private MongoTemplate readMongoTemplate;

    @Resource
    private KnowledgeService knowledgeService;

    @Resource
    private SpecialAnswerConfigService specialAnswerConfigService;

    @Resource
    private AnswerStatsService answerStatsService;

    @Resource
    private IntentService intentService;

    @Resource
    private StepNodeService stepNodeService;

    @Resource
    private StepService stepService;

    @Resource
    private CallStatsMongoService callStatsMongoService;

    @Override
    public void saveNodeJumpStats(BotStatsAnalysisResult analysisResult) {
        if (MapUtils.isEmpty(analysisResult.getNodeJumpCountMap())) {
            return;
        }

        analysisResult.getNodeJumpCountMap().forEach((k, v) -> {
            Query query = BotStatsUtil.generateCommonQuery(analysisResult);
            query.addCriteria(Criteria.where("stepId").is(k.getStepId()));
            query.addCriteria(Criteria.where("nodeId").is(k.getNodeId()));
            query.addCriteria(Criteria.where("intentId").is(k.getIntentId()));
            query.addCriteria(Criteria.where("targetType").is(k.getTargetType().name()));
            query.addCriteria(Criteria.where("targetId").is(k.getTargetId()));

            Update update = new Update();
            update.inc("count", v.get());
            update.inc("callCount", 1);

            callStatsMongoService.updateMongoDataUsingCache(MongoCollectionNameCenter.STEP_NODE_JUMP_STATS, query, update);
        });
    }

    private Set<String> getAnswerTemplateSet(List<? extends BaseAnswerContent> answerList) {
        if (CollectionUtils.isEmpty(answerList)) {
            return Collections.emptySet();
        }
        return answerList.stream().map(BaseAnswerContent::getText).map(StringUtils::trimToEmpty).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
    }

    private CommonPercentVO calculatePercent(int itemCount, int totalCount) {
        if (totalCount <= 0 || itemCount <= 0) {
            return new CommonPercentVO(0, 0);
        }
        return new CommonPercentVO(itemCount, (double) itemCount / totalCount);
    }

    private boolean checkJumpTargetIsAvailable(StepNodeJumpStatsPO statsItem,
                                               Set<String> knowledgeIdSet,
                                               Set<String> stepIdSet,
                                               Set<String> specialAnswerConfigIdSet,
                                               Set<String> intentIdSet) {

        String jumpTargetType = statsItem.getTargetType();
        String jumpTargetId = statsItem.getTargetId();
        String intentId = statsItem.getIntentId();

        if (StringUtils.equals(jumpTargetType, NodeJumpTargetTypeEnum.KNOWLEDGE.name())) {
            return knowledgeIdSet.contains(jumpTargetId);
        } else if (StringUtils.equals(jumpTargetType, NodeJumpTargetTypeEnum.STEP.name())) {
            return stepIdSet.contains(jumpTargetId);
        } else if (StringUtils.equals(jumpTargetType, NodeJumpTargetTypeEnum.SPECIAL_ANSWER.name())) {
            return specialAnswerConfigIdSet.contains(jumpTargetId);
        } else if (StringUtils.equals(jumpTargetType, NodeJumpTargetTypeEnum.NODE.name())) {
            return intentIdSet.contains(intentId);
        }
        return false;
    }

    private List queryStepNodeStats(StepNodeQuery condition) {
        return null;
    }

    private List queryStepNodeStats(StepNodeQuery condition, List<DialogBaseNodePO> nodeList) {
        return null;
    }


    @Override
    public List<StepNodeJumpStatsPO> queryNodesAllJumpStats(Long botId, String stepId, List<String> nodeIdList, BaseStatsQuery condition) {
        long beginEpochHour = BotStatsUtil.toEpochHour(BotStatsUtil.getBeginDateTimeOrDefault(condition.getBeginTime()));
        long endEpochHour = BotStatsUtil.toEpochHour(BotStatsUtil.getEndDateTimeOrDefault(condition.getEndTime()));
        List<AggregationOperation> aggregationOperationList = new ArrayList<>();
        aggregationOperationList.add(BotStatsUtil.generateCommonEpochHourCondition(beginEpochHour, endEpochHour));
        aggregationOperationList.add(Aggregation.match(Criteria.where("botId").is(botId)));
        aggregationOperationList.add(Aggregation.match(Criteria.where("stepId").is(stepId)));
        if (CollectionUtils.isNotEmpty(nodeIdList)) {
            aggregationOperationList.add(Aggregation.match(Criteria.where("nodeId").in(nodeIdList)));
        }

        BotStatsUtil.generateTenantCondition(condition).ifPresent(aggregationOperationList::add);
        BotStatsUtil.generateCallJobCondition(condition).ifPresent(aggregationOperationList::add);

        AggregationOperation groupOperation = Aggregation.group("nodeId", "targetType", "targetId", "intentId")
                .sum("count").as("count")
                .sum("callCount").as("callCount")
                .first("botId").as("botId")
                .first("stepId").as("stepId")
                .first("nodeId").as("nodeId")
                .first("intentId").as("intentId")
                .first("targetType").as("targetType")
                .first("targetId").as("targetId");

        aggregationOperationList.add(groupOperation);
        Aggregation aggregation = Aggregation.newAggregation(aggregationOperationList);
        return readMongoTemplate.aggregate(aggregation, MongoCollectionNameCenter.STEP_NODE_JUMP_STATS, StepNodeJumpStatsPO.class)
                .getMappedResults();
    }

    @Override
    public List<StepNodeJumpStatsPO> queryNodeJumpStats(Long botId, String stepId, String nodeId, BaseStatsQuery condition) {
        long beginEpochHour = BotStatsUtil.toEpochHour(BotStatsUtil.getBeginDateTimeOrDefault(condition.getBeginTime()));
        long endEpochHour = BotStatsUtil.toEpochHour(BotStatsUtil.getEndDateTimeOrDefault(condition.getEndTime()));
        List<AggregationOperation> aggregationOperationList = new ArrayList<>();
        aggregationOperationList.add(BotStatsUtil.generateCommonEpochHourCondition(beginEpochHour, endEpochHour));
        aggregationOperationList.add(Aggregation.match(Criteria.where("botId").is(botId)));
        aggregationOperationList.add(Aggregation.match(Criteria.where("stepId").is(stepId)));
        aggregationOperationList.add(Aggregation.match(Criteria.where("nodeId").is(nodeId)));

        BotStatsUtil.generateTenantCondition(condition).ifPresent(aggregationOperationList::add);
        BotStatsUtil.generateCallJobCondition(condition).ifPresent(aggregationOperationList::add);

        AggregationOperation groupOperation = Aggregation.group("nodeId", "targetType", "targetId", "intentId")
                .sum("count").as("count")
                .sum("callCount").as("callCount")
                .first("botId").as("botId")
                .first("stepId").as("stepId")
                .first("nodeId").as("nodeId")
                .first("intentId").as("intentId")
                .first("targetType").as("targetType")
                .first("targetId").as("targetId");

        aggregationOperationList.add(groupOperation);
        Aggregation aggregation = Aggregation.newAggregation(aggregationOperationList);

        return readMongoTemplate.aggregate(aggregation, MongoCollectionNameCenter.STEP_NODE_JUMP_STATS, StepNodeJumpStatsPO.class)
                .getMappedResults();
    }

    private List<StepNodeJumpStatsPO> queryBotIntentTriggerStats(Long botId, BaseStatsQuery condition) {
        long beginEpochHour = BotStatsUtil.toEpochHour(BotStatsUtil.getBeginDateTimeOrDefault(condition.getBeginTime()));
        long endEpochHour = BotStatsUtil.toEpochHour(BotStatsUtil.getEndDateTimeOrDefault(condition.getEndTime()));
        List<AggregationOperation> aggregationOperationList = new ArrayList<>();
        aggregationOperationList.add(BotStatsUtil.generateCommonEpochHourCondition(beginEpochHour, endEpochHour));
        aggregationOperationList.add(Aggregation.match(Criteria.where("botId").is(botId)));
        aggregationOperationList.add(Aggregation.match(Criteria.where("intentId").ne(null)));
        aggregationOperationList.add(Aggregation.match(Criteria.where("targetType")
                .in(Arrays.asList(NodeJumpTargetTypeEnum.KNOWLEDGE.name(),
                        NodeJumpTargetTypeEnum.STEP.name(),
                        NodeJumpTargetTypeEnum.SPECIAL_ANSWER.name())))
        );

        BotStatsUtil.generateTenantCondition(condition).ifPresent(aggregationOperationList::add);
        BotStatsUtil.generateCallJobCondition(condition).ifPresent(aggregationOperationList::add);


        AggregationOperation groupOperation = Aggregation.group("targetType", "targetId", "intentId")
                .sum("count").as("count")
                .sum("callCount").as("callCount")
                .first("botId").as("botId")
                .first("stepId").as("stepId")
                .first("nodeId").as("nodeId")
                .first("intentId").as("intentId")
                .first("targetType").as("targetType")
                .first("targetId").as("targetId");

        aggregationOperationList.add(groupOperation);
        Aggregation aggregation = Aggregation.newAggregation(aggregationOperationList);

        return readMongoTemplate.aggregate(aggregation, MongoCollectionNameCenter.STEP_NODE_JUMP_STATS, StepNodeJumpStatsPO.class)
                .getMappedResults();
    }



}
