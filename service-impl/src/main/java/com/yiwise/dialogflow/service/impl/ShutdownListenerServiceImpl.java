package com.yiwise.dialogflow.service.impl;

import com.alibaba.cloud.nacos.registry.NacosServiceRegistry;
import com.yiwise.base.common.context.AppContextUtils;
import com.yiwise.base.common.helper.ServerInfoConstants;
import com.yiwise.dialogflow.service.ShutdownListener;
import com.yiwise.dialogflow.utils.LogUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.client.serviceregistry.Registration;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;

@Slf4j
@Service
public class ShutdownListenerServiceImpl implements ShutdownListener {

    private final AtomicBoolean shutdown = new AtomicBoolean(false);

    @Resource
    private NacosServiceRegistry nacosServiceRegistry;

    @Resource
    private Registration registration;

    @Override
    public void beforeShutdown() {

    }

    @EventListener(ContextClosedEvent.class)
    public void contextClosed() {
        if (!shutdown.compareAndSet(false, true)) {
            return;
        }
        log.info("contextClosed");
        unregister();
        LogUtils.setLoggerImmediateFlush(true, "asyncRollingFileAppender", "rollingFileAppender");
        delay(30);
        // 获取所有的实现了ShutdownListener接口的实现类
        Map<String, ShutdownListener> shutdownListenerMap = AppContextUtils.getContext().getBeansOfType(ShutdownListener.class);
        shutdownListenerMap.forEach((k, v) -> {
            try {
                log.info("开始执行{}.beforeShutdown", v.getClass().getSimpleName());
                v.beforeShutdown();
                log.info("执行完成{}.beforeShutdown", v.getClass().getSimpleName());
            } catch (Exception e) {
                log.error("ShutdownListenerServiceImpl beforeShutdown error", e);
            }
        });
    }

    private static void delay(long delaySecond) {
        long start = System.currentTimeMillis();
        long end = start + delaySecond * 1000;
        long now;
        while ((now = System.currentTimeMillis()) < end) {
            try {
                log.info("正在等待关闭{}, 剩余时间: {}s", ServerInfoConstants.SERVER_HOSTNAME, (end - now) / 1000);
                Thread.sleep(1000);
            } catch (InterruptedException ignore) {

            }
        }
    }

    private void unregister() {
        try {
            nacosServiceRegistry.deregister(registration);
        } catch (Exception e) {
            log.error("unregister error", e);
        }
    }

}
