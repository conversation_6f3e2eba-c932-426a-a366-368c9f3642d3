package com.yiwise.dialogflow.service.impl;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.yiwise.dialogflow.api.enums.V3BotTypeEnum;
import com.yiwise.dialogflow.common.MdcLogIdLifterTransformer;
import com.yiwise.dialogflow.entity.po.BotPO;
import com.yiwise.dialogflow.entity.po.BotRefPO;
import com.yiwise.dialogflow.mapper.BotRefPOMapper;
import com.yiwise.dialogflow.service.BotRefService;
import com.yiwise.dialogflow.service.BotService;
import com.yiwise.middleware.mysql.service.impl.BasicServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class BotRefServiceImpl extends BasicServiceImpl<BotRefPO> implements BotRefService {

    @Resource
    private BotRefPOMapper botRefPOMapper;

    @Lazy
    @Resource
    private BotService botService;

    // todo 目前新数据botId 和 dialogFlowId 一一对应, 还有一部分老数据需要查询处理
    private final Cache<Long, Long> dialogFlowId2BotIdCache = CacheBuilder.newBuilder()
            .expireAfterAccess(1, java.util.concurrent.TimeUnit.HOURS)
            .maximumSize(1000)
            .build();

    private final Cache<Long, Long> botId2DialogFlowIdCache = CacheBuilder.newBuilder()
            .expireAfterAccess(1, java.util.concurrent.TimeUnit.HOURS)
            .maximumSize(1000)
            .build();

    @Override
    public void create(BotRefPO botRefPO) {
        // todo botRef 这个表删掉
        botRefPO.setCreateTime(LocalDateTime.now());
        botRefPOMapper.insertSelective(botRefPO);
    }

    @Override
    public Long getBotId(Long dialogFlowId) {
        Long botId = dialogFlowId2BotIdCache.getIfPresent(dialogFlowId);
        if (Objects.nonNull(botId)) {
            return botId < 0 ? null : botId;
        }
        botId = botRefPOMapper.getBotId(dialogFlowId);
        if (Objects.nonNull(botId)) {
            dialogFlowId2BotIdCache.put(dialogFlowId, botId);
        } else {
            dialogFlowId2BotIdCache.put(dialogFlowId, -1L);
        }
        return botId;
    }

    @Override
    public Mono<Long> asyncGetBotId(Long dialogFlowId) {
        Long cacheBotId = dialogFlowId2BotIdCache.getIfPresent(dialogFlowId);
        if (Objects.nonNull(cacheBotId)) {
            return cacheBotId < 0 ? Mono.empty() : Mono.just(cacheBotId);
        }
        return Mono.fromCallable(() -> {
                    Long botId = botRefPOMapper.getBotId(dialogFlowId);
                    if (Objects.nonNull(botId)) {
                        dialogFlowId2BotIdCache.put(dialogFlowId, botId);
                    } else {
                        dialogFlowId2BotIdCache.put(dialogFlowId, -1L);
                    }
                    return botId;
                })
                .subscribeOn(Schedulers.elastic())
                .transform(MdcLogIdLifterTransformer.lift())
                .doOnNext(botId -> {
                    log.info("asyncGetBotId dialogFlowId: {}, botId: {}", dialogFlowId, botId);
                });
    }

    @Override
    public Long getDialogFlowId(Long botId) {
        Long dialogFlowId = botId2DialogFlowIdCache.getIfPresent(botId);
        if (Objects.nonNull(dialogFlowId)) {
            return dialogFlowId < 0 ? null : dialogFlowId;
        }
        dialogFlowId = botRefPOMapper.getDialogFlowId(botId);
        if (Objects.nonNull(dialogFlowId)) {
            botId2DialogFlowIdCache.put(botId, dialogFlowId);
        } else {
            botId2DialogFlowIdCache.put(botId, -1L);
        }
        return dialogFlowId;
    }

    @Override
    public List<BotRefPO> getByBotIdList(List<Long> botIdList) {
        if (CollectionUtils.isEmpty(botIdList)) {
            return Collections.emptyList();
        }
        return botRefPOMapper.getByBotIdList(botIdList);
    }

    @Override
    public List<BotRefPO> getByDialogFlowIdList(List<Long> dialogFlowIdList) {
        if (CollectionUtils.isEmpty(dialogFlowIdList)) {
            return Collections.emptyList();
        }
        return botRefPOMapper.getByDialogFlowIdList(dialogFlowIdList);
    }

    @Override
    public void deleteByDialogFlowId(Long dialogFlowId) {
        botRefPOMapper.deleteByDialogFlowId(dialogFlowId);
    }

    @Override
    public void deleteByBotId(Long botId) {
        botRefPOMapper.deleteByBotId(botId);
    }

    @Override
    public Long getTenantIdByBotId(Long botId) {
        return botRefPOMapper.getTenantIdByBotId(botId);
    }

    @Override
    public void updateByDialogFlowIds(List<Long> dialogFlowIds, Long tenantId) {
        botRefPOMapper.updateByDialogFlowIds(dialogFlowIds, tenantId);
    }

    @Override
    public List<Long> getBotIdList(List<Long> dialogFlowIds) {
        return botRefPOMapper.getBotIdListByDialogFlowIds(dialogFlowIds);
    }

    @Override
    public List<BotRefPO> getAllBindList() {
        return botRefPOMapper.getAllBindList();
    }

    @Override
    public boolean checkHasPermission(Long botId, Long tenantId) {
        return checkAllHasPermission(Collections.singletonList(botId), tenantId);
    }

    @Override
    public boolean checkAllHasPermission(List<Long> botIdList, Long tenantId) {
        if (CollectionUtils.isNotEmpty(botIdList)) {
            List<Long> bindTenantIdList = botIdList.stream().map(this::getTenantIdByBotId)
                    .filter(Objects::nonNull).distinct().collect(Collectors.toList());
            log.info("current tenantID = {}, bindTenantIdList = {}", tenantId, bindTenantIdList);
            return CollectionUtils.isNotEmpty(bindTenantIdList) && bindTenantIdList.size() == 1 && bindTenantIdList.get(0).equals(tenantId);
        }
        return true;
    }

    @Override
    public boolean checkHasPermissionByDialogFlowId(Long dialogFlowId, Long tenantId) {
        Long botId = getBotId(dialogFlowId);
        BotPO bot = botService.getById(botId);
        if (Objects.isNull(bot)) {
            return false;
        }
        if (V3BotTypeEnum.isMagicTemplate(bot.getType())) {
            // todo 暂时不鉴权
            return true;
        }
        Long realBind = getTenantIdByBotId(botId);
        botService.checkBotExistAndThrow(botId);
        return Objects.equals(realBind, tenantId);
    }

}
