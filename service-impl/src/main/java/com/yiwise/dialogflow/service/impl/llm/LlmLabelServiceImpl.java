package com.yiwise.dialogflow.service.impl.llm;

import com.yiwise.base.common.utils.collection.MyCollectionUtils;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.entity.bo.ResourceCopyReferenceMappingBO;
import com.yiwise.dialogflow.entity.context.RobotResourceContext;
import com.yiwise.dialogflow.entity.enums.LlmLabelTypeEnum;
import com.yiwise.dialogflow.entity.po.LlmLabelPO;
import com.yiwise.dialogflow.entity.po.intent.IntentRuleActionPO;
import com.yiwise.dialogflow.entity.po.intent.IntentRulePO;
import com.yiwise.dialogflow.entity.query.BotQuery;
import com.yiwise.dialogflow.entity.vo.BotVO;
import com.yiwise.dialogflow.entity.vo.llm.LlmLabelQueryVO;
import com.yiwise.dialogflow.service.BotService;
import com.yiwise.dialogflow.service.RobotResourceService;
import com.yiwise.dialogflow.service.intent.IntentRuleActionService;
import com.yiwise.dialogflow.service.intent.IntentRuleService;
import com.yiwise.dialogflow.service.llm.LlmLabelService;
import com.yiwise.dialogflow.service.operationlog.LlmLabelOperationLogService;
import com.yiwise.dialogflow.utils.ParseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class LlmLabelServiceImpl implements LlmLabelService, RobotResourceService {

    @Resource
    private MongoTemplate mongoTemplate;

    @Resource
    private LlmLabelOperationLogService llmLabelOperationLogService;

    @Resource
    private IntentRuleService intentRuleService;

    @Resource
    private IntentRuleActionService intentRuleActionService;

    @Resource
    private BotService botService;

    private static final Map<String, List<String>> BUILT_IN_LABEL_LIST;

    static  {
        BUILT_IN_LABEL_LIST = new HashMap<>();
        BUILT_IN_LABEL_LIST.put("高意向", Collections.singletonList("客户询表现出高意向，没有拒绝，且通话完成度较高。"));
        BUILT_IN_LABEL_LIST.put("中意向", Collections.singletonList("客户可能表示过拒绝，但是最后表现出高意向，且通话完成度较高。"));
        BUILT_IN_LABEL_LIST.put("不确定", Collections.singletonList("客户表达过肯定，没有拒绝。"));
        BUILT_IN_LABEL_LIST.put("无意向", Collections.singletonList("客户明确表示拒绝或机器人通话内容较少。"));
    }

    private Query buildQuery(LlmLabelQueryVO request) {
        Query query = new Query();
        query.addCriteria(Criteria.where("botId").is(request.getBotId()));
        if (StringUtils.isNotBlank(request.getSearch())) {
            String regex = ParseUtil.regex(request.getSearch());
            query.addCriteria(
                    new Criteria().orOperator(
                            Criteria.where("name").regex(regex),
                            Criteria.where("descList").regex(regex)
                    )
            );
        }
        if (Objects.nonNull(request.getType())) {
            query.addCriteria(Criteria.where("type").is(request.getType()));
        }
        return query;
    }

    @Override
    public void initOnCreateBot(Long botId, Long userId) {
        LocalDateTime now = LocalDateTime.now();
        List<LlmLabelPO> labelList = new ArrayList<>();
        BUILT_IN_LABEL_LIST.forEach((name, descList) -> {
            LlmLabelPO label = new LlmLabelPO();
            label.setBotId(botId);
            label.setName(name);
            label.setDescList(descList);
            label.setType(LlmLabelTypeEnum.BUILTIN);
            label.setCreateTime(now);
            label.setUpdateTime(now);
            label.setCreateUserId(userId);
            label.setUpdateUserId(userId);
            labelList.add(label);
        });
        mongoTemplate.insertAll(labelList);
    }

    @Override
    public void fixData() {
        List<BotVO> allBotList = botService.queryListWithoutPage(new BotQuery());
        for (int i = 0; i < allBotList.size(); i++) {
            BotVO bot = allBotList.get(i);
            fixDataByBotId(bot.getBotId());
            log.info("botId={}的bot初始化内置分类完成, progress:{}/{}", bot.getBotId(), i + 1, allBotList.size());
        }
    }

    @Override
    public void fixDataByBotId(Long botId) {
        LocalDateTime now = LocalDateTime.now();
        List<LlmLabelPO> labelList = new ArrayList<>();
        List<LlmLabelPO> existsLabelList = mongoTemplate.find(Query.query(Criteria.where("botId").is(botId).and("type").is(LlmLabelTypeEnum.BUILTIN)), LlmLabelPO.class, LlmLabelPO.COLLECTION_NAME);
        List<String> existsLabelNameList = MyCollectionUtils.listToConvertList(existsLabelList, LlmLabelPO::getName);
        BUILT_IN_LABEL_LIST.forEach((name, descList) -> {
            if (!existsLabelNameList.contains(name)) {
                LlmLabelPO label = new LlmLabelPO();
                label.setBotId(botId);
                label.setName(name);
                label.setDescList(descList);
                label.setType(LlmLabelTypeEnum.BUILTIN);
                label.setCreateTime(now);
                label.setUpdateTime(now);
                labelList.add(label);
            }
        });
        if (CollectionUtils.isNotEmpty(labelList)) {
            mongoTemplate.insertAll(labelList);
        }
    }

    @Override
    public List<LlmLabelPO> list(LlmLabelQueryVO request) {
        Query query = buildQuery(request);
        query.with(Sort.by(Sort.Direction.DESC, "_id"));
        return mongoTemplate.find(query, LlmLabelPO.class, LlmLabelPO.COLLECTION_NAME);
    }

    @Override
    public List<LlmLabelPO> getAllByBotId(Long botId) {
        return mongoTemplate.find(Query.query(Criteria.where("botId").is(botId)), LlmLabelPO.class, LlmLabelPO.COLLECTION_NAME);
    }

    private void validateParam(LlmLabelPO request) {
        if (Objects.isNull(request.getBotId())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "botId不能为空");
        }
        if (StringUtils.isBlank(request.getName())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "名称不能为空");
        }
        if (Objects.isNull(request.getType())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "类型不能为空");
        }
        if (LlmLabelTypeEnum.isCustomized(request.getType()) && CollectionUtils.isEmpty(request.getDescList())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "描述不能为空");
        }
    }

    private LlmLabelPO getByBotIdAndName(Long botId, String name) {
        return mongoTemplate.findOne(Query.query(Criteria.where("botId").is(botId).and("name").is(name)), LlmLabelPO.class, LlmLabelPO.COLLECTION_NAME);
    }

    @Override
    public LlmLabelPO create(LlmLabelPO request, Long userId) {
        validateParam(request);
        request.setId(null);

        if (Objects.nonNull(getByBotIdAndName(request.getBotId(), request.getName()))) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "名称已存在");
        }

        checkDescExists(request.getBotId(), request.getDescList());

        LocalDateTime now = LocalDateTime.now();
        request.setCreateTime(now);
        request.setUpdateTime(now);
        request.setCreateUserId(userId);
        request.setUpdateUserId(userId);
        mongoTemplate.save(request, LlmLabelPO.COLLECTION_NAME);

        botService.onUpdateBotResource(request.getBotId());

        llmLabelOperationLogService.create(request.getBotId(), request.getName(), userId);
        return request;
    }

    private void checkDescExists(Long botId, List<String> descList) {
        if (CollectionUtils.isEmpty(descList)) {
            return;
        }
        Map<String, List<String>> existsDescMap = findExistsDescMap(botId, descList);
        if (MapUtils.isNotEmpty(existsDescMap)) {
            StringJoiner sj = new StringJoiner(";");
            existsDescMap.forEach((desc, list) -> sj.add(String.format("描述[%s]已存在于分类[%s]", desc, String.join(",", list))));
            throw new ComException(ComErrorCode.VALIDATE_ERROR, sj.toString());
        }
    }

    private Map<String, List<String>> findExistsDescMap(Long botId, List<String> descList) {
        List<LlmLabelPO> labelList = mongoTemplate.find(
                Query.query(Criteria.where("botId").is(botId).and("descList").in(descList)), LlmLabelPO.class, LlmLabelPO.COLLECTION_NAME
        );
        if (CollectionUtils.isEmpty(labelList)) {
            return Collections.emptyMap();
        }
        Map<String, List<String>> resultMap = new HashMap<>();
        for (LlmLabelPO label : labelList) {
            for (String desc : label.getDescList()) {
                if (descList.contains(desc)) {
                    resultMap.computeIfAbsent(desc, k -> new ArrayList<>()).add(label.getName());
                }
            }
        }
        return resultMap;
    }

    @Override
    public LlmLabelPO update(LlmLabelPO request, Long userId) {
        validateParam(request);
        if (StringUtils.isBlank(request.getId())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "id不能为空");
        }
        LlmLabelPO old = mongoTemplate.findById(request.getId(), LlmLabelPO.class, LlmLabelPO.COLLECTION_NAME);
        if (Objects.isNull(old)) {
            throw new ComException(ComErrorCode.NOT_EXIST, "分类不存在");
        }
        if (!StringUtils.equals(old.getName(), request.getName()) && Objects.nonNull(getByBotIdAndName(request.getBotId(), request.getName()))) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "名称已存在");
        }

        checkDescExists(request.getBotId(), ListUtils.subtract(request.getDescList(), old.getDescList()));

        llmLabelOperationLogService.update(old, request, userId);

        old.setName(request.getName());
        old.setDescList(request.getDescList());
        old.setUpdateTime(LocalDateTime.now());
        old.setUpdateUserId(userId);
        mongoTemplate.save(old, LlmLabelPO.COLLECTION_NAME);

        botService.onUpdateBotResource(old.getBotId());
        return old;
    }

    @Override
    public void delete(String id, Long userId) {
        LlmLabelPO label = mongoTemplate.findById(id, LlmLabelPO.class, LlmLabelPO.COLLECTION_NAME);
        if (Objects.isNull(label)) {
            return;
        }
        List<IntentRulePO> ruleList = intentRuleService.findLlmLabelRule(label.getBotId(), label.getId());
        if (CollectionUtils.isNotEmpty(ruleList)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, String.format("被意向配置[%s]引用，删除失败",
                    ruleList.stream().map(IntentRulePO::getMatchOrder).map(String::valueOf).collect(Collectors.joining(","))));
        }
        List<IntentRuleActionPO> actionList = intentRuleActionService.findLlmLabelAction(label.getBotId(), label.getId());
        if (CollectionUtils.isNotEmpty(actionList)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, String.format("被动作配置[%s]引用，删除失败",
                    actionList.stream().map(IntentRulePO::getMatchOrder).map(String::valueOf).collect(Collectors.joining(","))));
        }

        mongoTemplate.remove(Query.query(Criteria.where("_id").is(id)), LlmLabelPO.COLLECTION_NAME);

        llmLabelOperationLogService.delete(label, userId);

        botService.onUpdateBotResource(label.getBotId());
    }

    @Override
    public void saveToSnapshot(RobotResourceContext context) {
        List<LlmLabelPO> llmLabelList = mongoTemplate.find(Query.query(Criteria.where("botId").is(context.getSrcBotId())), LlmLabelPO.class, LlmLabelPO.COLLECTION_NAME);
        context.getSnapshot().setLlmLabelList(llmLabelList);
    }

    @Override
    public void loadFromSnapshot(RobotResourceContext context) {
        List<LlmLabelPO> llmLabelList = context.getSnapshot().getLlmLabelList();
        if (CollectionUtils.isEmpty(llmLabelList)) {
            return;
        }
        ResourceCopyReferenceMappingBO mapping = context.getResourceCopyReferenceMapping();
        Map<String, String> llmLabelIdMapping = mapping.getLlmLabelIdMapping();
        for (LlmLabelPO label : llmLabelList) {
            String newId = new ObjectId().toString();
            llmLabelIdMapping.put(label.getId(), newId);
            label.setId(newId);
            label.setBotId(context.getTargetBotId());
            mongoTemplate.save(label, LlmLabelPO.COLLECTION_NAME);
        }
    }

    @Override
    public Map<String, String> sync(List<LlmLabelPO> srcList, Long targetBotId) {
        if (CollectionUtils.isEmpty(srcList)) {
            return Collections.emptyMap();
        }
        Map<String, LlmLabelPO> targetLlmLabelMap = MyCollectionUtils.listToMap(getAllByBotId(targetBotId), LlmLabelPO::getName);

        Map<String, String> resultMap = new HashMap<>();
        for (LlmLabelPO src : srcList) {
            LlmLabelPO label = targetLlmLabelMap.get(src.getName());
            if (Objects.nonNull(label)) {
                label.setDescList(src.getDescList());
                mongoTemplate.save(label, LlmLabelPO.COLLECTION_NAME);
                resultMap.put(src.getId(), label.getId());
            } else {
                String newId = new ObjectId().toString();
                resultMap.put(src.getId(), newId);
                src.setId(newId);
                src.setBotId(targetBotId);
                mongoTemplate.save(src, LlmLabelPO.COLLECTION_NAME);
            }
        }
        return resultMap;
    }
}