package com.yiwise.dialogflow.service.impl.magic;

import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.base.common.utils.bean.MyBeanUtils;
import com.yiwise.dialogflow.api.dto.response.activity.MagicFormTemplateDTO;
import com.yiwise.dialogflow.entity.context.RobotResourceContext;
import com.yiwise.dialogflow.entity.po.MagicFormTemplatePO;
import com.yiwise.dialogflow.service.OperationLogService;
import com.yiwise.dialogflow.service.RobotResourceService;
import com.yiwise.dialogflow.service.magic.MagicFormTemplateService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.Optional;

@Service
public class MagicFormTemplateServiceImpl implements MagicFormTemplateService, RobotResourceService {

    @Resource
    private MongoTemplate mongoTemplate;

    @Resource
    private OperationLogService operationLogService;

    @Override
    public MagicFormTemplatePO create(MagicFormTemplatePO template, Long userId) {
        return doSave(template, false, userId);
    }

    @Override
    public MagicFormTemplatePO createAndPublish(MagicFormTemplatePO template, Long userId) {
        return doSave(template, true, userId);
    }

    private Optional<MagicFormTemplatePO> getLast(Long botId) {
        Query query = new Query();
        query.addCriteria(Criteria.where("botId").is(botId));
        query.with(Sort.by(Sort.Direction.DESC, "_id"));
        return Optional.ofNullable(mongoTemplate.findOne(query, MagicFormTemplatePO.class));
    }

    private Optional<MagicFormTemplatePO> getById(Long botId, String id) {
        if (Objects.isNull(botId) || StringUtils.isBlank(id)) {
            return Optional.empty();
        }

        Query query = new Query();
        query.addCriteria(Criteria.where("botId").is(botId))
                .addCriteria(Criteria.where("_id").is(id));
        return Optional.ofNullable(mongoTemplate.findOne(query, MagicFormTemplatePO.class));
    }

    private MagicFormTemplatePO doSave(MagicFormTemplatePO template, boolean published, Long userId) {
        MagicFormTemplatePO old = getLastMagicFormTemplate(template.getBotId());

        // 设置时间
        template.setCreateTime(LocalDateTime.now());
        template.setUpdateTime(LocalDateTime.now());
        
        // 设置发布状态
        template.setPublished(published);
        
        // 如果是发布状态，设置发布时间
        if (published) {
            template.setPublishedDateTime(LocalDateTime.now());
        }

        // 设置创建者
        template.setCreateUserId(userId);
        template.setUpdateUserId(userId);

        if (old != null && BooleanUtils.isTrue(old.getPublished())) {
            // 上一个版本已经发布了, 需要创建
            template.setId(null);
            mongoTemplate.insert(template, MagicFormTemplatePO.COLLECTION_NAME);
        } else {
            mongoTemplate.save(template, MagicFormTemplatePO.COLLECTION_NAME);
        }
        createOperateLog(template, userId, old);
        return template;
    }

    private void createOperateLog(MagicFormTemplatePO template, Long userId, MagicFormTemplatePO lastTemplate) {
        String logDetail = String.format("%s轻量化表单模板: %s", BooleanUtils.isTrue(template.getPublished()) ? "发布" : "创建", renderForm(template));
        operationLogService.save(
                template.getBotId(),
                com.yiwise.dialogflow.entity.enums.OperationLogTypeEnum.MAGIC_FORM_TEMPLATE,
                com.yiwise.dialogflow.entity.enums.OperationLogResourceTypeEnum.MAGIC_FORM_TEMPLATE,
                logDetail,
                userId
        );
    }

    private String renderForm(MagicFormTemplatePO template) {
        if (Objects.isNull(template) || CollectionUtils.isEmpty(template.getFieldList())) {
            return "[]";
        }
        return JsonUtils.object2String(template.getFieldList());
    }

    @Override
    public MagicFormTemplatePO getLastMagicFormTemplate(Long botId) {
        if (Objects.isNull(botId)) {
            return null;
        }
        
        // 构建查询条件
        Query query = new Query();
        query.addCriteria(Criteria.where("botId").is(botId));
        query.with(Sort.by(Sort.Direction.DESC, "_id"));
        query.limit(1);
        
        // 查询最后一条记录
        return mongoTemplate.findOne(query, MagicFormTemplatePO.class, MagicFormTemplatePO.COLLECTION_NAME);
    }

    @Override
    public MagicFormTemplatePO getLastPublishedMagicFormTemplate(Long botId) {
        if (Objects.isNull(botId)) {
            return null;
        }
        
        // 构建查询条件
        Query query = new Query();
        query.addCriteria(Criteria.where("botId").is(botId)
                .and("published").is(true));
        query.with(Sort.by(Sort.Direction.DESC, "_id"));
        query.limit(1);
        
        // 查询最后一条已发布的记录
        return mongoTemplate.findOne(query, MagicFormTemplatePO.class, MagicFormTemplatePO.COLLECTION_NAME);
    }

    @Override
    public MagicFormTemplateDTO getLastPublishedMagicFormTemplateDTO(Long botId) {
        MagicFormTemplatePO lastTemplate = getLastPublishedMagicFormTemplate(botId);
        if (Objects.isNull(lastTemplate)) {
            return null;
        }
        return MyBeanUtils.copy(lastTemplate, MagicFormTemplateDTO.class);
    }

    @Override
    public void saveToSnapshot(RobotResourceContext context) {

    }

    @Override
    public void loadFromSnapshot(RobotResourceContext context) {
        Long botId = context.getSrcBotId();
        MagicFormTemplatePO last = getLastMagicFormTemplate(botId);
        if (last != null) {
            last.setId(null);
            last.setBotId(context.getTargetBotId());
            last.setPublished(false);
            mongoTemplate.insert(last, MagicFormTemplatePO.COLLECTION_NAME);
        }
    }
}
