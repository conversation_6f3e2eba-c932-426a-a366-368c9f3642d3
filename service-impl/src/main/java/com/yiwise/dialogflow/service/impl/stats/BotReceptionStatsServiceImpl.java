package com.yiwise.dialogflow.service.impl.stats;

import com.yiwise.base.common.utils.collection.MyCollectionUtils;
import com.yiwise.dialogflow.common.MongoCollectionNameCenter;
import com.yiwise.dialogflow.entity.bo.stats.BotStatsAnalysisResult;
import com.yiwise.dialogflow.entity.enums.DialStatusEnum;
import com.yiwise.dialogflow.entity.po.stats.BotReceptionStatsPO;
import com.yiwise.dialogflow.entity.po.stats.JobCallDateRangePO;
import com.yiwise.dialogflow.entity.query.BaseStatsQuery;
import com.yiwise.dialogflow.entity.vo.stats.AllBotDailyReceptionStatsVO;
import com.yiwise.dialogflow.entity.vo.stats.AllBotHourlyReceptionStatsVO;
import com.yiwise.dialogflow.service.CallStatsMongoService;
import com.yiwise.dialogflow.service.stats.BotReceptionStatsService;
import com.yiwise.dialogflow.utils.BotStatsUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class BotReceptionStatsServiceImpl implements BotReceptionStatsService {

    @Resource
    private MongoTemplate readMongoTemplate;

    @Resource
    private CallStatsMongoService callStatsMongoService;

    @Override
    public int queryBotReceptionCount(Long botId, BaseStatsQuery condition) {
        long beginEpochHour = BotStatsUtil.toEpochHour(BotStatsUtil.getBeginDateTimeOrDefault(condition.getBeginTime()));
        long endEpochHour = BotStatsUtil.toEpochHour(BotStatsUtil.getEndDateTimeOrDefault(condition.getEndTime()));
        List<AggregationOperation> aggregationOperationList = new ArrayList<>();
        aggregationOperationList.add(BotStatsUtil.generateCommonEpochHourCondition(beginEpochHour, endEpochHour));
        aggregationOperationList.add(Aggregation.match(Criteria.where("botId").is(botId)));
        BotStatsUtil.generateTenantCondition(condition).ifPresent(aggregationOperationList::add);
        BotStatsUtil.generateCallJobCondition(condition).ifPresent(aggregationOperationList::add);


        AggregationOperation groupOperation = Aggregation.group("botId")
                .sum("count").as("count")
                .first("botId").as("botId");

        aggregationOperationList.add(groupOperation);
        Aggregation aggregation = Aggregation.newAggregation(aggregationOperationList);

        List<BotReceptionStatsPO> statsList = readMongoTemplate.aggregate(aggregation, MongoCollectionNameCenter.BOT_RECEPTION_STATS, BotReceptionStatsPO.class)
                .getMappedResults();
        if (CollectionUtils.isEmpty(statsList)) {
            return 0;
        }
        return statsList.get(0).getCount();
    }

    @Override
    public List<Long> queryAllCallJobId(Long botId, BaseStatsQuery condition) {
        long beginEpochHour = BotStatsUtil.toEpochHour(BotStatsUtil.getBeginDateTimeOrDefault(LocalDateTime.now().minusYears(2)));
        long endEpochHour = BotStatsUtil.toEpochHour(BotStatsUtil.getEndDateTimeOrDefault(LocalDateTime.now()));
        List<AggregationOperation> aggregationOperationList = new ArrayList<>();
        aggregationOperationList.add(BotStatsUtil.generateCommonEpochHourCondition(beginEpochHour, endEpochHour));
        aggregationOperationList.add(Aggregation.match(Criteria.where("botId").is(botId)));
        BotStatsUtil.generateTenantCondition(condition).ifPresent(aggregationOperationList::add);


        AggregationOperation groupOperation = Aggregation.group( "callJobId")
                .first("callJobId").as("callJobId")
                .first("tenantId").as("tenantId");

        aggregationOperationList.add(groupOperation);
        Aggregation aggregation = Aggregation.newAggregation(aggregationOperationList);

        List<BotReceptionStatsPO> statsList = readMongoTemplate.aggregate(aggregation, MongoCollectionNameCenter.BOT_RECEPTION_STATS, BotReceptionStatsPO.class)
                .getMappedResults();
        return statsList.stream().map(BotReceptionStatsPO::getCallJobId).filter(Objects::nonNull).collect(Collectors.toList());
    }

    @Override
    public List<JobCallDateRangePO> queryJobReceptionInfo(Long botId, BaseStatsQuery condition) {
        long beginEpochHour = BotStatsUtil.toEpochHour(BotStatsUtil.getBeginDateTimeOrDefault(LocalDateTime.now().minusYears(2)));
        long endEpochHour = BotStatsUtil.toEpochHour(BotStatsUtil.getEndDateTimeOrDefault(LocalDateTime.now()));
        List<AggregationOperation> aggregationOperationList = new ArrayList<>();
        aggregationOperationList.add(BotStatsUtil.generateCommonEpochHourCondition(beginEpochHour, endEpochHour));
        aggregationOperationList.add(Aggregation.match(Criteria.where("botId").is(botId)));
        BotStatsUtil.generateTenantCondition(condition).ifPresent(aggregationOperationList::add);
        BotStatsUtil.generateCallJobCondition(condition).ifPresent(aggregationOperationList::add);


        AggregationOperation groupOperation = Aggregation.group( "callJobId", "maFlowId", "newCallJobId", "callInJobId")
                .first("botId").as("botId")
                .first("callJobId").as("callJobId")
                .first("maFlowId").as("maFlowId")
                .first("newCallJobId").as("newCallJobId")
                .first("epochDay").as("firstEpochDay")
                .last("epochDay").as("lastEpochDay")
                .first("tenantId").as("tenantId");

        aggregationOperationList.add(groupOperation);
        // limit 仅返回 20000 条数据
        aggregationOperationList.add(Aggregation.limit(20000));

        Aggregation aggregation = Aggregation.newAggregation(aggregationOperationList);
        return readMongoTemplate.aggregate(aggregation, MongoCollectionNameCenter.BOT_RECEPTION_STATS, JobCallDateRangePO.class)
                .getMappedResults();
    }

    @Override
    public List<BotReceptionStatsPO> queryBotDailyReceptionInfo(Long botId, BaseStatsQuery condition) {
        long beginEpochHour = BotStatsUtil.toEpochHour(BotStatsUtil.getBeginDateTimeOrDefault(LocalDateTime.now().minusYears(2)));
        long endEpochHour = BotStatsUtil.toEpochHour(BotStatsUtil.getEndDateTimeOrDefault(LocalDateTime.now()));
        List<AggregationOperation> aggregationOperationList = new ArrayList<>();
        aggregationOperationList.add(BotStatsUtil.generateCommonEpochHourCondition(beginEpochHour, endEpochHour));
        aggregationOperationList.add(Aggregation.match(Criteria.where("botId").is(botId)));
        BotStatsUtil.generateTenantCondition(condition).ifPresent(aggregationOperationList::add);
        BotStatsUtil.generateCallJobCondition(condition).ifPresent(aggregationOperationList::add);


        AggregationOperation groupOperation = Aggregation.group( "epochDay")
                .sum("count").as("count")
                .first("year").as("year")
                .first("month").as("month")
                .first("day").as("day")
                .first("tenantId").as("tenantId")
                .first("callJobId").as("callJobId")
                .first("maFlowId").as("maFlowId")
                .first("newCallJobId").as("newCallJobId")
                .first("callInJobId").as("callInJobId");

        aggregationOperationList.add(groupOperation);
        Aggregation aggregation = Aggregation.newAggregation(aggregationOperationList);
        return readMongoTemplate.aggregate(aggregation, MongoCollectionNameCenter.BOT_RECEPTION_STATS, BotReceptionStatsPO.class)
                .getMappedResults();
    }

    @Override
    public void saveBotReceptionStats(BotStatsAnalysisResult analysisResult, int dialStatus) {
        Query query = BotStatsUtil.generateCommonQuery(analysisResult);
        Update update = new Update();
        if (DialStatusEnum.ANSWERED.getCode().equals(dialStatus)) {
            update.inc("count", 1);
        }
        update.inc("totalCallCount", 1);
        callStatsMongoService.updateMongoDataUsingCache(MongoCollectionNameCenter.BOT_RECEPTION_STATS, query, update);
    }

    @Override
    public List<AllBotDailyReceptionStatsVO> queryAllBotDailyReceptionInfo(LocalDate beginDate, LocalDate endDate) {
        long beginEpochDay = BotStatsUtil.toEpochDay(beginDate.atTime(0, 0, 0));
        long endEpochDay = BotStatsUtil.toEpochDay(endDate.atTime(23, 59, 59));

        List<AggregationOperation> aggregationOperationList = new ArrayList<>();
        aggregationOperationList.add(BotStatsUtil.generateCommonEpochDayCondition(beginEpochDay, endEpochDay));

        AggregationOperation groupOperation = Aggregation.group( "epochDay")
                .first("epochDay").as("epochDay")
                .sum("count").as("answeredCount")
                .sum("totalCallCount").as("totalCallCount");

        aggregationOperationList.add(groupOperation);
        Aggregation aggregation = Aggregation.newAggregation(aggregationOperationList);
        List<AllBotDailyReceptionStatsVO> totalBotDailyStatsList = readMongoTemplate.aggregate(aggregation, MongoCollectionNameCenter.BOT_RECEPTION_STATS, AllBotDailyReceptionStatsVO.class)
                .getMappedResults();

        // 对其中的epochDay进行转换
        Map<Long, AllBotDailyReceptionStatsVO> statsMap = MyCollectionUtils.listToMap(totalBotDailyStatsList, AllBotDailyReceptionStatsVO::getEpochDay);
        List<AllBotDailyReceptionStatsVO> result = new ArrayList<>();


        for (long i = beginEpochDay; i <= endEpochDay; i++) {
            AllBotDailyReceptionStatsVO stats = statsMap.get(i);
            if (Objects.isNull(stats)) {
                stats = new AllBotDailyReceptionStatsVO();
                stats.setEpochDay(i);
                stats.setAnsweredCount(0);
                stats.setTotalCallCount(0);
            }

            stats.setDate(LocalDate.ofEpochDay(i));
            result.add(stats);
        }

        return result;
    }

    @Override
    public List<AllBotHourlyReceptionStatsVO> queryAllBotHourlyReceptionInfo(LocalDateTime beginDateTime, LocalDateTime endDateTime) {
        long beginEpochHour = BotStatsUtil.toEpochHour(beginDateTime);
        long endEpochHour = BotStatsUtil.toEpochHour(endDateTime);

        List<AggregationOperation> aggregationOperationList = new ArrayList<>();
        aggregationOperationList.add(BotStatsUtil.generateCommonEpochHourCondition(beginEpochHour, endEpochHour));

        AggregationOperation groupOperation = Aggregation.group( "epochHour")
                .first("epochHour").as("epochHour")
                .sum("count").as("answeredCount")
                .sum("totalCallCount").as("totalCallCount");

        aggregationOperationList.add(groupOperation);
        Aggregation aggregation = Aggregation.newAggregation(aggregationOperationList);
        List<AllBotHourlyReceptionStatsVO> totalBotDailyStatsList = readMongoTemplate.aggregate(aggregation, MongoCollectionNameCenter.BOT_RECEPTION_STATS, AllBotHourlyReceptionStatsVO.class)
                .getMappedResults();

        // 对其中的epochDay进行转换
        Map<Long, AllBotHourlyReceptionStatsVO> statsMap = MyCollectionUtils.listToMap(totalBotDailyStatsList, AllBotHourlyReceptionStatsVO::getEpochHour);
        List<AllBotHourlyReceptionStatsVO> result = new ArrayList<>();


        for (long i = beginEpochHour; i <= endEpochHour; i++) {
            AllBotHourlyReceptionStatsVO stats = statsMap.get(i);
            if (Objects.isNull(stats)) {
                stats = new AllBotHourlyReceptionStatsVO();
                stats.setEpochHour(i);
                stats.setAnsweredCount(0);
                stats.setTotalCallCount(0);
            }
            stats.setDateTime(BotStatsUtil.epochHour2LocalDateTime(i));
            result.add(stats);
        }

        return result;
    }
}
