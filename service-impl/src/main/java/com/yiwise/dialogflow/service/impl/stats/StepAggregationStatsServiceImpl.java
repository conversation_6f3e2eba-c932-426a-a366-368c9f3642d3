package com.yiwise.dialogflow.service.impl.stats;

import com.yiwise.base.common.utils.collection.MyCollectionUtils;
import com.yiwise.dialogflow.api.dto.request.StepAggregationStatsRequest;
import com.yiwise.dialogflow.api.dto.response.stats.DataReportNodeDataPO;
import com.yiwise.dialogflow.api.dto.response.stats.DataReportStepDataPO;
import com.yiwise.dialogflow.entity.enums.IntentPropertiesEnum;
import com.yiwise.dialogflow.entity.enums.NodeJumpTargetTypeEnum;
import com.yiwise.dialogflow.entity.enums.NodeTypeEnum;
import com.yiwise.dialogflow.entity.po.DialogBaseNodePO;
import com.yiwise.dialogflow.entity.po.StepPO;
import com.yiwise.dialogflow.entity.po.intent.IntentPO;
import com.yiwise.dialogflow.entity.po.stats.AnswerStatsPO;
import com.yiwise.dialogflow.entity.po.stats.StepNodeJumpStatsPO;
import com.yiwise.dialogflow.entity.po.stats.StepStatsPO;
import com.yiwise.dialogflow.entity.query.BaseStatsQuery;
import com.yiwise.dialogflow.entity.query.StepAggregationStatsQueryVO;
import com.yiwise.dialogflow.service.BotRefService;
import com.yiwise.dialogflow.service.StepNodeService;
import com.yiwise.dialogflow.service.StepService;
import com.yiwise.dialogflow.service.intent.IntentService;
import com.yiwise.dialogflow.service.stats.*;
import javaslang.Tuple;
import javaslang.Tuple2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Slf4j
@Service
public class StepAggregationStatsServiceImpl implements StepAggregationStatsService {

    @Resource
    private IntentService intentService;


    @Resource
    private StepService stepService;

    @Resource
    private StepStatsService stepStatsService;

    @Resource
    private StepNodeService stepNodeService;

    @Resource
    private BotReceptionStatsService botReceptionStatsService;

    @Resource
    private StepNodeStatsService stepNodeStatsService;

    @Resource
    private AnswerStatsService answerStatsService;

    @Resource
    private BotRefService botRefService;

    @Override
    public List<DataReportStepDataPO> generateDialogFlowDataReportData(StepAggregationStatsRequest condition) {
        if (Objects.isNull(condition) || MapUtils.isEmpty(condition.getTenantDialogFlowIdPairMap())) {
            return Collections.emptyList();
        }
        List<DataReportStepDataPO> result = Lists.newArrayList();

        int stepCount = condition.getStepCount() == null ? 3 : condition.getStepCount();

        initNodeData(result, stepCount);

        condition.getTenantDialogFlowIdPairMap().forEach((tenantId, dialogFlowId) -> {
            // 把dialogFlowId转成botId
            Long botId = botRefService.getBotId(dialogFlowId);
            if (Objects.nonNull(botId)) {
                wrapDialogFlowDataReportData(result, stepCount, tenantId, botId, condition);
            }
        });

        // 完成话术遍历后进行后处理
        result.forEach(stepDataPO -> {
            // 计算平均流程到达率
            Integer totalArriveCount = stepDataPO.getTotalArriveCount();
            Integer totalCount = stepDataPO.getTotalCount();
            if (Objects.isNull(totalArriveCount) || Objects.isNull(totalCount) || totalCount == 0) {
                stepDataPO.setAvgArriveRate(null);;
            } else {
                String avgArriveRate = String.format("%.2f", totalArriveCount * 100 / (double) totalCount);
                stepDataPO.setAvgArriveRate(avgArriveRate);
            }

            stepDataPO.getDataReportNodeDataPOList().forEach(nodeDataPO -> {
                // 计算平均节点挂断率
                Integer totalHangupCount = nodeDataPO.getTotalHangupCount();
                Integer nodeTotalCount = nodeDataPO.getTotalCount();
                if (Objects.isNull(totalHangupCount) || Objects.isNull(nodeTotalCount) || nodeTotalCount == 0) {
                    nodeDataPO.setAvgHangupRate(null);
                } else {
                    String avgHangupRate = String.format("%.2f", totalHangupCount * 100 / (double) nodeTotalCount);
                    nodeDataPO.setAvgHangupRate(avgHangupRate);
                }

                // 计算平均节点拒绝率
                Integer totalDeclineCount = nodeDataPO.getTotalDeclineCount();
                if (Objects.isNull(totalDeclineCount) || Objects.isNull(nodeTotalCount) || nodeTotalCount == 0) {
                    nodeDataPO.setAvgDeclineRate(null);
                } else {
                    String avgDeclineRate = String.format("%.2f", totalDeclineCount * 100 / (double) nodeTotalCount);
                    nodeDataPO.setAvgDeclineRate(avgDeclineRate);
                }
            });
        });

        return result;
    }

    private static void initNodeData(List<DataReportStepDataPO> result, Integer stepCount) {
        int nodeCount1 = 2;
        for (int i = 1; i <= stepCount; i++) {
            DataReportStepDataPO stepDataPO = new DataReportStepDataPO();
            stepDataPO.setSeq(i);
            List<DataReportNodeDataPO> nodeDataPOList = Lists.newArrayList();
            if (nodeCount1 == 0) {
                nodeCount1 = 1;
            }
            for (; nodeCount1 > 0; nodeCount1--) {
                DataReportNodeDataPO nodeDataPO = new DataReportNodeDataPO();
                nodeDataPO.setSeq(nodeCount1);
                nodeDataPOList.add(0, nodeDataPO);
            }
            stepDataPO.setDataReportNodeDataPOList(nodeDataPOList);
            result.add(stepDataPO);
        }
    }


    private void wrapDialogFlowDataReportData(List<DataReportStepDataPO> stepDataPOList,
                                              long stepCount,
                                              Long tenantId,
                                              Long botId,
                                              StepAggregationStatsRequest condition) {

        // 查询意图列表
        List<IntentPO> intentList = intentService.getAllByBotId(botId);

        // 拒绝意图id集合
        Set<String> declineIntentIdSet = intentList.stream()
                .filter(intent -> IntentPropertiesEnum.DECLINE.equals(intent.getIntentProperties()))
                .map(IntentPO::getId).collect(Collectors.toSet());

        List<StepPO> stepList = stepService.getMainListByBotId(botId);
        if (CollectionUtils.isEmpty(stepList)) {
            return;
        }

        BaseStatsQuery baseStatsQuery = new BaseStatsQuery();
        baseStatsQuery.setTenantId(tenantId);
        baseStatsQuery.setBotId(botId);
        baseStatsQuery.setBeginTime(LocalDateTime.of(condition.getBeginDate(), LocalTime.MIN));
        baseStatsQuery.setEndTime(LocalDateTime.of(condition.getEndDate(), LocalTime.MAX));
        List<StepStatsPO> stepStatsList = stepStatsService.queryAllStepStatsList(botId, stepList.stream().map(StepPO::getId).limit(stepCount).collect(Collectors.toList()), baseStatsQuery);

        Map<String, StepStatsPO> stepStatsMap = MyCollectionUtils.listToMap(stepStatsList, StepStatsPO::getStepId);
        int totalCallCount = botReceptionStatsService.queryBotReceptionCount(botId, baseStatsQuery);

        if (CollectionUtils.isEmpty(stepStatsList)) {
            return;
        }

        List<DialogBaseNodePO> botAllNodeList = stepNodeService.getAllListByBotId(botId);
        Map<String, List<DialogBaseNodePO>> stepNodeListMap = MyCollectionUtils.listToMapList(botAllNodeList, DialogBaseNodePO::getStepId);

        List<AnswerStatsPO> allStepAnswerStatsList = answerStatsService.queryAllStepAllAnswerTotalHangupStats(botId, baseStatsQuery);
        Map<Tuple2<String, String>, AtomicInteger> nodeHangupCountMap = new HashMap<>();
        for (AnswerStatsPO answerStats : allStepAnswerStatsList) {
            Tuple2<String, String> nodeKey = Tuple.of(answerStats.getStepId(), answerStats.getNodeId());
            AtomicInteger count = nodeHangupCountMap.computeIfAbsent(nodeKey, (x) -> new AtomicInteger(0));
            count.getAndAdd(answerStats.getTotalHangupCount());
        }


        // 需要统计的节点数，只有第一个流程为2，其余都为1
        int nodeCount = 2;

        for (int i = 0; i < stepCount && i < stepList.size(); i++) {
            StepPO step = stepList.get(i);
            StepStatsPO stepStats = stepStatsMap.get(step.getId());
            if (Objects.isNull(stepStats)) {
                continue;
            }

            DataReportStepDataPO stepDataPO = stepDataPOList.get(i);
            stepDataPO.setArriveRate(calculatePercent(stepStats.getCallCount(), totalCallCount));
            stepDataPO.addArriveCount(stepStats.getCallCount());
            stepDataPO.addTotalCount(totalCallCount);
            List<DataReportNodeDataPO> nodeDataPOList = stepDataPO.getDataReportNodeDataPOList();
            if (CollectionUtils.isEmpty(nodeDataPOList)) {
                nodeDataPOList = com.google.common.collect.Lists.newArrayList();
            }

            stepDataPO.setDataReportNodeDataPOList(nodeDataPOList);
            // 查询节点统计数据
            List<DialogBaseNodePO> nodeList = stepNodeListMap.get(step.getId());

            if (CollectionUtils.isEmpty(nodeList)) {
                continue;
            }

            List<DialogBaseNodePO> sordedNodeList = stepNodeService.breadthTraverse(nodeList);
            List<String> chatNodeIdList = sordedNodeList.stream()
                    .filter(node -> NodeTypeEnum.CHAT.equals(node.getType()))
                    .map(DialogBaseNodePO::getId)
                    .collect(Collectors.toList());


            List<StepNodeJumpStatsPO> allNodeStatsList = stepNodeStatsService.queryNodesAllJumpStats(botId, step.getId(), chatNodeIdList, baseStatsQuery);

            if (CollectionUtils.isEmpty(allNodeStatsList)) {
                if (nodeCount == 2) {
                    nodeCount = 0;
                }
                continue;
            }

            Map<String, List<StepNodeJumpStatsPO>> nodeJumpStatsListMap = MyCollectionUtils.listToMapList(allNodeStatsList, StepNodeJumpStatsPO::getNodeId);

            if (nodeCount == 0) {
                nodeCount = 1;
            }
            for (int j = 0; nodeCount > 0 && j < chatNodeIdList.size(); j++, nodeCount--) {

                String nodeId = chatNodeIdList.get(j);
                List<StepNodeJumpStatsPO> nodeStatsList = nodeJumpStatsListMap.get(nodeId);
                if (CollectionUtils.isEmpty(nodeStatsList)) {
                    nodeStatsList = Collections.emptyList();
                }

                DataReportNodeDataPO nodeDataPO = nodeDataPOList.get(j);

                // 计算节点挂断数, 需要从答案的数据统计中获取
                int hangupCount = nodeHangupCountMap.getOrDefault(Tuple.of(step.getId(), nodeId), new AtomicInteger(0)).get();
                nodeDataPO.addHangupCount(hangupCount);

                // 计算节点拒绝数
                int declineCount = nodeStatsList.stream()
                        .filter(item -> NodeJumpTargetTypeEnum.NODE.name().equals(item.getTargetType()))
                        .filter(item -> declineIntentIdSet.contains(item.getIntentId()))
                        .map(StepNodeJumpStatsPO::getCallCount)
                        .reduce(0, Integer::sum);
                nodeDataPO.addDeclineCount(declineCount);

                // 计算当前节点跳出总数(跳转到各个地方次数 + 挂断次数)
                int totalJumpCount = nodeStatsList.stream()
                        .map(StepNodeJumpStatsPO::getCallCount)
                        .reduce(0, Integer::sum);
                int totalCount = totalJumpCount + hangupCount;
                nodeDataPO.addTotalCount(totalCount);

                // 计算节点挂断率
                nodeDataPO.setHangupRate(calculatePercent(hangupCount, totalCount));

                // 计算节点拒绝率
                nodeDataPO.setDeclineRate(calculatePercent(declineCount, totalCount));
            }
        }
    }

    private String calculatePercent(Integer numerator, Integer denominator) {
        if (numerator == null) {
            numerator = 0;
        }
        if (denominator == null) {
            denominator = 1;
        }
        if (denominator == 0) {
            return "0.00";
        }
        return String.format("%.2f", numerator * 100 / (double) denominator);
    }


}
