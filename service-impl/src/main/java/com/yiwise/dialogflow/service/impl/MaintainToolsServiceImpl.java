package com.yiwise.dialogflow.service.impl;

import com.yiwise.dialogflow.entity.po.*;
import com.yiwise.dialogflow.service.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class MaintainToolsServiceImpl implements MaintainToolsService {

    @Resource
    private StepNodeService stepNodeService;

    @Resource
    private KnowledgeService knowledgeService;

    @Resource
    private SpecialAnswerConfigService specialAnswerConfigService;

    @Resource
    private StepService stepService;

    @Resource
    private LabelGenerateService labelGenerateService;

    @Resource
    private MongoTemplate mongoTemplate;

    @Override
    public void resetAnswerLabelIfDuplicate(Long botId) {
        Set<String> answerLabelSet = new HashSet<>();

        List<StepPO> stepList = stepService.getAllListByBotId(botId);
        List<DialogBaseNodePO> nodeList = stepNodeService.getListByStepIdList(botId, stepList.stream().map(StepPO::getId).collect(Collectors.toList()));

        for (DialogBaseNodePO node : nodeList) {
            boolean updated = false;
            if (CollectionUtils.isNotEmpty(node.getAnswerList())) {
                for (NodeAnswer nodeAnswer : node.getAnswerList()) {
                    if (answerLabelSet.contains(nodeAnswer.getLabel()) || StringUtils.isBlank(nodeAnswer.getLabel())) {
                        nodeAnswer.setLabel(null);
                        labelGenerateService.answerLabel(botId, Collections.singletonList(nodeAnswer));
                        answerLabelSet.add(nodeAnswer.getLabel());
                        updated = true;
                    } else {
                        answerLabelSet.add(nodeAnswer.getLabel());
                    }
                }
            }
            if (updated) {
                mongoTemplate.save(node, DialogBaseNodePO.COLLECTION_NAME);
            }
        }

        List<KnowledgePO> knowledgeList = knowledgeService.getAllListByBotId(botId);
        for (KnowledgePO knowledge : knowledgeList) {
            boolean update = false;
            if (CollectionUtils.isNotEmpty(knowledge.getAnswerList())) {
                for (KnowledgeAnswer answer : knowledge.getAnswerList()) {
                    if (answerLabelSet.contains(answer.getLabel()) || StringUtils.isBlank(answer.getLabel())) {
                        answer.setLabel(null);
                        labelGenerateService.answerLabel(botId, Collections.singletonList(answer));
                        answerLabelSet.add(answer.getLabel());
                        update = true;
                    } else {
                        answerLabelSet.add(answer.getLabel());
                    }
                }
            }
            if (update) {
                mongoTemplate.save(knowledge, KnowledgePO.COLLECTION_NAME);
            }
        }

        List<SpecialAnswerConfigPO> specialAnswerConfigList = specialAnswerConfigService.getByBotId(botId);
        for (SpecialAnswerConfigPO specialAnswerConfig : specialAnswerConfigList) {
            boolean update = false;
            if (CollectionUtils.isNotEmpty(specialAnswerConfig.getAnswerList())) {
                for (KnowledgeAnswer answer : specialAnswerConfig.getAnswerList()) {
                    if (answerLabelSet.contains(answer.getLabel()) || StringUtils.isBlank(answer.getLabel())) {
                        answer.setLabel(null);
                        labelGenerateService.answerLabel(botId, Collections.singletonList(answer));
                        answerLabelSet.add(answer.getLabel());
                        update = true;
                    } else {
                        answerLabelSet.add(answer.getLabel());
                    }
                }
            }
            if (update) {
                mongoTemplate.save(specialAnswerConfig, SpecialAnswerConfigPO.COLLECTION_NAME);
            }
        }
    }
}














