package com.yiwise.dialogflow.service.impl.botgenerate;

import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.common.OssKeyCenter;
import com.yiwise.dialogflow.entity.enums.BotGenerateConfigTypeEnum;
import com.yiwise.dialogflow.entity.po.botgenerate.BotGenerateConfigPO;
import com.yiwise.dialogflow.service.botgenerate.BotGenerateConfigService;
import com.yiwise.middleware.objectstorage.common.ObjectStorageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Service
public class BotGenerateConfigServiceImpl implements BotGenerateConfigService {

    @Resource
    private MongoTemplate mongoTemplate;

    @Resource
    private ObjectStorageHelper objectStorageHelper;

    @Override
    public Optional<BotGenerateConfigPO> getLastSchemaConfig() {
        return getLastConfigByType(BotGenerateConfigTypeEnum.SCHEMA);
    }

    @Override
    public Optional<BotGenerateConfigPO> getLastTemplateConfig() {
        return getLastConfigByType(BotGenerateConfigTypeEnum.SYNTAX_TEMPLATE);
    }

    private Optional<BotGenerateConfigPO> getLastConfigByType(BotGenerateConfigTypeEnum type) {
        Query query = new Query();
        query.addCriteria(Criteria.where("type").is(type));
        query.with(Sort.by(Sort.Order.desc("_id")));
        return Optional.ofNullable(mongoTemplate.findOne(query, BotGenerateConfigPO.class, BotGenerateConfigPO.COLLECTION_NAME));
    }

    @Override
    public String uploadSchema(MultipartFile configFile) {
        return doUpload(configFile, BotGenerateConfigTypeEnum.SCHEMA);
    }

    @Override
    public String uploadTemplate(MultipartFile configFile) {
        return doUpload(configFile, BotGenerateConfigTypeEnum.SYNTAX_TEMPLATE);
    }

    private String doUpload(MultipartFile configFile, BotGenerateConfigTypeEnum type) {
        if (Objects.isNull(configFile)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "配置文件不能为空");
        }
        // 获取文件后缀
        String fileName = configFile.getOriginalFilename();
        if (StringUtils.isBlank(fileName)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "配置文件名不能为空");
        }
        String fileSuffix = fileName.substring(fileName.lastIndexOf(".") + 1);
        String ossKey = OssKeyCenter.getBotGenerateConfigKey(fileSuffix);

        try {
            objectStorageHelper.upload(ossKey, configFile.getBytes());
        } catch (Exception e) {
            log.error("upload bot generate config file error", e);
            throw new ComException(ComErrorCode.UNKNOWN_ERROR, "上传bot生成配置文件失败", e);
        }

        BotGenerateConfigPO conf = new BotGenerateConfigPO();
        conf.setOssKey(ossKey);
        conf.setCreateTime(LocalDateTime.now());
        conf.setUpdateTime(LocalDateTime.now());
        conf.setType(type);
        mongoTemplate.insert(conf, BotGenerateConfigPO.COLLECTION_NAME);
        return ossKey;
    }

}
