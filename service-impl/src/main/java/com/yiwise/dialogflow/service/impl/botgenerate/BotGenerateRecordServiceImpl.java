package com.yiwise.dialogflow.service.impl.botgenerate;

import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.base.model.websocket.BasicMsg;
import com.yiwise.base.model.websocket.UserIdPrincipal;
import com.yiwise.dialogflow.common.ApplicationConstant;
import com.yiwise.dialogflow.entity.dto.BotGenerateValidateResultDTO;
import com.yiwise.dialogflow.entity.enums.BotGenerateRecordStatusEnum;
import com.yiwise.dialogflow.entity.enums.BotGenerateTemplateStatusEnum;
import com.yiwise.dialogflow.entity.po.botgenerate.BotGenerateRecordPO;
import com.yiwise.dialogflow.entity.po.botgenerate.BotGenerateTemplatePO;
import com.yiwise.dialogflow.entity.po.remote.UserPO;
import com.yiwise.dialogflow.entity.vo.botgenerate.BotGenerateFailCallbackVO;
import com.yiwise.dialogflow.entity.vo.botgenerate.BotGenerateResultVO;
import com.yiwise.dialogflow.helper.AliMessageQueueHelper;
import com.yiwise.dialogflow.service.botgenerate.BotGenerateRecordService;
import com.yiwise.dialogflow.service.botgenerate.BotGenerateTemplateService;
import com.yiwise.dialogflow.service.remote.UserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;

@Slf4j
@Service
public class BotGenerateRecordServiceImpl implements BotGenerateRecordService {

    @Lazy
    @Resource
    private BotGenerateTemplateService botGenerateTemplateService;

    @Resource
    private MongoTemplate mongoTemplate;

    @Resource
    private RestTemplate longestLatencyRestTemplate;

    @Resource
    private UserService userService;

    @Override
    public BotGenerateResultVO generate(BotGenerateTemplatePO template, Boolean ignoreWarning, Long userId) {
        int concurrency = calculateCurrentGlobalConcurrency();
        int maxConcurrency = ApplicationConstant.BOT_GENERATE_CONCURRENCY;
        log.info("当前并发数:{}, 最大并发数:{}", concurrency, maxConcurrency);
        if (concurrency >= maxConcurrency) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, String.format("当前并发数:%s已达上限:%s, 请稍后再试", concurrency, maxConcurrency));
        }

        BotGenerateValidateResultDTO validateResultDTO = invokeValidate(template.getPayload());
        if (CollectionUtils.isNotEmpty(validateResultDTO.getErrors())
                || (CollectionUtils.isNotEmpty(validateResultDTO.getWarnings()) && BooleanUtils.isNotTrue(ignoreWarning))) {
            return BotGenerateResultVO.builder()
                    .template(template)
                    .validateResult(validateResultDTO)
                    .success(false)
                    .build();
        }

        BotGenerateRecordPO record = new BotGenerateRecordPO();
        record.setId(new ObjectId().toHexString());
        record.setTemplateId(template.getId());
        record.setTitle(template.getName());
        record.setCreateUserId(userId);
        record.setUpdateUserId(userId);
        record.setCreateTime(LocalDateTime.now());
        record.setUpdateTime(LocalDateTime.now());
        record.setStatus(BotGenerateRecordStatusEnum.RUNNING);

        mongoTemplate.insert(record, BotGenerateRecordPO.COLLECTION_NAME);

        String author = userService.getUserByIdIfPresent(userId).map(UserPO::getName).orElseGet(() -> String.format("userId:%s", userId));

        invokeGenerateAndUpdateRecord(template, author, record);

        return BotGenerateResultVO.builder()
                .record(record)
                .template(template)
                .success(BotGenerateRecordStatusEnum.RUNNING.equals(record.getStatus()))
                .validateResult(validateResultDTO)
                .build();
    }

    private BotGenerateValidateResultDTO invokeValidate(Map<String, Object> payloadData) {
        if (MapUtils.isEmpty(payloadData)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "校验接口参数为空");
        }

        String validateUrl = ApplicationConstant.BOT_GENERATE_VALIDATE_URL;

        Map<String, Object> param = new HashMap<>();
        param.put("data", payloadData);
        param.put("namespace", ApplicationConstant.BOT_GENERATE_NAMESPACE);

        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/json; charset=utf-8");


        String jsonParam = JsonUtils.object2String(param);
        HttpEntity<String> httpEntity = new HttpEntity<>(jsonParam, headers);
        log.info("调用校验接口, url:{}, param:{}", validateUrl, jsonParam);
        ResponseEntity<String> exchange = longestLatencyRestTemplate.exchange(validateUrl, HttpMethod.POST, httpEntity, String.class);

        String body = exchange.getBody();
        log.info("调用校验接口返回, url:{}, body:{}", validateUrl, body);
        if (StringUtils.isBlank(body)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "校验接口返回为空");
        }

        BotGenerateValidateResultDTO result = JsonUtils.string2Object(body, BotGenerateValidateResultDTO.class);
        if (result == null) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "校验接口返回格式错误");
        }
        return result;
    }


    private void invokeGenerateAndUpdateRecord(BotGenerateTemplatePO template, String author, BotGenerateRecordPO record) {
        String validateUrl = ApplicationConstant.BOT_GENERATE_CREATE_URL;
        Map<String, Object> param = new HashMap<>();
        param.put("author", author);
        param.put("namespace", ApplicationConstant.BOT_GENERATE_NAMESPACE);
        param.put("data", template.getPayload());
        param.put("title", template.getName());
        param.put("job_id", record.getId());

        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/json; charset=utf-8");

        String jsonParam = JsonUtils.object2String(param);
        record.setParams(jsonParam);
        HttpEntity<String> httpEntity = new HttpEntity<>(jsonParam, headers);
        Exception excpetion = null;
        try {
            log.info("调用生成接口, url:{}, param:{}", validateUrl, jsonParam);
            ResponseEntity<String> exchange = longestLatencyRestTemplate.exchange(validateUrl, HttpMethod.POST, httpEntity, String.class);
            if (exchange.getStatusCode() != HttpStatus.OK) {
                log.info("调用生成接口失败, url:{}, code:{}", validateUrl, exchange.getStatusCode());
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "调用生成接口失败:code=" + exchange.getStatusCode());
            }
            String body = exchange.getBody();
            log.info("调用生成接口, url:{}, result:{}", validateUrl, body);
            record.setOriginResult(body);
        } catch (Exception e) {
            log.warn("调用生成接口失败, url:{}, param:{}", validateUrl, jsonParam, e);
            excpetion = e;
        } finally {
            if (excpetion != null) {
                record.setStatus(BotGenerateRecordStatusEnum.FAIL);
                record.setFailReason(excpetion.getMessage());
            }
            mongoTemplate.save(record, BotGenerateRecordPO.COLLECTION_NAME);
        }
    }

    @Override
    public void complete(String recordId, Long targetBotId, String completePercent) {
        getById(recordId)
                .ifPresent(record -> {
                    record.setCompletedPercent(completePercent);
                    record.setCompleteTime(LocalDateTime.now());
                    record.setGenerateBotId(targetBotId);
                    record.setUpdateTime(LocalDateTime.now());
                    record.setStatus(BotGenerateRecordStatusEnum.SUCCESS);
                    mongoTemplate.save(record, BotGenerateRecordPO.COLLECTION_NAME);

                    updateTemplateAndSendMsgIfRecordIsLatest(record);
                });
    }

    private void sendMsg(BotGenerateRecordPO record) {
        BasicMsg<BotGenerateRecordPO> msg = new BasicMsg<>();
        msg.setInfo(record);
        msg.setMsg("bot生成完成");
        log.info("推送bot生成完成消息, msg={}", JsonUtils.object2String(record));
        AliMessageQueueHelper.sendWebSocketMessage(new UserIdPrincipal(record.getCreateUserId(), String.valueOf(record.getCreateUserId())), ApplicationConstant.WEBSOCKET_BOT_GENERATE_SUBSCRIPT_URL, msg, "BOT_GENERATE");
    }

    @Override
    public Optional<BotGenerateRecordPO> getById(String recordId) {
        if (StringUtils.isBlank(recordId)) {
            return Optional.empty();
        }
        return Optional.ofNullable(mongoTemplate.findById(recordId, BotGenerateRecordPO.class, BotGenerateRecordPO.COLLECTION_NAME));
    }

    /**
     * 查询正在运行中的记录, 用于并发控制
     * todo qps和并发应该都很低, 直接查数据库判断, 暂时不考虑并发竞争等问题
     * @return 正在处理中的记录
     */
    @Override
    public List<BotGenerateRecordPO> queryRunningRecordList() {
        Query query = new Query();
        query.addCriteria(Criteria.where("status").is(BotGenerateRecordStatusEnum.RUNNING));
        return mongoTemplate.find(query, BotGenerateRecordPO.class, BotGenerateRecordPO.COLLECTION_NAME);
    }

    private int calculateCurrentGlobalConcurrency() {
        List<BotGenerateRecordPO> runningRecordList = queryRunningRecordList();
        return runningRecordList.size();
    }

    /**
     * 查询最新的bot生成记录
     */
    private BotGenerateRecordPO queryLatestRecord(String templateId) {
        Query query = Query.query(Criteria.where("templateId").is(templateId));
        query.limit(1);
        query.with(Sort.by(Sort.Direction.DESC, "createTime"));
        return mongoTemplate.findOne(query, BotGenerateRecordPO.class, BotGenerateRecordPO.COLLECTION_NAME);
    }

    private boolean isLatestRecord(String templateId, String recordId) {
        return Optional.ofNullable(queryLatestRecord(templateId)).map(BotGenerateRecordPO::getId).map(id -> Objects.equals(id, recordId)).orElse(false);
    }

    @Override
    public void updateTemplateAndSendMsgIfRecordIsLatest(BotGenerateRecordPO record) {
        if (!isLatestRecord(record.getTemplateId(), record.getId())) {
            log.warn("recordId={}, template={}不是最新的生成记录,无法更新模板数据", record.getTemplateId(), record.getTitle());
            return;
        }
        BotGenerateTemplateStatusEnum status = BotGenerateRecordStatusEnum.SUCCESS.equals(record.getStatus()) ? BotGenerateTemplateStatusEnum.SUCCESS : BotGenerateTemplateStatusEnum.FAIL;
        botGenerateTemplateService.updateLastCompletePercentAndStatus(record.getTemplateId(), record.getCompletedPercent(), status);
        sendMsg(record);
    }

    @Override
    public void failCallback(BotGenerateFailCallbackVO callback) {
        log.warn("[LogHub_Warn]话术自动生成算法回调,jobId={}, failReason={}", callback.getJobId(), callback.getFailReason());
        getById(callback.getJobId())
                .ifPresent(record -> {
                    record.setUpdateTime(LocalDateTime.now());
                    record.setStatus(BotGenerateRecordStatusEnum.FAIL);
                    record.setFailReason(callback.getFailReason());
                    mongoTemplate.save(record, BotGenerateRecordPO.COLLECTION_NAME);

                    updateTemplateAndSendMsgIfRecordIsLatest(record);
                });
    }
}
