package com.yiwise.dialogflow.service.impl.intent;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.yiwise.dialogflow.entity.bo.algorithm.ResourceId2NameBO;
import com.yiwise.dialogflow.entity.enums.OperationLogResourceTypeEnum;
import com.yiwise.dialogflow.entity.enums.OperationLogTypeEnum;
import com.yiwise.dialogflow.entity.po.intent.IntentRuleConditionPO;
import com.yiwise.dialogflow.entity.po.intent.IntentRulePO;
import com.yiwise.dialogflow.service.BotService;
import com.yiwise.dialogflow.service.OperationLogService;
import com.yiwise.dialogflow.service.intent.IntentRuleOperationLogService;
import com.yiwise.dialogflow.service.remote.IntentLevelTagDetailService;
import com.yiwise.dialogflow.utils.IntentRuleContentRenderUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> @ yiwise . com>
 * @version v0.1 2022/9/21
 * @class <code>IntentRuleOperationLogServiceImpl</code>
 * @see
 * @since JDK1.8
 */
@Slf4j
@Service
public class IntentRuleOperationLogServiceImpl implements IntentRuleOperationLogService {

    private static final Cache<Long, Map<Integer, String>> INTENT_LEVEL_TAG_CACHE = CacheBuilder.newBuilder()
            .maximumSize(1000)
            .expireAfterWrite(10, TimeUnit.MINUTES)
            .build();

    @Resource
    private OperationLogService operationLogService;

    @Resource
    private IntentLevelTagDetailService intentLevelTagDetailService;

    @Resource
    private BotService botService;


    @Override
    public void addOperationLog(IntentRulePO oldIntentRule, IntentRulePO newIntentRule,
                                ResourceId2NameBO resourceMap, Long botId, Long userId) {
        Long intentLevelTagId = botService.selectByKey(botId).getIntentLevelTagId();
        Map<Integer, String> intentLevelTagId2NameMap = INTENT_LEVEL_TAG_CACHE.getIfPresent(intentLevelTagId);
        //删除操作和未更新意向等级操作 使用缓存
        if (Objects.isNull(intentLevelTagId2NameMap)
                || hasChangeIntentLevel(oldIntentRule, newIntentRule)) {
            intentLevelTagId2NameMap = intentLevelTagDetailService.getIntentLevelTagDetailCode2NameMap(intentLevelTagId);
            INTENT_LEVEL_TAG_CACHE.put(intentLevelTagId, intentLevelTagId2NameMap);
        }

        //新增操作，删除操作，编辑操作
        if (Objects.isNull(oldIntentRule)) {
            String ruleContent = buildConditionContent(newIntentRule.getConditionList(), resourceMap);
            String detail = String.format("添加意向规则:%s", buildElement(newIntentRule, ruleContent, intentLevelTagId2NameMap));
            operationLogService.save(newIntentRule.getBotId(), OperationLogTypeEnum.RULE_CONFIG,
                    OperationLogResourceTypeEnum.INTENT_RULE_CONFIG, detail, userId);
        } else if (Objects.isNull(newIntentRule)) {
            String ruleContent = buildConditionContent(oldIntentRule.getConditionList(), resourceMap);
            String detail = String.format("删除意向规则:%s", buildElement(oldIntentRule, ruleContent, intentLevelTagId2NameMap));
            operationLogService.save(oldIntentRule.getBotId(), OperationLogTypeEnum.RULE_CONFIG,
                    OperationLogResourceTypeEnum.INTENT_RULE_CONFIG, detail, userId);
        } else {
            String oldContent = buildConditionContent(oldIntentRule.getConditionList(), resourceMap);
            String newContent = buildConditionContent(newIntentRule.getConditionList(), resourceMap);
            Long oldSort = oldIntentRule.getMatchOrder();
            Long newSort = newIntentRule.getMatchOrder();
            Integer oldIntentLevel = oldIntentRule.getIntentLevelTagDetailCode();
            Integer newIntentLevel = newIntentRule.getIntentLevelTagDetailCode();
            if (!Objects.equals(oldSort, newSort) || !Objects.equals(oldIntentLevel, newIntentLevel) || !oldContent.equals(newContent)) {
                String detail = String.format("编辑意向规则:%s，修改为%s", buildElement(oldIntentRule, oldContent, intentLevelTagId2NameMap),
                        buildElement(newIntentRule, newContent, intentLevelTagId2NameMap));
                operationLogService.save(oldIntentRule.getBotId(), OperationLogTypeEnum.RULE_CONFIG,
                        OperationLogResourceTypeEnum.INTENT_RULE_CONFIG, detail, userId);
            }
        }
    }

    @Override
    public void moveMatchOrderLog(Map<Integer, IntentRulePO> ruleOrderMap, int[] moveInfo, ResourceId2NameBO resourceMap, Long botId, Long userId) {
        IntentRulePO rule = ruleOrderMap.get(moveInfo[0]);
        if (Objects.isNull(rule)) {
            return;
        }
        // 上下交换
        boolean isSwap = moveInfo[1] == 1 || moveInfo[1] == -1;
        int swapOrder = moveInfo[0] + moveInfo[1];
        if (isSwap && ruleOrderMap.get(swapOrder) != null) {
            IntentRulePO swapRule = ruleOrderMap.get(swapOrder);
            String ruleContent = buildConditionContent(rule.getConditionList(), resourceMap);
            String swapRuleContent = buildConditionContent(swapRule.getConditionList(), resourceMap);
            String detail = String.format("将意向规则:[%s]与意向规则:[%s]交换位置", buildElement(rule, ruleContent, resourceMap.getIntentLevelIdNameMap()),
                    buildElement(swapRule, swapRuleContent, resourceMap.getIntentLevelIdNameMap()));
            operationLogService.save(rule.getBotId(), OperationLogTypeEnum.RULE_CONFIG,
                    OperationLogResourceTypeEnum.INTENT_RULE_CONFIG, detail, userId);
        } else {
            String ruleContent = buildConditionContent(rule.getConditionList(), resourceMap);
            String detail = String.format("将意向规则:[%s]向%s移动%s,序号由%s更新为%s", ruleContent, moveInfo[1] < 0 ? "上" : "下", Math.abs(moveInfo[1]), moveInfo[0], rule.getMatchOrder());
            operationLogService.save(rule.getBotId(), OperationLogTypeEnum.RULE_CONFIG,
                    OperationLogResourceTypeEnum.INTENT_RULE_CONFIG, detail, userId);
        }
    }

    private boolean hasChangeIntentLevel(IntentRulePO oldIntentRule, IntentRulePO newIntentRule) {
        if (Objects.isNull(oldIntentRule)) {
            return true;
        }
        return Objects.nonNull(newIntentRule)
                && !Objects.equals(oldIntentRule.getIntentLevelTagDetailCode(), newIntentRule.getIntentLevelTagDetailCode());
    }

    private String buildElement(IntentRulePO intentRule, String content, Map<Integer, String> intentLevelTagId2NameMap) {
        return String.format("【%s:%s:%s】", intentRule.getMatchOrder(),
                intentLevelTagId2NameMap.getOrDefault(intentRule.getIntentLevelTagDetailCode(), null), content);
    }

    private String buildConditionContent(List<IntentRuleConditionPO> conditionList, ResourceId2NameBO resourceMap) {
        return IntentRuleContentRenderUtils.renderIntentRuleConditionContent(conditionList,
                resourceMap.getStepNode2NameMap(),
                resourceMap.getSpecialAnswerId2NameMap(),
                resourceMap.getKnowledgeMap(),
                resourceMap.getIntentLevelIdNameMap(),
                resourceMap.getEntityId2NameMap(),
                resourceMap.getIntentId2NameMap(),
                resourceMap.getVarId2NameMap(),
                resourceMap.getLlmLabeId2NameMap());
    }
}
