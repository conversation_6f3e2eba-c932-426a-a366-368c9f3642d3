package com.yiwise.dialogflow.service.impl;

import com.yiwise.dialogflow.entity.enums.DialogFlowConditionTypeEnum;
import com.yiwise.dialogflow.entity.po.DialogFlowExtraRuleConditionNodePO;
import com.yiwise.dialogflow.entity.po.intent.IntentRuleActionPO;
import com.yiwise.dialogflow.entity.po.intent.IntentRuleConditionPO;
import com.yiwise.dialogflow.entity.po.intent.IntentRulePO;
import com.yiwise.dialogflow.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> @ yiwise . com>
 * @version v0.1 2022/7/26
 * @class <code>DataSyncServiceImpl</code>
 * @see
 * @since JDK1.8
 */
@Slf4j
@Service
public class DataSyncServiceImpl implements DataSyncService {

    @Resource
    private SourceRefService sourceRefService;

    @Resource
    private StepService stepService;

    @Resource
    private StepNodeService stepNodeService;

    @Resource
    private KnowledgeService knowledgeService;

    @Resource
    private SpecialAnswerConfigService specialAnswerConfigService;

    @Resource
    private MongoTemplate mongoTemplate;

    @Override
    public boolean syncSourceRef() {
        return true;
    }

    @Override
    public void fixBotDependenceStep() {
        List<IntentRulePO> intentRuleList = mongoTemplate.findAll(IntentRulePO.class, IntentRulePO.COLLECTION_NAME);
        List<IntentRuleActionPO> intentRuleActionList = mongoTemplate.findAll(IntentRuleActionPO.class, IntentRuleActionPO.COLLECTION_NAME);
        for (IntentRulePO intentRulePO : intentRuleList) {
            if (CollectionUtils.isNotEmpty(intentRulePO.getConditionList())) {
                boolean isUpdate = false;
                //是否存在
                for (IntentRuleConditionPO intentRuleConditionPO : intentRulePO.getConditionList()) {
                    if (intentRuleConditionPO.getType().equals(DialogFlowConditionTypeEnum.TRIGGER_DEPENDENCE_DIALOGFLOW)
                            && CollectionUtils.isNotEmpty(intentRuleConditionPO.getNodeList())) {
                        intentRuleConditionPO.setType(DialogFlowConditionTypeEnum.TRIGGER_PROCESS_NODE);
                        isUpdate = true;
                    }
                }
                if (isUpdate) {
                    mongoTemplate.save(intentRulePO, IntentRulePO.COLLECTION_NAME);
                }
//                for (IntentRuleConditionPO intentRuleConditionPO : intentRulePO.getConditionList()) {
//                    if (intentRuleConditionPO.getType().equals(DialogFlowConditionTypeEnum.TRIGGER_PROCESS_NODE)
//                            && CollectionUtils.isNotEmpty(nodeList)) {
//                        if (CollectionUtils.isNotEmpty(intentRuleConditionPO.getNodeList())) {
//                            intentRuleConditionPO.getNodeList().addAll(nodeList);
//                        } else {
//                            intentRuleConditionPO.setNodeList(nodeList);
//                        }
//                    }
//                }
            }
        }
        for (IntentRuleActionPO intentRulePO : intentRuleActionList) {
            if (CollectionUtils.isNotEmpty(intentRulePO.getConditionList())) {
                boolean isUpdate = false;
                //是否存在
                for (IntentRuleConditionPO intentRuleConditionPO : intentRulePO.getConditionList()) {
                    if (intentRuleConditionPO.getType().equals(DialogFlowConditionTypeEnum.TRIGGER_DEPENDENCE_DIALOGFLOW)
                            && CollectionUtils.isNotEmpty(intentRuleConditionPO.getNodeList())) {
                        intentRuleConditionPO.setType(DialogFlowConditionTypeEnum.TRIGGER_PROCESS_NODE);
                        isUpdate = true;
                    }
                }
                if (isUpdate) {
                    mongoTemplate.save(intentRulePO, IntentRulePO.COLLECTION_NAME);
                }
            }
        }
    }
}
