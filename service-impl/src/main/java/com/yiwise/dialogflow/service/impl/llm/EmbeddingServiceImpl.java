package com.yiwise.dialogflow.service.impl.llm;

import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.base.common.utils.encrypt.Md5Utils;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.common.ApplicationConstant;
import com.yiwise.dialogflow.entity.po.*;
import com.yiwise.dialogflow.entity.po.llm.RagTextUploadRecordPO;
import com.yiwise.dialogflow.entity.vo.BaseCallbackResultVO;
import com.yiwise.dialogflow.service.llm.EmbeddingService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.slf4j.MDC;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class EmbeddingServiceImpl implements EmbeddingService {
    private static final long TIMEOUT_SECONDS = 60;

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private MongoTemplate mongoTemplate;


    @Override
    public String uploadAll(Long botId, Long userId,
                            List<DialogBaseNodePO> allNodeList,
                            List<KnowledgePO> allKnowledgeList,
                            List<SpecialAnswerConfigPO> allSpecialAnswerList) {

        Map<String, String> nodeTextMap = getAllNodeTextList(allNodeList);
        Map<String, String> knowledgeTextMap = getAllKnowledgeTextList(allKnowledgeList, allSpecialAnswerList);

        String hashValue = calculateHashValue(nodeTextMap, knowledgeTextMap);

        RagTextUploadRecordPO record = new RagTextUploadRecordPO();
        record.setBotId(botId);
        record.setUpdateUserId(userId);
        record.setCreateUserId(userId);
        record.setCreateTime(LocalDateTime.now());
        record.setUpdateTime(LocalDateTime.now());
        record.setTextHashValue(hashValue);
        record.setRequestId(MDC.get("MDC_LOG_ID"));
        record.setId(new ObjectId().toHexString());

        doUploadRequest(botId, record.getId(), nodeTextMap, knowledgeTextMap);

        mongoTemplate.insert(record, RagTextUploadRecordPO.COLLECTION_NAME);
        return record.getId();
    }

    @Override
    public boolean checkNeedUpload(RagTextUploadRecordPO preRecord,
                                   List<DialogBaseNodePO> allNodeList,
                                   List<KnowledgePO> allKnowledgeList,
                                   List<SpecialAnswerConfigPO> allSpecialAnswerList) {
        if (Objects.isNull(preRecord)) {
            return true;
        }

        if (BooleanUtils.isFalse(preRecord.getSuccess())) {
            return true;
        }

        if (Objects.isNull(preRecord.getSuccess()) && checkIsTimeout(preRecord)) {
            return true;
        }
        String hashValue = calculateHashValue(getAllNodeTextList(allNodeList), getAllKnowledgeTextList(allKnowledgeList, allSpecialAnswerList));
        return !StringUtils.equals(hashValue, preRecord.getTextHashValue());
    }

    @Override
    public boolean checkIsSuccess(Long botId, String id) {
        Optional<RagTextUploadRecordPO> recordOpt = getById(id);
        if (!recordOpt.isPresent()) {
            throw new ComException(ComErrorCode.NOT_EXIST, "上传记录不存在");
        }
        return BooleanUtils.isTrue(recordOpt.get().getSuccess());
    }

    @Override
    public boolean checkIsTimeout(RagTextUploadRecordPO record) {
        LocalDateTime now = LocalDateTime.now();
        return record.getCreateTime().plusSeconds(TIMEOUT_SECONDS).isBefore(now);
    }

    @Override
    public Optional<RagTextUploadRecordPO> getLastByBotId(Long botId) {
        Query query = new Query();
        query.addCriteria(Criteria.where("botId").is(botId))
                .with(Sort.by(Sort.Direction.DESC, "_id"))
                .limit(1);
        RagTextUploadRecordPO record = mongoTemplate.findOne(query, RagTextUploadRecordPO.class, RagTextUploadRecordPO.COLLECTION_NAME);
        return Optional.ofNullable(record);
    }

    @Override
    public void updateResult(String id, BaseCallbackResultVO result) {
        Optional<RagTextUploadRecordPO> recordOpt = getById(id);
        if (!recordOpt.isPresent()) {
            log.warn("[LogHub_Warn] 上传记录不存在, id={}", id);
            throw new ComException(ComErrorCode.NOT_EXIST, "上传记录不存在");
        }

        boolean success = true;
        String reason = "success";
        if (CollectionUtils.isNotEmpty(result.getResponses())) {
            for (BaseCallbackResultVO.Item item : result.getResponses()) {
                if (Objects.nonNull(item.getCode()) && item.getCode() != 0) {
                    success = false;
                    reason = item.getInfo();
                    break;
                }
            }
        }

        RagTextUploadRecordPO record = recordOpt.get();
        record.setUpdateTime(LocalDateTime.now());
        record.setSuccess(success);
        record.setReason(reason);

        mongoTemplate.save(record, RagTextUploadRecordPO.COLLECTION_NAME);
    }

    @Override
    public void waitingResult(Long botId, String id) {
        long start = System.currentTimeMillis();
        long timeoutMs = start + TIMEOUT_SECONDS * 1000;
        while (true) {
            Optional<RagTextUploadRecordPO> recordOpt = getById(id);
            if (!recordOpt.isPresent()) {
                throw new ComException(ComErrorCode.NOT_EXIST, "rag上传记录不存在");
            }

            RagTextUploadRecordPO record = recordOpt.get();

            if (Objects.nonNull(record.getSuccess())) {
                if (BooleanUtils.isTrue(record.getSuccess())) {
                    return;
                } else {
                    throw new ComException(ComErrorCode.UNKNOWN_ERROR, String.format("rag数据上传失败, 记录 id:%s, 原因: %s", record.getId(), record.getReason()));
                }
            }
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime timeoutTime = record.getCreateTime().plusSeconds(TIMEOUT_SECONDS);
            if (now.isAfter(timeoutTime)) {
                throw new ComException(ComErrorCode.UNKNOWN_ERROR, "等待 rag 上传结果超");
            }

            try {
                Thread.sleep(200);
            } catch (InterruptedException e) {
                log.error("等待结果时被打断", e);
            }

            long nowTime = System.currentTimeMillis();
            if (nowTime > timeoutMs) {
                throw new ComException(ComErrorCode.UNKNOWN_ERROR, "等待 rag 上传结果超时");
            }
        }
    }

    private Optional<RagTextUploadRecordPO> getById(String id) {
        return Optional.ofNullable(mongoTemplate.findById(id, RagTextUploadRecordPO.class));
    }

    private Optional<RagTextUploadRecordPO> getLastSuccessRecord(Long botId) {
        Query query = new Query();
        query.addCriteria(Criteria.where("botId").is(botId))
                .addCriteria(Criteria.where("success").is(true))
                .with(Sort.by(Sort.Direction.DESC, "_id"))
                .limit(1);
        return Optional.ofNullable(mongoTemplate.findOne(query, RagTextUploadRecordPO.class, RagTextUploadRecordPO.COLLECTION_NAME));
    }



    // key: nodeId, value: 答案文本列表
    private Map<String, String> getAllNodeTextList(List<DialogBaseNodePO> nodeList) {
        Map<String, String> result = new HashMap<>();
        for (DialogBaseNodePO node : nodeList) {
            if (CollectionUtils.isNotEmpty(node.getAnswerList())) {
                Optional<String> text = node.getAnswerList().stream()
                        .map(NodeAnswer::getText)
                        .filter(StringUtils::isNotBlank)
                        .findFirst();
                text.ifPresent(x -> result.put(node.getId(), x));
            }
        }
        return result;
    }

    // key: 知识标题, value: 答案列表
    private Map<String, String> getAllKnowledgeTextList(List<KnowledgePO> knowledgeList,
                                                        List<SpecialAnswerConfigPO> allSpecialAnswerList) {
        Map<String, String> result = new HashMap<>();
        for (KnowledgePO knowledge : knowledgeList) {
            if (CollectionUtils.isNotEmpty(knowledge.getAnswerList())) {
                Optional<String> text = knowledge.getAnswerList().stream()
                .map(KnowledgeAnswer::getText)
                .filter(StringUtils::isNotBlank)
                .findFirst();
                text.ifPresent(x -> result.put(knowledge.getName(), x));
            }
        }
        for (SpecialAnswerConfigPO specialAnswerConfig : allSpecialAnswerList) {
            if (CollectionUtils.isNotEmpty(specialAnswerConfig.getAnswerList())) {
                Optional<String> text = specialAnswerConfig.getAnswerList().stream()
                        .map(KnowledgeAnswer::getText)
                        .filter(StringUtils::isNotBlank)
                        .findFirst();
                text.ifPresent(x -> result.put(specialAnswerConfig.getName(), x));
            }
        }
        return result;
    }


    // 请求接口
    private void doUploadRequest(Long botId,
                                 String recordId,
                                 Map<String, String> nodeMap,
                                 Map<String, String> knowledgeMap) {

        Map<String, Object> param =  new HashMap<>();

        String url = ApplicationConstant.ALGORITHM_RAG_UPLOAD_URL;

        String callbackUrl = String.format("%s/apiBot/v3/algorithm/ragUploadCallback/%s/%s",
                ApplicationConstant.ALGORITHM_RAG_UPLOAD_CALLBACK_DOMAIN, botId, recordId);

        List<Map<String, Object>> contentList = new ArrayList<>();

        Map<String, Object> nodeContent = new HashMap<>();

        nodeContent.put("fileType", "node");
        nodeContent.put("fileId", new ObjectId().toHexString());
        nodeContent.put("nodes", nodeMap.values());

        contentList.add(nodeContent);

        knowledgeMap.forEach((k, v) -> {
            Map<String, Object> knowledgeContent = new HashMap<>();
            knowledgeContent.put("fileType", "qa");
            knowledgeContent.put("fileId", new ObjectId().toHexString());
            knowledgeContent.put("questions", Collections.singletonList(k));
            knowledgeContent.put("answers", Collections.singletonList(v));
            contentList.add(knowledgeContent);
        });

        param.put("dialogFlowId", String.valueOf(botId));
        param.put("callbackUrl", callbackUrl);
        param.put("contents", contentList);
        param.put("envName", ApplicationConstant.ALGORITHM_RAG_UPLOAD_ENV);

        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.add("Content-Type", "application/json");
        String body = JsonUtils.object2String(param);
        HttpEntity<String> requestEntity = new HttpEntity<>(body, httpHeaders);

        log.info("请求upload, url={},body={}", url, body);
        ResponseEntity<String> response = restTemplate.postForEntity(url, requestEntity, String.class);
        log.info("请求upload结果为: response={}", response);
        if (!HttpStatus.OK.equals(response.getStatusCode())) {
            throw new ComException(ComErrorCode.UNKNOWN_ERROR, String.format("请求 RAG 上传接口失败, statusCode=%s", response.getStatusCode()));
        }

        Map<String, Object> responseMap = JsonUtils.string2MapObject(response.getBody(), String.class, Object.class);
        if (!"0".equals(responseMap.getOrDefault("code", -1).toString())) {
            throw new ComException(ComErrorCode.UNKNOWN_ERROR, String.format("请求 RAG 上传接口失败, response=%s", response.getBody()));
        }
    }

    private String calculateHashValue(Map<String, String> nodeMap,
                                      Map<String, String> knowledgeMap) {
        StringBuilder sb = new StringBuilder();

        List<String> nodeIdList = nodeMap.keySet().stream()
                .sorted()
                .collect(Collectors.toList());

        for (String nodeId : nodeIdList) {
            sb.append(nodeId);
            sb.append(nodeMap.get(nodeId));
        }

        List<String> knowledgeTitleList = knowledgeMap.keySet().stream()
                .sorted()
                .collect(Collectors.toList());

        for (String title : knowledgeTitleList) {
            sb.append(title);
            sb.append(knowledgeMap.get(title));
        }

        return Md5Utils.getMd5String(sb.toString());
    }



}
