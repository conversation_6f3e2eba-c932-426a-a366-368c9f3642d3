package com.yiwise.dialogflow.service.impl;

import com.github.pagehelper.PageHelper;
import com.yiwise.base.common.context.AppContextUtils;
import com.yiwise.base.common.utils.bean.MyBeanUtils;
import com.yiwise.base.common.utils.collection.MyCollectionUtils;
import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.base.model.enums.EnabledStatusEnum;
import com.yiwise.base.model.enums.SystemEnum;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.api.dto.request.BotApprovalRequest;
import com.yiwise.dialogflow.api.dto.request.BotListRequest;
import com.yiwise.dialogflow.api.dto.request.CopyBotRequest;
import com.yiwise.dialogflow.api.dto.response.BotInfo;
import com.yiwise.dialogflow.api.dto.response.SimpleBotApprovalResult;
import com.yiwise.dialogflow.api.dto.response.SimpleBotInfo;
import com.yiwise.callactivity.api.dto.DependSubSceneQueryDTO;
import com.yiwise.callactivity.api.dto.SimpleActivitySubSceneDTO;
import com.yiwise.dialogflow.api.dto.request.*;
import com.yiwise.dialogflow.api.dto.response.*;
import com.yiwise.dialogflow.api.dto.response.variable.VariableInfoDTO;
import com.yiwise.dialogflow.api.enums.V3BotTypeEnum;
import com.yiwise.dialogflow.common.ApplicationConstant;
import com.yiwise.dialogflow.entity.bo.BotBO;
import com.yiwise.dialogflow.entity.bo.SnapshotInvalidFailItemMsg;
import com.yiwise.dialogflow.entity.context.RobotResourceContext;
import com.yiwise.dialogflow.entity.dto.BotNameCountDTO;
import com.yiwise.dialogflow.entity.dto.DialogFlowPostDTO;
import com.yiwise.dialogflow.entity.dto.OperationLogDTO;
import com.yiwise.dialogflow.api.enums.AudioTypeEnum;
import com.yiwise.dialogflow.entity.enums.*;
import com.yiwise.dialogflow.entity.po.*;
import com.yiwise.dialogflow.entity.po.intent.*;
import com.yiwise.dialogflow.entity.po.remote.TenantPO;
import com.yiwise.dialogflow.entity.po.remote.UserPO;
import com.yiwise.dialogflow.entity.query.BotQuery;
import com.yiwise.dialogflow.entity.vo.*;
import com.yiwise.dialogflow.entity.vo.audio.BaseAnswerContentVO;
import com.yiwise.dialogflow.mapper.BotFolderRelPOMapper;
import com.yiwise.dialogflow.mapper.BotPOMapper;
import com.yiwise.dialogflow.service.*;
import com.yiwise.dialogflow.service.botgenerate.BotAnswerRewriteTaskService;
import com.yiwise.dialogflow.service.entitycollect.EntityService;
import com.yiwise.dialogflow.service.intent.IntentConfigService;
import com.yiwise.dialogflow.service.intent.IntentRuleActionService;
import com.yiwise.dialogflow.service.intent.IntentRuleService;
import com.yiwise.dialogflow.service.llm.LlmLabelService;
import com.yiwise.dialogflow.service.magic.MagicActivityConfigService;
import com.yiwise.dialogflow.service.remote.*;
import com.yiwise.dialogflow.service.remote.activity.CallActivitySubSceneService;
import com.yiwise.dialogflow.service.thirdpart.douyin.DouyinCallbackService;
import com.yiwise.dialogflow.service.train.TrainService;
import com.yiwise.middleware.mysql.service.impl.BasicServiceImpl;
import com.yiwise.middleware.tts.enums.TtsVoiceEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.codehaus.plexus.util.dag.CycleDetectedException;
import org.codehaus.plexus.util.dag.DAG;
import org.codehaus.plexus.util.dag.TopologicalSorter;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Slf4j
@Service
public class BotServiceImpl extends BasicServiceImpl<BotPO> implements BotService {

    @Resource
    private BotRefService botRefService;

    @Resource
    private DialogFlowService dialogFlowService;

    @Resource
    private UserService userService;

    @Resource
    private CustomerTrackTypeService customerTrackTypeService;

    @Resource
    private CustomerSceneService customerSceneService;

    @Resource
    private AnswerAudioManagerService answerAudioManagerService;

    @Resource
    private IntentLevelTagService intentLevelTagService;

    @Resource
    private RobotSnapshotService robotSnapshotService;

    @Resource
    private BotPOMapper botPOMapper;

    @Resource
    private VariableService variableService;

    @Resource
    private BotConfigService botConfigService;

    @Resource
    private IntentConfigService intentConfigService;

    @Resource
    private IntentRuleService intentRuleService;

    @Lazy
    @Resource
    private StepService stepService;

    @Lazy
    @Resource
    private SpecialAnswerConfigService specialAnswerConfigService;

    @Lazy
    @Resource
    private AnswerManagerService answerManagerService;

    @Lazy
    @Resource
    private KnowledgeService knowledgeService;

    @Lazy
    @Resource
    private IntentRuleActionService intentRuleActionService;

    @Lazy
    @Resource
    private StepNodeService stepNodeService;

    @Resource
    private MongoTemplate mongoTemplate;

    @Resource
    private IntentTagService intentTagService;

    @Resource
    private GroupService groupService;

    @Resource
    private FolderService folderService;

    @Resource
    private BotFolderRelPOMapper botFolderRelPOMapper;

    @Resource
    private OperationLogService operationLogService;

    @Resource
    private EntityService entityService;

    @Resource
    private EntityCollectConfigService entityCollectConfigService;

    @Resource
    private MaintainToolsService maintainToolsService;

    @Lazy
    @Resource
    private BotService botService;

    @Lazy
    @Resource
    private TrainService trainService;

    @Resource
    private TenantService tenantService;

    @Lazy
    @Resource
    private BotAnswerRewriteTaskService botAnswerRewriteTaskService;

    @Resource
    private CallActivitySubSceneService callActivitySubSceneService;

    @Lazy
    @Resource
    private SensitiveWordsService sensitiveWordsService;

    @Resource
    private AlgorithmLabelService algorithmLabelService;

    @Resource
    private BotExportService botExportService;

    @Lazy
    @Resource
    private MagicActivityConfigService magicActivityConfigService;

    @Lazy
    @Resource
    private DouyinCallbackService douyinCallbackService;

    @Resource
    private LlmLabelService llmLabelService;

    /**
     * 创建Bot
     * - 在AICC侧同步创建话术
     * - 同名校验在AICC侧进行
     * - 初始化Bot资源
     */
    @Override
    public BotPO create(BotCreateRequestVO createRequest) {
        BotPO bot = new BotPO();
        BotPO srcBot = null;
        if (Objects.nonNull(createRequest.getSrcBotId())) {
            srcBot = getById(createRequest.getSrcBotId());
            if (Objects.isNull(srcBot)) {
                throw new ComException(ComErrorCode.NOT_EXIST, "源Bot不存在");
            }
        }

        if (V3BotTypeEnum.isMagicTemplate(createRequest.getType())) {
            if (Objects.isNull(createRequest.getEasyCallVersion())) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "模板版本不能为空");
            }
        }

        // 轻量化模板的易呼版本复制有一些限制, 需要校验源 bot 和复制后的目标 bot 的易呼版本
        if (Objects.nonNull(srcBot)) {
            if (V3BotTypeEnum.isMagicTemplate(srcBot.getType())) {
                if (EasyCallVersionEnum.V2.equals(srcBot.getEasyCallVersion())
                        && EasyCallVersionEnum.V1.equals(createRequest.getEasyCallVersion())) {
                    throw new ComException(ComErrorCode.VALIDATE_ERROR, "易呼 2.0 不支持复制为易呼 1.0 ");
                }
            }
        }

        try {
            BeanUtils.copyProperties(createRequest, bot);

            if (BotCreateSourceEnum.MAGIC_AICC.equals(createRequest.getCreateSource())) {
                bot.setName(srcBot.getName() + generateTimeSequence());
            }
            // 在AICC侧创建话术
            DialogFlowPostDTO dialogFlowPostDTO = new DialogFlowPostDTO();
            BeanUtils.copyProperties(bot, dialogFlowPostDTO);
            dialogFlowPostDTO.setUserId(createRequest.getUserId());
            dialogFlowPostDTO.setBotType(BotTypeEnum.V3);
            dialogFlowPostDTO.setTenantId(createRequest.getTenantId());
            Long dialogFlowId = dialogFlowService.createNewDialogFlow(dialogFlowPostDTO);


            // botId和dialogFlowId保持一致，兼容使用
            bot.setBotId(dialogFlowId);

            if (BotCreateSourceEnum.MAGIC_AICC.equals(createRequest.getCreateSource())) {
                bot.setName(srcBot.getName() + dialogFlowId);
                dialogFlowPostDTO.setName(srcBot.getName() + dialogFlowId);
                dialogFlowPostDTO.setId(dialogFlowId);
                dialogFlowService.updateNewDialogFlow(dialogFlowPostDTO);
            }

            // Bot复制
            Long createTenantId = createRequest.getTenantId();
            if (Objects.nonNull(srcBot)) {
                copyBotParams(createRequest, srcBot, bot);
                // 复制场景下, 先设置目标bot为删除状态, 待复制成功后, 再恢复
                bot.setEnableStatus(EnabledStatusEnum.DISABLE);
            } else {
                bot.setEnableStatus(EnabledStatusEnum.ENABLE);
            }
            bot.setVisibleStatus(EnabledStatusEnum.ENABLE);
            if (Objects.isNull(bot.getType())) {
                bot.setType(V3BotTypeEnum.COMMON);
            }
            if (Objects.nonNull(createRequest.getType())) {
                bot.setType(createRequest.getType());
            }
            if (Objects.nonNull(createRequest.getCustomerSceneId())) {
                bot.setCustomerSceneId(createRequest.getCustomerSceneId());
            }
            bot.setEasyCallVersion(createRequest.getEasyCallVersion());
            bot.setPublished(false);
            bot.setAuditStatus(AuditStatusEnum.DRAFT);
            bot.setCreateTenantId(createTenantId);
            bot.setCreateTime(LocalDateTime.now());
            bot.setCreateUserId(createRequest.getUserId());
            bot.setUpdateUserId(createRequest.getUserId());
            bot.setUpdateTime(LocalDateTime.now());
            bot.setCreateSource(createRequest.getCreateSource() == null ?
                    BotCreateSourceEnum.MANUAL :
                    createRequest.getCreateSource());
            if (BotCreateSourceEnum.GENERATE_REWRITE.equals(bot.getCreateSource())) {
                bot.setGenerateStatus(BotGenerateStatusEnum.READY);
            } else {
                bot.setGenerateStatus(null);
            }

            if (V3BotTypeEnum.isMagic(bot.getType())) {
                bot.setMagicTemplateId(srcBot.getBotId());
            }
            // 这里不调用 saveNotNull, 因为新的包里面要求不能主动设置 id
            botPOMapper.insertSelective(bot);

            BotRefPO botRefPO = new BotRefPO();
            botRefPO.setBotId(bot.getBotId());
            botRefPO.setTenantId(createTenantId);
            botRefPO.setDialogFlowId(dialogFlowId);
            botRefPO.setCreateUserId(bot.getCreateUserId());
            botRefPO.setUpdateUserId(bot.getCreateUserId());
            botRefService.create(botRefPO);

            // 创建文件夹关联
            if (Objects.nonNull(createRequest.getFolderId())) {
                BotFolderRelPO botFolderRelPO = new BotFolderRelPO();
                botFolderRelPO.setTenantId(createRequest.getTenantId());
                botFolderRelPO.setFolderId(createRequest.getFolderId());
                botFolderRelPO.setBotId(bot.getBotId());
                botFolderRelPO.setCreateUserId(bot.getCreateUserId());
                botFolderRelPO.setUpdateUserId(bot.getCreateUserId());
                botFolderRelPOMapper.insertSelective(botFolderRelPO);
            }

            // 初始化bot其他资源
            if (Objects.isNull(srcBot)) {
                initBotResource(bot.getBotId(), bot.getCreateUserId());
            } else {
                boolean fromSnapshot = BotCreateSourceEnum.MAGIC_AICC.equals(createRequest.getCreateSource());
                copyBotResource(createRequest.getSrcBotId(), srcBot.getType(), bot.getBotId(), bot.getType(), bot.getCreateUserId(),
                        createRequest.getSystemType(), bot.getIntentLevelTagId(), createRequest.getCreateSource(), fromSnapshot, srcBot, bot);
                updateEnableStatus(bot.getBotId(), EnabledStatusEnum.ENABLE);
                if (!createRequest.getIsAutoCreate()) {
                    // 自动创建会在创建结束后自行添加操作日志
                    operationLogService.save(bot.getBotId(), OperationLogTypeEnum.BOT_COPY, null,
                            String.format("复制自%s【%s：%s】", srcBot.getType().getDesc(), createRequest.getSrcBotId(), Optional.ofNullable(botPOMapper.selectByPrimaryKey(createRequest.getSrcBotId())).map(BotPO::getName).orElse("")),
                            bot.getCreateUserId());
                }
            }
        } catch (Exception e) {
            log.error("新增bot失败:失败原因:", e);
            if (bot.getBotId() != null) {
                delete(bot.getBotId(), bot.getCreateUserId());
            }
            throw new ComException(ComErrorCode.UNKNOWN_ERROR, String.format("新增bot失败,失败原因:%s", e.getMessage()));
        }

        // 整理修正下答案标签(如果复制的话, 可能源bot有脏数据)
        maintainToolsService.resetAnswerLabelIfDuplicate(bot.getBotId());
        return bot;
    }

    private void updateEnableStatus(Long botId, EnabledStatusEnum status) {
        BotPO botPO = new BotPO();
        botPO.setBotId(botId);
        botPO.setEnableStatus(status);
        updateNotNull(botPO);
    }

    private void copyBotParams(BotCreateRequestVO createRequest, BotPO srcBot, BotPO targetBot) {
        targetBot.setDomainName(srcBot.getDomainName());
        if (BooleanUtils.isTrue(createRequest.getIsAutoCreate())) {
            BeanUtils.copyProperties(srcBot, targetBot, "botId", "name", "srcBotId", "createUserId", "tenantId", "isAutoCreate", "qrCodeId", "qrCodeName");
        } else {
            BeanUtils.copyProperties(srcBot, targetBot, "botId", "tenantId", "name", "description", "intentLevelTagId", "industry", "subIndustry", "customerTrackType", "customerTrackSubType", "folderId", "srcBotId", "systemType", "createUserId", "qrCodeId", "qrCodeName", "type");
        }
    }

    @Override
    public Long createFromSnapshot(RobotSnapshotPO snapshot) {
        if (Objects.isNull(snapshot)) {
            throw new ComException(ComErrorCode.UNKNOWN_ERROR, "机器人快照不存在");
        }

        String botName = snapshot.getBot().getName();
        String newName = String.format("%s_%s", botName, System.currentTimeMillis());
        BotPO bot = snapshot.getBot();
        Long oldDialogFlowId = bot.getBotId();
        bot.setBotId(null);
        bot.setName(newName);
        bot.setAuditStatus(AuditStatusEnum.DRAFT);
        // 复制完成时, 先不可见, 因为有些资源在请求到列表的时候, 也会初始化, 那么这个时候可能出现 key 重复的问题
        bot.setEnableStatus(EnabledStatusEnum.DISABLE);
        bot.setVisibleStatus(EnabledStatusEnum.ENABLE);

        // 在AICC侧创建话术
        DialogFlowPostDTO dialogFlowPostDTO = new DialogFlowPostDTO();
        BeanUtils.copyProperties(bot, dialogFlowPostDTO);
        dialogFlowPostDTO.setUserId(bot.getCreateUserId());
        dialogFlowPostDTO.setBotType(BotTypeEnum.V3);
        dialogFlowPostDTO.setTenantId(ApplicationConstant.OPE_TENANT_ID);
        Long dialogFlowId = dialogFlowService.createNewDialogFlow(dialogFlowPostDTO);

        bot.setBotId(dialogFlowId);
        // 创建Bot
        bot.setCreateTime(LocalDateTime.now());
        bot.setUpdateTime(LocalDateTime.now());

        // 这里不调用 saveNotNull, 因为新的包里面要求不能主动设置 id
        botPOMapper.insertSelective(bot);

        BotRefPO botRefPO = new BotRefPO();
        botRefPO.setBotId(bot.getBotId());
        botRefPO.setTenantId(ApplicationConstant.OPE_TENANT_ID);
        botRefPO.setDialogFlowId(dialogFlowId);
        botRefService.create(botRefPO);

        // 创建操作日志
        operationLogService.save(bot.getBotId(), OperationLogTypeEnum.BOT_MIGRATION, null,
                String.format("迁移自【%s：%s】", oldDialogFlowId, botName),
                snapshot.getCreateUserId());

        List<Integer> intentTagDetailCodeList = intentTagService.getIntentTagDetailCodeList(snapshot.getBot().getIntentLevelTagId());
        RobotResourceContext context = RobotResourceContext.init(0L, bot.getBotId(), snapshot.getBot().getCreateUserId(), SystemEnum.OPE);
        context.setIntentLevelTagId(snapshot.getBot().getIntentLevelTagId());
        context.setIntentTagDetailCodeList(intentTagDetailCodeList);
        context.setSnapshot(snapshot);
        loadResourceFromSnapshot(context);
        updateEnableStatus(bot.getBotId(), EnabledStatusEnum.ENABLE);
        return bot.getBotId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(BotPO botPO) {
        Long botId = botPO.getBotId();
        BotPO old = botService.getById(botId);
        if (Objects.isNull(old)) {
            return;
        }
        // 切换行业或者场景清空所有的标签
        if (!Objects.equals(old.getCustomerTrackType(), botPO.getCustomerTrackType()) || !Objects.equals(old.getCustomerSceneId(), botPO.getCustomerSceneId())) {
            algorithmLabelService.deleteAll(botId);
        }

        Long dialogFlowId = botRefService.getDialogFlowId(botId);

        // 在AICC侧更新话术
        DialogFlowPostDTO dialogFlowPostDTO = new DialogFlowPostDTO();
        BeanUtils.copyProperties(botPO, dialogFlowPostDTO);
        dialogFlowPostDTO.setId(dialogFlowId);
        dialogFlowPostDTO.setUserId(botPO.getCreateUserId());
        dialogFlowPostDTO.setBotType(BotTypeEnum.V3);
        dialogFlowService.updateNewDialogFlow(dialogFlowPostDTO);

        botPO.setAuditStatus(AuditStatusEnum.DRAFT);
        updateNotNull(botPO);
    }

    private void copyBotResource(Long srcBotId,
                                 V3BotTypeEnum srcBotType,
                                 Long targetBotId,
                                 V3BotTypeEnum targetBotType,
                                 Long currentUserId,
                                 SystemEnum systemEnum,
                                 Long intentLevelTagId,
                                 BotCreateSourceEnum botCreateSource,
                                 boolean fromSnapshot,
                                 BotPO srcBot,
                                 BotPO targetBot) {
        List<Integer> intentTagDetailCodeList = intentTagService.getIntentTagDetailCodeList(intentLevelTagId);

        // 初始化上下文
        RobotResourceContext context = RobotResourceContext.init(srcBotId, targetBotId, currentUserId, systemEnum, intentLevelTagId, intentTagDetailCodeList);
        context.setBotCreateSource(botCreateSource);
        context.setSrcBotType(srcBotType);
        context.setTargetBotType(targetBotType);
        context.setSrcTenantId(botRefService.getTenantIdByBotId(srcBotId));
        context.setTargetTenantId(botRefService.getTenantIdByBotId(targetBotId));
        context.setSrcBot(srcBot);
        context.setTargetBot(targetBot);

        boolean loadNewestData = true;
        if (fromSnapshot) {
            // 从快照中复制
            RobotSnapshotPO snapshot = robotSnapshotService.getLastPublishRobotSnapshot(srcBotId);
            if (Objects.nonNull(snapshot)) {
                loadNewestData = false;
                context.setSnapshot(snapshot);
                log.info("从快照中复制, 快照版本:{}", snapshot.getVersion());
            }
        }

        if (loadNewestData) {
            // 获取接口所有的实现类并调用save
            Map<String, RobotResourceService> beansOfType = AppContextUtils.getContext().getBeansOfType(RobotResourceService.class);
            for (Map.Entry<String, RobotResourceService> entry : beansOfType.entrySet()) {
                log.info("saveToSnapshot, beanName={}", entry.getKey());
                entry.getValue().saveToSnapshot(context);
            }
        }

        loadResourceFromSnapshot(context);
    }


    private void loadResourceFromSnapshot(RobotResourceContext context) {
        // 获取接口所有的实现类并调用save
        Map<String, RobotResourceService> beansOfType = AppContextUtils.getContext().getBeansOfType(RobotResourceService.class);

        // 构建资源的DAG并进行拓扑排序
        DAG dag = new DAG();
        // 添加顶点
        for (String beanName : beansOfType.keySet()) {
            dag.addVertex(beanName);
        }
        // 添加边
        try {
            for (Map.Entry<String, RobotResourceService> entry : beansOfType.entrySet()) {
                List<Class<? extends RobotResourceService>> dependsOn = entry.getValue().dependsOn();
                if (CollectionUtils.isNotEmpty(dependsOn)) {
                    for (Class<? extends RobotResourceService> aClass : dependsOn) {
                        // 被依赖的对象为起点，当前对象为终点
                        dag.addEdge(StringUtils.uncapitalize(aClass.getSimpleName()), entry.getKey());
                    }
                }
            }
        } catch (CycleDetectedException e) {
            log.error("构造DAG错误", e);
        }
        // 拓扑排序
        List<String> list = TopologicalSorter.sort(dag);
        Collections.reverse(list);

        // 根据排序后的顺序进行调用
        for (String name : list) {
            log.info("loadFromSnapshot, beanName={}", name);
            if (Objects.nonNull(beansOfType.get(name))) {
                beansOfType.get(name).loadFromSnapshot(context);
            }
        }

        // 根据排序后的顺序进行调用
        for (String name : list) {
            log.info("resetRefId, beanName={}", name);
            if (Objects.nonNull(beansOfType.get(name))) {
                beansOfType.get(name).resetRefId(context);
            }
        }
    }


    private void initBotResource(Long botId, Long userId) {
        // 默认的开场白流程
        stepService.initOnCreateBot(botId);
        // 变量
        variableService.initOnCreateBot(botId);
        // 录音和语音设置
        botConfigService.initOnCreateBot(botId);
        // 意图设置
        intentConfigService.initOnCreateBot(botId);

        specialAnswerConfigService.initOnCreateBot(botId);

        intentRuleService.initDialogFlowIntentRule(botId, null, false);

        // 初始化'全部知识'分组
        groupService.initAllKnowledgeGroupOnCreateBot(botId, userId);
        // 初始化'全部意图'分组
        groupService.initAllIntentGroupOnCreateBot(botId, userId);

        entityService.initSystemEntity(botId);
        entityCollectConfigService.init(botId);

        llmLabelService.initOnCreateBot(botId, userId);
    }


    /**
     * 删除Bot
     * - 同步删除AICC侧话术
     * - 软删除
     */
    @Override
    public void delete(Long botId, Long userId) {
        // 已被场景关联的BOT不允许删除
        List<String> dependentSceneNameList = getDependentSceneNameList(botId);
        if (CollectionUtils.isNotEmpty(dependentSceneNameList)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "已被场景关联，无法删除，请前往资源配置查看");
        }

        Long dialogFlowId = botRefService.getDialogFlowId(botId);
        if (dialogFlowId != null) {
            dialogFlowService.deleteDialogFlow(dialogFlowId, true);
        }

        Long tenantId = botRefService.getTenantIdByBotId(botId);
        // 删除关联关系
        botRefService.deleteByBotId(botId);
        Example example = new Example(BotFolderRelPO.class);
        example.createCriteria().andEqualTo("botId", botId);
        botFolderRelPOMapper.deleteByExample(example);
        BotPO updatePO = new BotPO();
        updatePO.setBotId(botId);
        updatePO.setEnableStatus(EnabledStatusEnum.DELETE);
        updateNotNull(updatePO);

        douyinCallbackService.callback(botId, tenantId);
        operationLogService.save(botId, OperationLogTypeEnum.BOT_CONFIG, OperationLogResourceTypeEnum.BOT, "删除话术", userId);
    }

    /**
     * - 平铺视图列表及搜索：全局
     * - 文件夹视图首页列表：文件夹外的所有话术
     * - 文件夹视图首页搜索：全局
     * - 文件夹视图子文件夹列表：该文件夹下（不包含子文件夹）的所有话术
     * - 文件夹视图子文件夹搜索：该文件夹下（包含子文件夹）的所有话术
     */
    @Override
    public PageResultObject<BotVO> list(BotQuery botQuery) {
        if (Objects.nonNull(botQuery.getFolderId())) {
            // 首页搜索为全局搜索，不走文件夹相关逻辑
            boolean isSearch = StringUtils.isNotEmpty(botQuery.getName())
                    || CollectionUtils.isNotEmpty(botQuery.getAuditStatusSet())
                    || CollectionUtils.isNotEmpty(botQuery.getCustomerTrackIdList())
                    || CollectionUtils.isNotEmpty(botQuery.getCustomerTrackSubIdList())
                    || CollectionUtils.isNotEmpty(botQuery.getCreateUserIdList());
            if (!(botQuery.getFolderId() == -1 && isSearch)) {
                if (botQuery.getFolderId() == -1) {
                    botQuery.setNotInAnyFolder(true);
                } else {
                    if (isSearch) {
                        botQuery.setFolderIdList(folderService.subFolderIdList(botQuery.getFolderId()));
                    } else {
                        botQuery.setFolderIdList(Collections.singletonList(botQuery.getFolderId()));
                    }
                }
            }
        }
        PageHelper.startPage(botQuery.getPageNum(), botQuery.getPageSize());
        List<BotVO> botVOList = botPOMapper.listBotByCondition(botQuery);
        wrapVOList(botQuery, botVOList);
        return PageResultObject.of(botVOList);
    }

    @Override
    public List<IdNamePair<Long, String>> listAllCreateUser(Long tenantId) {
        List<Long> userIdList = botPOMapper.listAllCreateUserId(tenantId);
        if (CollectionUtils.isEmpty(userIdList)) {
            return Collections.emptyList();
        }
        return MyCollectionUtils.listToConvertList(userService.getUserByIdList(userIdList),
                user -> new IdNamePair<>(user.getUserId(), user.getName()));
    }

    @Override
    public PageResultObject<BotVO> queryListWithoutWrapVO(BotQuery botQuery) {
        PageHelper.startPage(botQuery.getPageNum(), botQuery.getPageSize());
        List<BotVO> botVOList = botPOMapper.listBotByCondition(botQuery);
        return PageResultObject.of(botVOList);
    }

    @Override
    public BotVO detail(Long botId) {
        BotVO botVO = botPOMapper.getBotByBotId(botId);
        if (Objects.isNull(botVO)) {
            return null;
        }
        wrapVO(Collections.singletonList(botVO));
        return botVO;
    }

    @Override
    public BotPO getById(Long botId) {
        return botPOMapper.selectByPrimaryKey(botId);
    }

    @Override
    public List<BotPO> getByIdList(List<Long> botIdList) {
        if (CollectionUtils.isEmpty(botIdList)) {
            return Collections.emptyList();
        }
        return botPOMapper.getBotByBotIdList(botIdList);
    }

    @Override
    public List<BotVO> queryListWithoutPage(BotQuery botQuery) {
        return botPOMapper.listBotByCondition(botQuery);
    }

    @Override
    public void onUpdateBotResource(Long botId) {
        updateAuditStatus(botId, AuditStatusEnum.DRAFT);
    }

    /**
     * 话术发生变动后，更新发布状态
     */
    @Override
    public void updateAuditStatus(Long botId, AuditStatusEnum auditStatus) {
        updateAuditStatus(botId, auditStatus, true);
    }

    private void updateAuditStatus(Long botId, AuditStatusEnum auditStatus, boolean syncAuditStatus) {
        BotPO oldBot = null;
        if (AuditStatusEnum.DRAFT.equals(auditStatus)) {
            oldBot = selectByKey(botId);
            if (Objects.isNull(oldBot)) {
                return;
            }
        }
        BotPO updatePO = new BotPO();
        updatePO.setBotId(botId);
        updatePO.setAuditStatus(auditStatus);

        botPOMapper.updateAuditStatus(botId, auditStatus.getCode());
        if (AuditStatusEnum.DRAFT.equals(auditStatus)) {
            answerAudioManagerService.clearAudioCompleteProgressCache(botId);
            // 画布节点自动保存会频繁调用, 所以在调用的时候进行判断, 如果状态未变更, 就不同步到aicc了, 10分钟至少同步一次
            if (!AuditStatusEnum.DRAFT.equals(oldBot.getAuditStatus())
                    || (Objects.nonNull(oldBot.getUpdateTime()) && LocalDateTime.now().minusMinutes(10).isAfter(oldBot.getUpdateTime()))) {
                if (syncAuditStatus) {
                    syncAiccDialogFlowStatus(botId, auditStatus);
                }
            }
        } else if (syncAuditStatus){
            syncAiccDialogFlowStatus(botId, auditStatus);
        }
    }

    @Override
    public void updateDomainName(Long botId, String domainName) {
        BotPO updatePO = new BotPO();
        updatePO.setBotId(botId);
        updatePO.setDomainName(domainName);
        botPOMapper.updateByPrimaryKeySelective(updatePO);
    }

    @Override
    public void updateAsrProvider(Long botId, Long asrProviderId) {
        BotPO updatePO = new BotPO();
        updatePO.setBotId(botId);
        updatePO.setAsrProviderId(asrProviderId);
        botPOMapper.updateByPrimaryKeySelective(updatePO);
    }

    /**
     * 包装视图类
     */
    private void wrapVO(List<BotVO> botList) {
        if (CollectionUtils.isEmpty(botList)) {
            return;
        }

        List<IntentConfigPO> intentConfigList = intentConfigService.queryByBotIdList(botList.stream().map(BotVO::getBotId).collect(Collectors.toList()));
        Map<Long, IntentConfigPO> intentConfigMap = MyCollectionUtils.listToMap(intentConfigList, IntentConfigPO::getBotId);
        List<Long> intetnLevelIdList = botList.stream().map(BotVO::getIntentLevelTagId).filter(Objects::nonNull).collect(Collectors.toList());
        Map<Long, String> intentLevelTagMap = intentLevelTagService.getIntentLevelTagNameMap(intetnLevelIdList);
        Map<Integer, String> sceneIdNameMap = new HashMap<>();
        for (BotVO botVO : botList) {
            botVO.setCustomerTrackTypeName(customerTrackTypeService.getCustomerTrackTypeName(botVO.getCustomerTrackType()));
            botVO.setCustomerTrackSubTypeName(customerTrackTypeService.getCustomerTrackTypeName(botVO.getCustomerTrackSubType()));
            String sceneName = sceneIdNameMap.computeIfAbsent(botVO.getCustomerSceneId(), sceneId -> customerSceneService.getCustomerSceneName(sceneId));
            botVO.setCustomerSceneName(sceneName);
            botVO.setIntentLevelTagName(intentLevelTagMap.getOrDefault(botVO.getIntentLevelTagId(), "未知"));
            IntentConfigPO intentConfigPO = intentConfigMap.get(botVO.getBotId());
            botVO.setEnableAlgorithm(Objects.nonNull(intentConfigPO) && BooleanUtils.isTrue(intentConfigPO.getEnableAlgorithm()));
            botVO.setAlgorithmLabelDomain(algorithmLabelService.obtainDomain(botVO.getCustomerTrackType(), botVO.getCustomerSceneId()));
        }
    }

    private void wrapVOList(BotQuery botQuery, List<BotVO> botVOList) {
        if (CollectionUtils.isEmpty(botVOList)) {
            return;
        }
        wrapVO(botVOList);
        wrapBotVO(botQuery, botVOList);

        List<Long> botIdList = botVOList.stream().map(BotVO::getBotId).collect(Collectors.toList());
        // 回写是否有 ai 改写记录字段
        Set<Long> successBotIdSet = botAnswerRewriteTaskService.getExistSuccessTaskBotIdSet(botIdList);
        botVOList.forEach(bot -> bot.setHasRewriteSuccessRecord(successBotIdSet.contains(bot.getBotId())));
    }

    private void wrapBotVO(BotQuery botQuery, List<BotVO> botVOList) {
        if (CollectionUtils.isEmpty(botVOList)) {
            return;
        }

        List<Long> botIdList = botVOList.stream().map(BotVO::getBotId).collect(Collectors.toList());
        Map<Long, Long> botId2RecordUserIdMap = new HashMap<>();

        //获取recorderUserId
        List<BotConfigPO> botConfigList = botConfigService.queryByBotIdList(botIdList);

        if (CollectionUtils.isNotEmpty(botConfigList)) {
            botId2RecordUserIdMap = botConfigList.stream()
                    .filter(config -> Objects.nonNull(config.getAudioConfig()) && !AudioTypeEnum.COMPOSE.equals(config.getAudioConfig().getAudioType()))
                    .filter(config -> Objects.nonNull(config.getAudioConfig().getRecordUserId()))
                    .collect(Collectors.toMap(BotConfigPO::getBotId, config -> config.getAudioConfig().getRecordUserId()));
        }

        List<BotRefPO> refList = botRefService.getByBotIdList(botIdList);
        Map<Long, Long> botId2dialogFlowId = MyCollectionUtils.listToConvertMap(refList, BotRefPO::getBotId, BotRefPO::getDialogFlowId);
        //获取创建者信息
        Map<Long, Long> finalBotId2RecordUserIdMap = botId2RecordUserIdMap;
        List<Long> createUserIdList = botVOList.stream().map(BotVO::getCreateUserId).filter(Objects::nonNull).collect(Collectors.toList());
        createUserIdList.addAll(botId2RecordUserIdMap.values().stream().filter(Objects::nonNull).collect(Collectors.toList()));
        createUserIdList = createUserIdList.stream().distinct().collect(Collectors.toList());
        List<UserPO> userList = userService.getUserByIdList(createUserIdList);
        Map<Long, String> userIdNameMap = MyCollectionUtils.listToConvertMap(userList, UserPO::getUserId, UserPO::getName);
        botVOList.forEach(bot -> {
            bot.setDialogFlowId(botId2dialogFlowId.get(bot.getBotId()));
            bot.setCreateUserName(userIdNameMap.getOrDefault(bot.getCreateUserId(), "未知"));
            if (!finalBotId2RecordUserIdMap.isEmpty()) {
                Long recordUserId = finalBotId2RecordUserIdMap.get(bot.getBotId());
                // todo 这里逻辑应该是重复了
                if (recordUserId != null) {
                    bot.setRecorderName(userIdNameMap.getOrDefault(recordUserId, "未知"));
                }
            }
        });

        // 文件夹路径
        Long tenantId = botQuery.getTenantId();
        if (SystemEnum.isOPE(botQuery.getSystemType())) {
            tenantId = 0L;
        }
        List<BotFolderRelPO> botFolderRelPOList = botFolderRelPOMapper.listByBotIdList(tenantId, botIdList);
        List<Long> folderIdList = MyCollectionUtils.listToConvertList(botFolderRelPOList, BotFolderRelPO::getFolderId);
        Map<Long, Long> relMap = MyCollectionUtils.listToMap(botFolderRelPOList, BotFolderRelPO::getBotId, BotFolderRelPO::getFolderId);
        Map<Long, List<IdNamePair<Long, String>>> pathMap = folderService.pathMap(tenantId, folderIdList);
        botVOList.forEach(botVO -> {
            botVO.setFolderId(relMap.get(botVO.getBotId()));
            botVO.setFolderPath(pathMap.get(botVO.getFolderId()));
        });
    }

    @Override
    public BotSnapshotVO publish(BotPublishVO publishParam, Long userId) {
        String errorMsg = null;
        Long botId = publishParam.getBotId();
        BotPO bot = botPOMapper.getBotByBotId(botId);
        if (Objects.isNull(bot)) {
            throw new ComException(ComErrorCode.NOT_EXIST, "bot不存在");
        }
        RobotSnapshotPO oldSnapshot = robotSnapshotService.getLastPublishRobotSnapshot(botId);
        RobotSnapshotCreateResult snapshotResult = robotSnapshotService.create(botId, userId, BooleanUtils.isTrue(publishParam.getIgnoreWarning()));
        if (BooleanUtils.isNotTrue(snapshotResult.isSuccess())) {
            BotSnapshotVO botSnapshotVO = new BotSnapshotVO();
            botSnapshotVO.setBotId(botId);
            botSnapshotVO.setAuditStatusEnum(AuditStatusEnum.INVALID);
            botSnapshotVO.setErrorMsg(errorMsg);
            botSnapshotVO.setSnapshotInvalidFailItemMsgList(snapshotResult.getMsgList());
            botSnapshotVO.setNeedBindVariable(BooleanUtils.isTrue(snapshotResult.getNeedBindVariable()));
            botSnapshotVO.setVariableBindInfoList(snapshotResult.getVariableBindInfoList());
            //完整性校验日志
            operationLogService.save(botId, OperationLogTypeEnum.PUBLISH_AUDIT,
                    OperationLogResourceTypeEnum.PUBLISH_AUDIT, "完整性校验不通过", userId);
            return botSnapshotVO;
        }

        BotPO botPO = new BotPO();
        try {
            //自动审核流程
            AuditStatusEnum status = autoCheckDialogFlow(botId, oldSnapshot, snapshotResult.getSnapshot());
            botPO.setAuditStatus(status);
            if (AuditStatusEnum.PASS.equals(status)) {
                douyinCallbackService.callback(botId);
            }
        } catch (ComException e) {
            botPO.setAuditStatus(AuditStatusEnum.FAIL);
            errorMsg = e.getMessage();
        }
        //更新快照发布状态
        if (AuditStatusEnum.PASS.equals(botPO.getAuditStatus())) {
            robotSnapshotService.updateRobotSnapshotPublishStatus(botId, snapshotResult.getSnapshot().getVersion());
            //自动审核通过日志
            operationLogService.save(botId, OperationLogTypeEnum.PUBLISH_AUDIT,
                    OperationLogResourceTypeEnum.PUBLISH_AUDIT, "自动审核通过", userId);
        }
        //更新bot
        botPO.setFailReason(errorMsg);
        botPO.setBotId(botId);
        botPOMapper.updateAuditStatusAndReason(botPO, botPO.getAuditStatus().getCode());
//        updateNotNull(botPO);
        //同步AICC侧话术状态
        syncAiccDialogFlowStatus(botId, botPO.getAuditStatus());
        BotSnapshotVO botSnapshotVO = new BotSnapshotVO();
        botSnapshotVO.setBotId(botId);
        botSnapshotVO.setAuditStatusEnum(botPO.getAuditStatus());
        botSnapshotVO.setErrorMsg(errorMsg);
        botSnapshotVO.setSnapshotInvalidFailItemMsgList(snapshotResult.getMsgList());
        botSnapshotVO.setNeedBindVariable(BooleanUtils.isTrue(snapshotResult.getNeedBindVariable()));
        botSnapshotVO.setVariableBindInfoList(snapshotResult.getVariableBindInfoList());
        return botSnapshotVO;
    }

    private void syncAiccDialogFlowStatus(Long botId, AuditStatusEnum auditStatus) {
        try {
            Long dialogFlowId = botRefService.getDialogFlowId(botId);
            if (dialogFlowId != null) {
                log.info("同步aicc侧dialogFlow发布状态，botId:{},dialogFlowId:{},auditStatus:{}", botId, dialogFlowId, auditStatus);
                dialogFlowService.updateDialogFlow(dialogFlowId, 0L, auditStatus);
            } else {
                log.error("找不到bot对应的dialogFlowId，botId:{}", botId);
            }
        } catch (Exception e) {
            if (AuditStatusEnum.PASS.equals(auditStatus)) {
                throw new ComException(ComErrorCode.UNKNOWN_ERROR, "同步aicc侧dialogFlow发布状态失败");
            }
            log.warn("[LogHub_Warn]同步aicc侧dialogFlow发布状态失败，botId:{}, 状态:{}", botId, auditStatus, e);
        }
    }

    @Override
    public PageResultObject<IdNamePair<Long, String>> queryBotListByRecordUserId(BotQuery query) {
        if (Objects.isNull(query.getRecordUserId())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "录音师id不能为空");
        }

        // 先不优化, 等后面接口慢了再优化
        List<Long> botIdList = botConfigService.queryBotIdListByRecordUserId(query.getRecordUserId());
        if (CollectionUtils.isEmpty(botIdList)) {
            return PageResultObject.of(Collections.emptyList());
        }
        query.setBotIdList(botIdList);
        PageHelper.startPage(query.getPageNum(), query.getPageSize());
        List<BotVO> botVOList = botPOMapper.listBotByCondition(query);

        List<IdNamePair<Long, String>> result = botVOList.stream()
                .map(bot -> IdNamePair.of(bot.getBotId(), bot.getName()))
                .collect(Collectors.toList());

        PageResultObject pageInfo = PageResultObject.of(result);
        PageResultObject tmp = PageResultObject.of(botVOList);
        pageInfo.setTotalElements(tmp.getTotalElements());
        pageInfo.setPages(tmp.getPages());
        pageInfo.setPageSize(tmp.getPageSize());
        return pageInfo;
    }

    @Override
    public void manualPublish(BotBO botQuery, Long userId) {
        if (Objects.isNull(botQuery)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "参数不能为空");
        }
        //同步AICC侧话术状态
        syncAiccDialogFlowStatus(botQuery.getBotId(), botQuery.getAuditStatusEnum());
        robotSnapshotService.updateRobotSnapshotPublishStatus(botQuery.getBotId());
        BotPO botPO = new BotPO();
        botPO.setBotId(botQuery.getBotId());
        botPO.setAuditStatus(botQuery.getAuditStatusEnum());
        botPO.setFailReason(botQuery.getSuggestion() == null ? null : botQuery.getSuggestion());
        botPOMapper.updateAuditStatusAndReason(botPO, botQuery.getAuditStatusEnum().getCode());
        if (AuditStatusEnum.FAIL.equals(botQuery.getAuditStatusEnum())) {
            operationLogService.save(botQuery.getBotId(), OperationLogTypeEnum.PUBLISH_AUDIT,
                    OperationLogResourceTypeEnum.PUBLISH_AUDIT, String.format("人工审核不通过【%s】", botQuery.getSuggestion()), userId);
        } else {
            operationLogService.save(botQuery.getBotId(), OperationLogTypeEnum.PUBLISH_AUDIT,
                    OperationLogResourceTypeEnum.PUBLISH_AUDIT, "人工审核通过", userId);
            douyinCallbackService.callback(botQuery.getBotId());
        }
    }

    /**
     * aicc绑定完成后回调该接口, 执行bot绑定逻辑
     * @param dialogFlowIds
     * @param tenantId
     * @return
     */
    @Override
    public List<BotConfigPO> bind(List<Long> dialogFlowIds, Long tenantId, Long userId) {
        if (CollectionUtils.isEmpty(dialogFlowIds)) {
            return new ArrayList<>();
        }
        //绑定bot
        botRefService.updateByDialogFlowIds(dialogFlowIds, tenantId);

        List<BotRefPO> botRefList = botRefService.getByDialogFlowIdList(dialogFlowIds);
        List<Long> botIdList = botRefList.stream()
                .map(BotRefPO::getBotId)
                .distinct()
                .collect(Collectors.toList());

        botIdList.forEach(botId -> {
            douyinCallbackService.callback(botId, tenantId);
        });

        // 添加操作日志
        createBindLog(tenantId, userId, botIdList);

        //查询bot音频配置
        return getBotConfig(dialogFlowIds);
    }

    private void createBindLog(Long tenantId, Long userId, List<Long> botIdList) {
        List<BotPO> botList = getByIdList(botIdList);
        TenantPO tenant = tenantService.selectByKey(tenantId);
        final String tenantName = Objects.nonNull(tenant) ? tenant.getCompanyName(): "tenantId=" + tenantId;
        List<OperationLogDTO> logList = botList.stream()
                .map(bot -> {
                    String detail = String.format("绑定bot到租户【%s】", tenantName);
                    OperationLogDTO operationLogDTO = new OperationLogDTO();
                    operationLogDTO.setBotId(bot.getBotId());
                    operationLogDTO.setResourceType(OperationLogResourceTypeEnum.BOT);
                    operationLogDTO.setType(OperationLogTypeEnum.BOT_BIND);
                    operationLogDTO.setOperatorId(userId);
                    operationLogDTO.setDetail(detail);
                    return operationLogDTO;
                }).collect(Collectors.toList());
        operationLogService.batchSave(logList);
    }

    private List<BotConfigPO> getBotConfig(List<Long> dialogFlowIds) {
        //查询botId列表
        List<Long> botIdList = botRefService.getBotIdList(dialogFlowIds);
        //获取bot音频配置
        if (CollectionUtils.isNotEmpty(botIdList)) {
            return botConfigService.getBotConfigListByBotId(botIdList);
        } else {
            return new ArrayList<>();
        }
    }

    @Override
    public void unbind(List<Long> dialogFlowIdList, Long userId) {
        if (CollectionUtils.isEmpty(dialogFlowIdList)) {
            return;
        }
        List<BotRefPO> botRefList = botRefService.getByDialogFlowIdList(dialogFlowIdList);


        //解绑bot
        botRefService.updateByDialogFlowIds(dialogFlowIdList, 0L);
        //清空动作配置
        dialogFlowIdList.forEach(dialogFlowId -> {
            clearActionConfig(dialogFlowId, false);
        });
        botRefList.forEach((refInfo) -> {
            douyinCallbackService.callback(refInfo.getBotId(), refInfo.getTenantId());
        });
        createUnbindLog(userId, dialogFlowIdList);
    }

    private void createUnbindLog(Long userId, List<Long> botIdList) {
        List<BotPO> botList = getByIdList(botIdList);
        List<OperationLogDTO> logList = botList.stream()
                .map(bot -> {
                    String botName = bot.getName();
                    OperationLogDTO operationLogDTO = new OperationLogDTO();
                    operationLogDTO.setBotId(bot.getBotId());
                    operationLogDTO.setResourceType(OperationLogResourceTypeEnum.BOT);
                    operationLogDTO.setType(OperationLogTypeEnum.BOT_BIND);
                    operationLogDTO.setOperatorId(userId);
                    operationLogDTO.setDetail("解绑");
                    return operationLogDTO;
                }).collect(Collectors.toList());
        operationLogService.batchSave(logList);
    }

    @Override
    public BatchBotSnapshotVO batchPublishBot(BotQuery botQuery, Long userId) {
        List<Long> botIdList = botQuery.getBotIdList();
        Assert.notNull(botIdList, "需要发布的bot不能为空");
        List<BotPO> botVOList = getByIdList(botIdList);
        Map<Long, BotPO> botNameToIdMap = botVOList.stream().collect(Collectors.toMap(BotPO::getBotId, botVO -> botVO));
        AtomicReference<Integer> successNum = new AtomicReference<>(0);
        AtomicReference<Integer> failNum = new AtomicReference<>(0);
        List<BotPO> failBotVOList = new ArrayList<>();
        botIdList.forEach(botId -> {
            try {
                BotPublishVO publishParam = new BotPublishVO();
                publishParam.setBotId(botId);
                publishParam.setIgnoreWarning(false);
                BotSnapshotVO botSnapshotVO = publish(publishParam, userId);
                if (CollectionUtils.isNotEmpty(botSnapshotVO.getSnapshotInvalidFailItemMsgList())) {
                    List<SnapshotInvalidFailItemMsg> errorMsr = botSnapshotVO.getSnapshotInvalidFailItemMsgList().stream().filter(snapshotInvalidFailItemMsg -> !snapshotInvalidFailItemMsg.getIsWarning()).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(errorMsr)) {
                        failNum.getAndSet(failNum.get() + 1);
                        failBotVOList.add(botNameToIdMap.get(botId));
                    } else {
                        successNum.getAndSet(successNum.get() + 1);
                    }
                } else {
                    successNum.getAndSet(successNum.get() + 1);
                }
            } catch (Exception e) {
                failNum.getAndSet(failNum.get() + 1);
                failBotVOList.add(botNameToIdMap.get(botId));
            }
        });
        BatchBotSnapshotVO botSnapshotVO = BatchBotSnapshotVO.builder()
                .successNum(successNum.get().intValue())
                .failNum(failNum.get().intValue())
                .failBotVOList(failBotVOList)
                .build();
        return botSnapshotVO;
    }

    @Override
    public void batchManualPublish(BotBO botQuery, Long userId) {
        List<Long> botIdList = botQuery.getBotIdList();
        if (CollectionUtils.isNotEmpty(botQuery.getBotIdList())) {
            List<Long> botIdListByFolderIdList = folderService.getBotIdListByFolderIdList(botQuery.getBotIdList());
            botIdList = MyCollectionUtils.merge(botIdList, botIdListByFolderIdList);
        }
        Assert.notNull(botIdList, "待审核的bot不能为空");
        botIdList.forEach(botId -> {
            botQuery.setBotId(botId);
            manualPublish(botQuery, userId);
        });
    }

    @Override
    public int batchBindAsrVocab(List<Long> botIdList, Long asrVocabId) {
        return botPOMapper.batchBindAsrVocab(botIdList, asrVocabId);
    }

    @Override
    public int batchBindAsrSelfLearning(List<Long> botIdList, Long asrSelfLearningDetailId) {
        return botPOMapper.batchBindAsrSelfLearning(botIdList, asrSelfLearningDetailId);
    }

    @Override
    public int bindAsrErrorCorrection(List<Long> botIdList, String asrErrorCorrectionDetailId, Long userId) {
        Example example = new Example(BotPO.class);
        example.createCriteria().andIn("botId", botIdList);
        BotPO updatePO = new BotPO();
        updatePO.setAsrErrorCorrectionDetailId(asrErrorCorrectionDetailId);
        updatePO.setUpdateTime(LocalDateTime.now());
        updatePO.setUpdateUserId(userId);
        return botPOMapper.updateByExampleSelective(updatePO, example);
    }

    @Override
    public int unBindAsrVocab(Long botId, Long asrVocabId) {
        return botPOMapper.unBindAsrVocab(botId, asrVocabId);
    }

    @Override
    public int unBindAsrSelfLearning(Long botId, Long asrSelfLearningDetailId) {
        return botPOMapper.unBindAsrSelfLearning(botId, asrSelfLearningDetailId);
    }

    @Override
    public int unBindAsrErrorCorrection(Long botId, String asrErrorCorrectionDetailId, Long userId) {
        Example example = new Example(BotPO.class);
        example.createCriteria().andEqualTo("botId", botId);
        BotPO updatePO = getById(botId);
        updatePO.setAsrErrorCorrectionDetailId(null);
        updatePO.setUpdateTime(LocalDateTime.now());
        updatePO.setUpdateUserId(userId);
        return botPOMapper.updateByExample(updatePO, example);
    }

    @Override
    public Optional<String> getNameByDialogFlowId(Long dialogFlowId) {
        BotPO botByDialogFlowId = botPOMapper.getBotByDialogFlowId(dialogFlowId);
        if (Objects.nonNull(botByDialogFlowId)) {
            return Optional.ofNullable(botByDialogFlowId.getName());
        }
        return Optional.empty();
    }

    private void clearActionConfig(Long dialogFlowId, boolean syncBotAuditStatus) {
        Long botId = botRefService.getBotId(dialogFlowId);
        if (Objects.isNull(botId)) {
            return;
        }
        //节点
        List<DialogBaseNodePO> nodeList = stepNodeService.getAllListByBotId(botId);
        if (CollectionUtils.isNotEmpty(nodeList)) {
            nodeList.forEach(node -> {
                if (org.apache.commons.lang.BooleanUtils.isTrue(node.getIsEnableAction()) && CollectionUtils.isNotEmpty(node.getActionList())) {
                    List<RuleActionParam> actionList = node.getActionList().stream().peek(action -> action.setSourceIdList(null)).collect(Collectors.toList());
                    node.setActionList(actionList);
                    stepNodeService.updateActionList(node);
                }
            });
        }
        //知识
        List<KnowledgePO> knowledgeList = knowledgeService.getAllListByBotId(botId);
        if (CollectionUtils.isNotEmpty(knowledgeList)) {
            knowledgeList.forEach(knowledge -> {
                if (org.apache.commons.lang.BooleanUtils.isTrue(knowledge.getIsEnableAction() != null && knowledge.getIsEnableAction() && CollectionUtils.isNotEmpty(knowledge.getActionList()))) {
                    List<RuleActionParam> actionList = knowledge.getActionList().stream().peek(action -> action.setSourceIdList(null)).collect(Collectors.toList());
                    knowledge.setActionList(actionList);
                    knowledgeService.updateActionList(knowledge);
                }
            });
        }
        //意向动作
        List<IntentRuleActionPO> intentRuleActionList = intentRuleActionService.getIntentRuleActionList(botId, null);
        if (CollectionUtils.isNotEmpty(intentRuleActionList)) {
            intentRuleActionList.forEach(intentAction -> {
                if (CollectionUtils.isNotEmpty(intentAction.getActionList())) {
                    List<RuleActionParam> actionList = intentAction.getActionList().stream().peek(action -> action.setSourceIdList(null)).collect(Collectors.toList());
                    intentAction.setActionList(actionList);
                    intentRuleActionService.updateActionList(intentAction);
                }
            });
        }

        // 解绑后将bot的状态改为草稿, 同时 aicc 那边需要把话术的已发布过标记清空(解绑后再次绑定也无法直接选择进行外呼, 需要发布审核后才可)
        updateAuditStatus(botId, AuditStatusEnum.DRAFT, syncBotAuditStatus);
    }

    @Override
    public String createQrCode(CreateBotQrCodeVO vo) {
        Long dialogFlowId = botRefService.getDialogFlowId(vo.getBotId());
        if (dialogFlowId != null) {
            vo.setDialogFlowId(dialogFlowId);
            vo.setDialogFlowName(vo.getName());
            String qrCodeId = dialogFlowService.createDialogFlowQcCode(vo);
            if (Objects.nonNull(qrCodeId)) {
                BotPO botPO = new BotPO();
                botPO.setBotId(vo.getBotId());
                botPO.setQrCodeId(qrCodeId);
                botPO.setQrCodeName(vo.getName());
                botPO.setPosterType(vo.getPosterType());
                updateNotNull(botPO);
            }
            return qrCodeId;
        }
        return null;
    }

    @Override
    public PageResultObject<BotVO> getPage(BotQuery botQuery) {
        PageHelper.startPage(botQuery.getPageNum(), botQuery.getPageSize());
        List<BotVO> botVOList = botPOMapper.listBotByCondition(botQuery);
        return PageResultObject.of(botVOList);
    }

    @Override
    public void batchUpdate(List<BotVO> updateList) {
        botPOMapper.batchUpdate(updateList);
    }

    @Override
    public List<SimpleBotInfo> querySimpleBotInfoList(BotListRequest request) {
        if (CollectionUtils.isEmpty(request.getBotIdList())) {
            return Collections.emptyList();
        }
        return botPOMapper.getBotByBotIdListIgnoreEnableStatus(request.getBotIdList()).stream()
                .filter(item -> BooleanUtils.isTrue(request.getContainsDeleted())
                        || EnabledStatusEnum.ENABLE.equals(item.getEnableStatus()))
                .map(bot -> {
                    SimpleBotInfo simpleBotInfo = MyBeanUtils.copy(bot, SimpleBotInfo.class);
                    simpleBotInfo.setDeleted(EnabledStatusEnum.DELETE.equals(bot.getEnableStatus()));
                    return simpleBotInfo;
                }).collect(Collectors.toList());
    }

    @Override
    public Set<String> getConcernNameSetByDialogFlowId(Long dialogFlowId) {
        Long botId = botRefService.getBotId(dialogFlowId);
        if (Objects.isNull(botId)) {
            return Collections.emptySet();
        }
        BotPO bot = getById(botId);
        if (Objects.isNull(bot)) {
            return Collections.emptySet();
        }
        Set<String> concernNameSet = new HashSet<>();
        stepService.getAllListByBotId(botId)
                .stream()
                .filter(step -> BooleanUtils.isTrue(step.getIsCustomerConcern()))
                .map(StepPO::getName)
                .forEach(concernNameSet::add);
        knowledgeService.getAllListByBotId(botId)
                .stream()
                .filter(knowledge -> BooleanUtils.isTrue(knowledge.getIsCustomerConcern()))
                .map(KnowledgePO::getName)
                .forEach(concernNameSet::add);
        specialAnswerConfigService.getByBotId(botId)
                .stream()
                .filter(config -> BooleanUtils.isTrue(config.getIsCustomerConcern()))
                .map(SpecialAnswerConfigPO::getName)
                .forEach(concernNameSet::add);
        return concernNameSet;
    }

    @Override
    public boolean checkEnableHumanInterventionByDialogFlowId(Long dialogFlowId) {
        Long botId = botRefService.getBotId(dialogFlowId);
        if (Objects.isNull(botId)) {
            return false;
        }
        return checkEnableHumanInterventionByBotId(botId);
    }

    @Override
    public boolean checkEnableHumanInterventionByBotId(Long botId) {
        BotPO bot = getById(botId);
        if (Objects.isNull(bot)) {
            return false;
        }
        return stepNodeService.getAllListByBotId(botId).stream().map(DialogBaseNodePO::getEnableHumanIntervention).anyMatch(BooleanUtils::isTrue)
                || knowledgeService.getAllListByBotId(botId).stream().map(KnowledgePO::getEnableHumanIntervention).anyMatch(BooleanUtils::isTrue)
                || specialAnswerConfigService.getByBotId(botId, EnabledStatusEnum.ENABLE).stream().map(SpecialAnswerConfigPO::getEnableHumanIntervention).anyMatch(BooleanUtils::isTrue);
    }

    @Override
    public void updateGenerateStatus(Long botId, BotGenerateStatusEnum generateStatus) {
        BotPO botPO = new BotPO();
        botPO.setBotId(botId);
        botPO.setGenerateStatus(generateStatus);
        updateNotNull(botPO);
    }

    @Override
    public List<BotNameCountDTO> countByNameList(List<String> botNameList) {
        if (CollectionUtils.isEmpty(botNameList)) {
            return Collections.emptyList();
        }
        return dialogFlowService.countDialogFlowByNameList(botNameList);
    }

    @Override
    public void updateDescription(Long botId, String description) {
        BotPO botPO = new BotPO();
        botPO.setBotId(botId);
        botPO.setDescription(description);
        updateNotNull(botPO);
    }

    @Override
    public boolean checkCanRewrite(Long botId) {
        BotPO bot = getById(botId);
        if (Objects.isNull(bot)) {
            throw new ComException(ComErrorCode.NOT_EXIST, "bot不存在");
        }
        List<DialogBaseNodePO> stepList = stepNodeService.getAllListByBotId(botId);
        return CollectionUtils.isNotEmpty(stepList);
    }

    @Override
    public SimpleBotApprovalResult publishAndApproval(BotApprovalRequest request) {
        Long botId = request.getBotId();
        if (Objects.isNull(botId)) {
            botId = botRefService.getBotId(request.getDialogFlowId());
        }
        BotPublishVO publishRequest = new BotPublishVO();
        publishRequest.setBotId(botId);
        publishRequest.setIgnoreWarning(true);
        SimpleBotApprovalResult result = new SimpleBotApprovalResult();

        try {
            BotSnapshotVO snapshotCreateResult = botService.publish(publishRequest, request.getUserId());
            if (AuditStatusEnum.PASS.equals(snapshotCreateResult.getAuditStatusEnum())) {
                // 自动审核通过
                result.setSuccess(true);
                result.setFailMsgList(Collections.emptyList());
                return result;
            } else {
                List<String> failMsgList = snapshotCreateResult.getSnapshotInvalidFailItemMsgList().stream()
                        .filter(item -> BooleanUtils.isNotTrue(item.getIsWarning()))
                        .map(SnapshotInvalidFailItemMsg::getFailMsg)
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(failMsgList)) {
                    // 包装错误信息
                    result.setSuccess(false);
                    result.setFailMsgList(failMsgList);
                } else {
                    // 手动审核
                    BotBO manualRequest = new BotBO();
                    manualRequest.setBotId(botId);
                    manualRequest.setAuditStatusEnum(AuditStatusEnum.PASS);
                    botService.manualPublish(manualRequest, request.getUserId());
                    result.setSuccess(true);
                    result.setFailMsgList(Collections.emptyList());
                }
            }
        } catch (Exception e) {
            log.warn("发布审核失败, botId:{}", botId, e);
            result.setSuccess(false);
            result.setFailMsgList(Collections.singletonList(String.format("系统未知异常:%s, 请联系管理员", e.getMessage())));
        }
        return result;
    }

    @Override
    public Long copyBotAndSubmitIntentTrain(CopyBotRequest request) {
        Long srcDialogFlowId = request.getSrcDialogFlowId();
        String name = request.getName();
        Long tenantId = request.getTenantId();
        Long userId = request.getUserId();

        if (Objects.isNull(srcDialogFlowId)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "srcDialogFlowId不能为空");
        }
        if (StringUtils.isBlank(name)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "name不能为空");
        }
        if (Objects.isNull(tenantId)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "tenantId不能为空");
        }
        Long srcBotId = botRefService.getBotId(srcDialogFlowId);
        if (Objects.isNull(srcBotId)) {
            throw new ComException(ComErrorCode.NOT_EXIST, "bot不存在");
        }
        BotPO srcBot = getById(srcBotId);
        if (Objects.isNull(srcBot)) {
            throw new ComException(ComErrorCode.NOT_EXIST, "bot不存在");
        }
        TtsVoiceEnum ttsVoice = null;
        if (StringUtils.isNotBlank(request.getTtsVoice())) {
            try {
                ttsVoice = TtsVoiceEnum.valueOf(request.getTtsVoice());
            } catch (Exception e) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "音色不合法:" + request.getTtsVoice());
            }
        }

        BotCreateRequestVO createRequest = MyBeanUtils.copy(srcBot, BotCreateRequestVO.class);
        createRequest.setCreateSource(BotCreateSourceEnum.COPY);
        createRequest.setTenantId(tenantId);
        createRequest.setName(name);
        // todo 上游传参过来
        createRequest.setSystemType(SystemEnum.AICC);
        createRequest.setSrcBotId(srcBotId);
        createRequest.setIntentLevelTagId(srcBot.getIntentLevelTagId());
        createRequest.setUserId(userId);
        createRequest.setCreateSource(BotCreateSourceEnum.OPEN_API);

        BotPO newBot = create(createRequest);

        // 检查是否开启算法
        IntentConfigPO intentConfig = intentConfigService.detail(newBot.getBotId());
        if (Objects.nonNull(intentConfig) && BooleanUtils.isTrue(intentConfig.getEnableAlgorithm())) {
            // 开启算法, 提交意图训练
            trainService.train(newBot.getBotId(), userId, "复制bot后提交意图训练", SystemEnum.OPE);
        }
        // 检查录音类型是否是合成音, 如果是合成音的话, 需要自动提交合成任务
        BotAudioConfigPO audioConfig = botConfigService.getAudioConfig(newBot.getBotId());
        if (Objects.nonNull(ttsVoice)) {
            TtsVoiceConfigPO ttsVoiceConfig = audioConfig.getTtsConfig();
            if (!AudioTypeEnum.COMPOSE.equals(audioConfig.getAudioType())) {
                ttsVoiceConfig = audioConfig.getSpliceTtsConfig();
            }
            ttsVoiceConfig.setTtsVoice(ttsVoice.name());
            ttsVoiceConfig.setTtsVoiceName(ttsVoice.getDesc());
            botConfigService.saveAudioConfig(newBot.getBotId(), audioConfig, userId);
        }
        if (AudioTypeEnum.COMPOSE.equals(audioConfig.getAudioType())) {
            log.info("bot:{} 开启了合成音, 自动提交合成任务", newBot.getBotId());
            answerAudioManagerService.startCompose(newBot.getBotId(), userId);
        }
        return newBot.getBotId();
    }

    @Override
    public void checkBotExistAndThrow(Long botId) {
        if (Objects.isNull(botId)) {
            throw new ComException(ComErrorCode.NOT_EXIST, "bot不存在");
        }
        BotPO bot = getById(botId);
        if (Objects.isNull(bot)) {
            throw new ComException(ComErrorCode.NOT_EXIST, "bot不存在");
        }
    }

    @Override
    public void checkBotExistAndThrowByDialogFlowId(Long dialogFlowId) {
        if (Objects.isNull(dialogFlowId)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "dialogFlowId不能为空");
        }
        checkBotExistAndThrow(botRefService.getBotId(dialogFlowId));
    }

    @Override
    public List<SimpleBotInfo> queryPublishedBotList(Long tenantId, String searchWord) {
        BotQuery query = new BotQuery();
        query.setTenantId(tenantId);
        query.setName(searchWord);
        query.setPublished(true);
        List<BotVO> botList = botPOMapper.listBotByCondition(query);
        return botList.stream().map(bot -> {
            SimpleBotInfo simpleBotInfo = new SimpleBotInfo();
            simpleBotInfo.setBotId(bot.getBotId());
            simpleBotInfo.setName(bot.getName());
            simpleBotInfo.setDialogFlowId(bot.getDialogFlowId());
            simpleBotInfo.setDeleted(false);
            return simpleBotInfo;
        }).collect(Collectors.toList());
    }

    @Override
    public BotInfo getByDialogFlowId(Long tenantId, Long dialogFlowId) {
        Long botId = botRefService.getBotId(dialogFlowId);
        if (Objects.isNull(botId)) {
            return null;
        }
        BotVO bot = botPOMapper.getBotByBotId(botId);
        if (Objects.isNull(bot)) {
            return null;
        }

        if (V3BotTypeEnum.isMagicTemplate(bot.getType())) {
            // todo 先不做校验
        } else {
            Long realTenantId = botRefService.getTenantIdByBotId(botId);
            if (!Objects.equals(tenantId, realTenantId)) {
                log.warn("botId:{},tenantId:{},dialogFlowId:{},tenantId:{}", botId, tenantId, dialogFlowId, realTenantId);
                return null;
            }
        }

        BotInfo botInfo = MyBeanUtils.copy(bot, BotInfo.class);
        botInfo.setAuditStatus(bot.getAuditStatus().name());
        botInfo.setEasyCallVersion(bot.getEasyCallVersion().name());
        botInfo.setType(bot.getType().name());
        botInfo.setTenantId(tenantId);
        botInfo.setDialogFlowId(dialogFlowId);
        return botInfo;
    }

    @Override
    public BotExportInfo exportBotInfo(Long tenantId, Long dialogFlowId) {
        Long botId = botRefService.getBotId(dialogFlowId);
        if (Objects.isNull(botId)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "话术不存在");
        }
        Long realTenantId = botRefService.getTenantIdByBotId(botId);
        if (!Objects.equals(realTenantId, tenantId)) {
            throw new ComException(ComErrorCode.FORBIDDEN, "无权限");
        }
        BotExportInfo botExportInfo = new BotExportInfo();
        botExportInfo.setWordUrl(botExportService.exportWord(botId, 1L, false));
        botExportInfo.setExcelUrl(botExportService.exportExcel(botId, 1L, false));
        botExportInfo.setXmindUrl(botExportService.exportXMind(botId, 1L));
        return botExportInfo;
    }

    @Override
    public List<BotInfo> getAllListByTenantId(Long tenantId, V3BotTypeEnum v3BotType) {
        BotQuery query = new BotQuery();
        query.setPageNum(1);
        query.setPageSize(Integer.MAX_VALUE);
        query.setTenantId(tenantId);
        if (v3BotType != null) {
            query.setTypeList(Collections.singletonList(v3BotType.getCode()));
        }
        PageResultObject<BotVO> botList = list(query);
        if (CollectionUtils.isEmpty(botList.getContent())) {
            return Collections.emptyList();
        }
        List<Long> botIdList = botList.getContent().stream().map(BotVO::getBotId).collect(Collectors.toList());
        List<BotConfigPO> botConfigList = botConfigService.getBotConfigListByBotId(botIdList);
        Map<Long, BotConfigPO> botConfigMap = botConfigList.stream().collect(Collectors.toMap(BotConfigPO::getBotId, botConfig -> botConfig));
        return botList.getContent().stream()
                .map(bot -> {
                    BotInfo botInfo = MyBeanUtils.copy(bot, BotInfo.class);
                    botInfo.setAuditStatus(bot.getAuditStatus().name());
                    botInfo.setTenantId(tenantId);
                    botInfo.setDialogFlowId(botRefService.getDialogFlowId(bot.getBotId()));
                    BotConfigPO config = botConfigMap.get(bot.getBotId());
                    if (Objects.nonNull(config)
                            && Objects.nonNull(config.getAudioConfig())
                            && Objects.nonNull(config.getAudioConfig().getAudioType())) {
                        botInfo.setAudioType(config.getAudioConfig().getAudioType().name());
                        TtsVoiceConfigPO ttsConfig = config.getAudioConfig().getSpliceTtsConfig();
                        if (AudioTypeEnum.COMPOSE.equals(config.getAudioConfig().getAudioType())) {
                            ttsConfig = config.getAudioConfig().getTtsConfig();;
                        }
                        if (Objects.nonNull(ttsConfig)) {
                            botInfo.setTtsVoice(ttsConfig.getTtsVoice());
                            botInfo.setTtsVoiceName(ttsConfig.getTtsVoiceName());
                        }
                    }
                    return botInfo;
                }).collect(Collectors.toList());
    }

    @Override
    public List<BotInfo> getDetailListByIdList(BotListRequest request) {
        if (CollectionUtils.isEmpty(request.getBotIdList())) {
            return Collections.emptyList();
        }
        List<Long> botIdList = request.getBotIdList();
        List<BotPO> botList = botPOMapper.getBotByBotIdListIgnoreEnableStatus(request.getBotIdList()).stream()
                .filter(item -> BooleanUtils.isTrue(request.getContainsDeleted())
                        || EnabledStatusEnum.ENABLE.equals(item.getEnableStatus()))
                .collect(Collectors.toList());

        // todo 代码重复
        List<BotConfigPO> botConfigList = botConfigService.getBotConfigListByBotId(botIdList);
        Map<Long, BotConfigPO> botConfigMap = botConfigList.stream().collect(Collectors.toMap(BotConfigPO::getBotId, botConfig -> botConfig));
        List<BotRefPO> botRefList = botRefService.getByBotIdList(botIdList);
        Map<Long, Long> botId2TenantIdMap = MyCollectionUtils.listToConvertMap(botRefList, BotRefPO::getBotId, BotRefPO::getTenantId);
        return botList.stream()
                .map(bot -> {
                    BotInfo botInfo = MyBeanUtils.copy(bot, BotInfo.class);
                    botInfo.setAuditStatus(bot.getAuditStatus().name());
                    botInfo.setTenantId(botId2TenantIdMap.get(bot.getBotId()));
                    botInfo.setDialogFlowId(botRefService.getDialogFlowId(bot.getBotId()));
                    BotConfigPO config = botConfigMap.get(bot.getBotId());
                    if (Objects.nonNull(config)
                            && Objects.nonNull(config.getAudioConfig())
                            && Objects.nonNull(config.getAudioConfig().getAudioType())) {
                        botInfo.setAudioType(config.getAudioConfig().getAudioType().name());
                        TtsVoiceConfigPO ttsConfig = config.getAudioConfig().getSpliceTtsConfig();
                        if (AudioTypeEnum.COMPOSE.equals(config.getAudioConfig().getAudioType())) {
                            ttsConfig = config.getAudioConfig().getTtsConfig();;
                        }
                        if (Objects.nonNull(ttsConfig)) {
                            botInfo.setTtsVoice(ttsConfig.getTtsVoice());
                            botInfo.setTtsVoiceName(ttsConfig.getTtsVoiceName());
                        }
                    }
                    return botInfo;
                }).collect(Collectors.toList());
    }

    @Override
    public List<MagicBotTemplateInfo> getMagicBotTemplateByIdList(BotListRequest request) {
        if (CollectionUtils.isEmpty(request.getBotIdList())) {
            return Collections.emptyList();
        }
        List<BotPO> botList = botPOMapper.getBotByBotIdListIgnoreEnableStatus(request.getBotIdList()).stream()
                .filter(item -> BooleanUtils.isTrue(request.getContainsDeleted())
                        || EnabledStatusEnum.ENABLE.equals(item.getEnableStatus()))
                .filter(item -> V3BotTypeEnum.isMagicTemplate(item.getType()))
                .collect(Collectors.toList());
        return convertToMagicTemplate(botList);
    }

    private List<MagicBotTemplateInfo> convertToMagicTemplate(List<BotPO> botList) {
        List<Long> botIdList = botList.stream().map(BotPO::getBotId).collect(Collectors.toList());
        List<VariablePO> variableList = variableService.getListByBotIdList(botIdList);
        Map<Long, List<VariablePO>> botVariableListMap = MyCollectionUtils.listToMapList(variableList, VariablePO::getBotId);

        Map<Long, Set<String>> usedVariableNameMap = variableService.getRealUsedVariableNameSetByBotIdList(botIdList);

        List<BotConfigPO> configList = botConfigService.getBotConfigListByBotId(botIdList);
        Map<Long, BotConfigPO> botConfigMap = MyCollectionUtils.listToMap(configList, BotConfigPO::getBotId);
        Map<Long, Long> botId2AudioRecorderIdMap = new HashMap<>();
        for (BotConfigPO config : configList) {
            if (Objects.nonNull(config.getAudioConfig())) {
                BotAudioConfigPO audioConfig = config.getAudioConfig();
                if (!AudioTypeEnum.COMPOSE.equals(audioConfig.getAudioType())
                        && Objects.nonNull(audioConfig.getRecordUserId())) {
                    botId2AudioRecorderIdMap.put(config.getBotId(), audioConfig.getRecordUserId());
                }
            }
        }
        return botList.stream()
                .map(item -> {
                    MagicBotTemplateInfo templateInfo = MyBeanUtils.copy(item, MagicBotTemplateInfo.class);
                    templateInfo.setEasyCallVersion(item.getEasyCallVersion().name());
                    return templateInfo;
                })
                .peek(template -> {
                    template.setDialogFlowId(botRefService.getDialogFlowId(template.getBotId()));
                    List<VariableInfoDTO> templaeVarList = botVariableListMap.getOrDefault(template.getBotId(), Collections.emptyList())
                            .stream()
                            .filter(variable -> VariableTypeEnum.isTemplateVariable(variable.getType()))
                            .map(variable -> {
                                VariableInfoDTO dto = new VariableInfoDTO();
                                dto.setId(variable.getId());
                                dto.setName(variable.getName());
                                dto.setType(variable.getType().name());
                                dto.setDesc(variable.getDesc());
                                dto.setDefaultValue(variable.getDefaultValue());
                                dto.setPrompt(variable.getPrompt());
                                dto.setTemplateSentence(variable.getTemplateSentence());
                                dto.setTemplateVariableConfigType(variable.getTemplateVariableConfigType().name());
                                return dto;
                            }).collect(Collectors.toList());
                    template.setTemplateVariableList(templaeVarList);

                    List<VariableInfoDTO> usedVariableList = botVariableListMap.getOrDefault(template.getBotId(), Collections.emptyList())
                            .stream()
                            .filter(item -> VariableTypeEnum.isCustomOrSystemVariable(item.getType()))
                            .filter(item -> usedVariableNameMap.getOrDefault(template.getBotId(), Collections.emptySet()).contains(item.getName()))
                            .map(variable -> {
                                VariableInfoDTO dto = new VariableInfoDTO();
                                dto.setId(variable.getId());
                                dto.setName(variable.getName());
                                dto.setType(variable.getType().name());
                                dto.setDesc(variable.getDesc());
                                dto.setDefaultValue(variable.getDefaultValue());
                                return dto;
                            }).collect(Collectors.toList());
                    template.setUsedVariableList(usedVariableList);
                    template.setRecordUserId(botId2AudioRecorderIdMap.get(template.getBotId()));
                    BotConfigPO config = botConfigMap.get(template.getBotId());
                    if (Objects.nonNull(config) && Objects.nonNull(config.getAudioConfig())) {
                        template.setAudioType(com.yiwise.dialogflow.api.enums.AudioTypeEnum.valueOf(config.getAudioConfig().getAudioType().name()));
                    }
                }).collect(Collectors.toList());
    }

    @Override
    public List<MagicBotTemplateInfo> getMagicBotTemplateByEasyCallVersion(@Nullable EasyCallVersionEnum easyCallVersion) {
        // todo 这个接口后续应该分页查询了
        List<BotPO> botList = botPOMapper.getAllPassMagicTemplateList();
        if (Objects.nonNull(easyCallVersion)) {
            botList = botList.stream()
                    .filter(item -> easyCallVersion.equals(item.getEasyCallVersion()))
                    .collect(Collectors.toList());
        }
        return convertToMagicTemplate(botList);
    }

    private AuditStatusEnum autoCheckDialogFlow(Long botId, RobotSnapshotPO oldSnapshot, RobotSnapshotPO newSnapshot) {
        //获取修改前后话术答案文本
        List<BaseAnswerContentVO> newAnswerList = answerManagerService.getAnswerFromSnapshot(newSnapshot);
        List<BaseAnswerContentVO> oldAnswerList = answerManagerService.getAnswerFromSnapshot(oldSnapshot);
        return checkAudioResult(botId, oldSnapshot, newSnapshot, newAnswerList, oldAnswerList);
    }

    private AuditStatusEnum checkAudioResult(Long botId, RobotSnapshotPO oldSnapshot, RobotSnapshotPO newRobotSnapshot,
                                             List<BaseAnswerContentVO> newAnswerList, List<BaseAnswerContentVO> oldAnswerList) {
        // 检查答案是否更新, 如果更新了, 则需要人工审核
        if (isModifiedAnswer(oldAnswerList, newAnswerList)) {
            log.info("bot答案更新, botId:{}", botId);
            return AuditStatusEnum.PENDING;
        }

        //判断录音是否更新
        BotAudioConfigPO botAudioConfig = newRobotSnapshot.getBotConfig().getAudioConfig();
        if (!botAudioConfig.getAudioType().equals(AudioTypeEnum.COMPOSE)) {
            log.debug("判断真人录音是否有更新，botId:{}", botId);
            Optional<LocalDateTime> lastTime = answerAudioManagerService.getLastAudioCreateTime(botId);
            if (lastTime.isPresent()) {
                if (oldSnapshot.getCreateTime().isBefore(lastTime.get())) {
                    log.debug("录音存在更新，转为人工审核状态，botId:{}", botId);
                    return AuditStatusEnum.PENDING;
                } else {
                    log.debug("录音没有更新,审核通过，botId:{}", botId);
                    return AuditStatusEnum.PASS;
                }
            } else {
                //最近一次录音时间为空,则认为录音是第一次上传，认为有更新
                log.debug("最近一次录音时间为空，认为录音有更新，转为人工审核状态,botId:{}", botId);
                return AuditStatusEnum.PENDING;
            }
        } else {
            log.debug("非真人录音审核通过,botId:{}", botId);
            return AuditStatusEnum.PASS;
        }
    }

    private boolean isModifiedAnswer(List<? extends BaseAnswerContent> oldAnswerList,
                                     List<? extends BaseAnswerContent> newAnswerList) {
        if (CollectionUtils.isEmpty(oldAnswerList)) {
            return true;
        }
        if (CollectionUtils.isEmpty(newAnswerList)) {
            return true;
        }
        Set<String> newAnswers = new HashSet<>();
        Set<String> oldAnswers = new HashSet<>();
        newAnswerList.forEach(answer -> newAnswers.add(answer.getText()));
        oldAnswerList.forEach(answer -> oldAnswers.add(answer.getText()));
        //话术未更新
        return !equalsList(newAnswers, oldAnswers);
    }

    public boolean equalsList(Set<String> newSet, Set<String> oldSet) {
        // 大小比较
        if (newSet.size() != oldSet.size()) {
            return false;
        }
        String[] arr1 = newSet.toArray(new String[]{});
        String[] arr2 = oldSet.toArray(new String[]{});
        Arrays.sort(arr1);
        Arrays.sort(arr2);
        return Arrays.equals(arr1, arr2);
    }

    @Override
    public List<V3BotTypeEnum> availableCopyType(Long botId) {
        BotPO bot = getById(botId);
        if (Objects.isNull(bot)) {
            return Collections.emptyList();
        }
        if (V3BotTypeEnum.isCommon(bot.getType())) {
            return Arrays.asList(V3BotTypeEnum.COMMON, V3BotTypeEnum.MAGIC_TEMPLATE);
        }
        // 轻量化BOT模板复制到旗舰版,需要校验是否存在模板变量
        if (V3BotTypeEnum.isMagicTemplate(bot.getType())) {
            return variableService.existsTemplateVariable(botId)
                    ? Collections.singletonList(V3BotTypeEnum.MAGIC_TEMPLATE)
                    : Arrays.asList(V3BotTypeEnum.COMMON, V3BotTypeEnum.MAGIC_TEMPLATE);
        }
        return Collections.emptyList();
    }

    @Override
    public List<String> getDependentSceneNameList(Long botId) {
        if (Objects.isNull(botId)) {
            return Collections.emptyList();
        }
        if (!V3BotTypeEnum.isMagicTemplate(getBotType(botId))) {
            return Collections.emptyList();
        }
        DependSubSceneQueryDTO query = new DependSubSceneQueryDTO();
        query.setBotIdList(Collections.singletonList(botId));
        return callActivitySubSceneService.getSimpleSubSceneByDependBotIdList(query).stream()
                .filter(item -> EnabledStatusEnum.ENABLE.equals(item.getParentSceneEnabledStatus()))
                .map(SimpleActivitySubSceneDTO::getName)
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public V3BotTypeEnum getBotType(Long botId) {
        return Optional.ofNullable(botId).map(this::getById).map(BotPO::getType).orElse(null);
    }

    @Override
    public MagicBotInfo createMagicBot(MagicBotCreateRequest request) {
        preCreateMagicBot(request);
        BotPO template = getById(request.getBotTemplateId());

        // 复制 bot
        BotCreateRequestVO createRequest = MyBeanUtils.copy(template, BotCreateRequestVO.class);
        createRequest.setType(V3BotTypeEnum.MAGIC);
        createRequest.setUserId(request.getUserId());
        createRequest.setFolderId(null);
        createRequest.setTenantId(request.getTenantId());
        createRequest.setCreateSource(BotCreateSourceEnum.MAGIC_AICC);
        createRequest.setSrcBotId(request.getBotTemplateId());
        createRequest.setCustomerTrackType(request.getCustomerTrackType());
        BotPO magicBot = create(createRequest);

        // 更新模板变量默认值
        // todo 批量更新
        List<VariablePO> newVariableList = variableService.getTemplateVariableByBotIdList(Collections.singletonList(magicBot.getBotId()));
        for (VariablePO variable : newVariableList) {
            String defaultValue = request.getTemplateVariableMap().get(variable.getName());
            variable.setDefaultValue(defaultValue);
            variableService.update(variable, request.getUserId());
        }

        // 自动审核
        BotApprovalRequest approvalRequest = new BotApprovalRequest();
        approvalRequest.setTenantId(request.getTenantId());
        approvalRequest.setBotId(magicBot.getBotId());
        approvalRequest.setUserId(request.getUserId());
        SimpleBotApprovalResult approvalResult = publishAndApproval(approvalRequest);

        if (BooleanUtils.isNotTrue(approvalResult.isSuccess())) {
            throw new ComException(ComErrorCode.UNKNOWN_ERROR, "bot 创建成功, 但是自动审核失败: " + approvalResult.getFailMsgList());
        }

        magicBot = getById(magicBot.getBotId());
        MagicBotInfo result = new MagicBotInfo();
        result.setBotId(magicBot.getBotId());
        result.setName(magicBot.getName());
        result.setDialogFlowId(botRefService.getDialogFlowId(magicBot.getBotId()));
        result.setIntentLevelTagId(magicBot.getIntentLevelTagId());

        return result;
    }

    @Override
    public Boolean preCreateMagicBot(MagicBotCreateRequest request) {
        if (Objects.isNull(request.getTenantId())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "tenantId不能为空");
        }
        if (Objects.isNull(request.getBotTemplateId())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "botTemplateId不能为空");
        }
        //if (MapUtils.isEmpty(request.getTemplateVariableMap())) {
        //    throw new ComException(ComErrorCode.VALIDATE_ERROR, "templateVariableMap不能为空");
        //}
        BotPO template = getById(request.getBotTemplateId());
        if (Objects.isNull(template)) {
            throw new ComException(ComErrorCode.NOT_EXIST, "bot模板不存在");
        }
        if (!V3BotTypeEnum.isMagicTemplate(template.getType())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "bot模板类型不合法");
        }

        List<VariablePO> usedTemplateVariableList = variableService.getUsedTemplateVarListByBotId(request.getBotTemplateId());

        // 校验变量名称是否和传过来的是否能对的上
        if (MapUtils.isEmpty(request.getTemplateVariableMap())) {
            request.setTemplateVariableMap(new HashMap<>());
        }
        if (CollectionUtils.isEmpty(usedTemplateVariableList)) {
            usedTemplateVariableList = new ArrayList<>();
        }

        // 收集所有敏感词信息
        Map<String, List<String>> allSensitiveWords = new HashMap<>();
        boolean hasSensitiveWords = false;

        List<VariableInfoDTO> usingTemplateVariableList = variableService.getUsingTemplateVariableListByDialogFlowId(request.getBotTemplateId());
        Set<String> requiredTemplateVariableNameSet = usingTemplateVariableList.stream().map(VariableInfoDTO::getName).collect(Collectors.toSet());
        for (VariablePO var : usedTemplateVariableList) {
            String defaultValue = request.getTemplateVariableMap().get(var.getName());
            if (StringUtils.isBlank(defaultValue) && requiredTemplateVariableNameSet.contains(var.getName())) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, String.format("模板变量默认值不能为空, 变量名:%s", var.getName()));
            }
            if (StringUtils.isNotBlank(defaultValue)) {
                // 校验变量值敏感词信息
                SensitiveWordsDetectResultBO detectResult = sensitiveWordsService.detect(defaultValue);
                if (BooleanUtils.isNotTrue(detectResult.getIsSafe())) {
                    hasSensitiveWords = true;
                    allSensitiveWords.put(var.getName(), detectResult.getWords());
                }
            }
        }
        
        // 如果存在敏感词，统一抛出异常
        if (hasSensitiveWords) {
            StringBuilder errorMsg = new StringBuilder("以下变量包含敏感词：");
            for (Map.Entry<String, List<String>> entry : allSensitiveWords.entrySet()) {
                errorMsg.append(String.format("\n变量[%s]值包含敏感词: [%s]", 
                    entry.getKey(), String.join(", ", entry.getValue())));
            }
            throw new ComException(ComErrorCode.VALIDATE_ERROR, errorMsg.toString());
        }
        
        return true;
    }

    @Override
    public BotAttributeDTO getBotAttributeByDialogFlowId(Long dialogFlowId) {
        Long botId = botRefService.getBotId(dialogFlowId);
        if (Objects.isNull(botId)) {
            BotAttributeDTO notFound = new BotAttributeDTO();
            notFound.setBotNotFound(true);
            notFound.setBotId(dialogFlowId);
            notFound.setContainsSmsTemplateInJobConfig(false);
            notFound.setContainsTransToHuman(false);
            return notFound;
        }
        List<DialogBaseNodePO> nodeList = stepNodeService.getAllListByBotId(botId);
        List<KnowledgePO> knowledgeList = knowledgeService.getAllListByBotId(botId);
        List<SpecialAnswerConfigPO> specialAnswerConfigList = specialAnswerConfigService.getByBotId(botId);
        List<IntentRuleActionPO> ruleList = intentRuleActionService.getIntentRuleActionList(botId, Collections.singletonList(ActionCategoryEnum.SEND_SMS));
        BotAttributeDTO attribute = new BotAttributeDTO();

        attribute.setBotId(botId);
        attribute.setBotNotFound(false);
        attribute.setContainsTransToHuman(hasTransToHuman(nodeList, knowledgeList, specialAnswerConfigList));
        attribute.setContainsSmsTemplateInJobConfig(containsSmsTemplateInJobConfig(nodeList, knowledgeList, specialAnswerConfigList, ruleList));
        return attribute;
    }

    @Override
    public PageResultObject<SimpleBotInfo> searchBot(BotSearchRequest request) {
        BotQuery botQuery = new BotQuery();
        botQuery.setName(request.getSearch());
        List<Integer> typeList = new ArrayList<>(2);
        typeList.add(V3BotTypeEnum.COMMON.getCode());
        if (BooleanUtils.isTrue(request.getContainsMagicBot())) {
            typeList.add(V3BotTypeEnum.MAGIC.getCode());
        }
        botQuery.setTypeList(typeList);
        PageHelper.startPage(request.getPageNum(), request.getPageSize());
        List<BotVO> botList = botPOMapper.listBotByCondition(botQuery);

        PageResultObject<BotVO> tmp = PageResultObject.of(botList);

        List<Long> botIdList = botList.stream().map(BotVO::getBotId).collect(Collectors.toList());
        List<BotRefPO> refList = botRefService.getByBotIdList(botIdList);
        Map<Long, Long> botId2dialogFlowId = MyCollectionUtils.listToConvertMap(refList, BotRefPO::getBotId, BotRefPO::getDialogFlowId);

        List<SimpleBotInfo> simpleBotList = botList.stream()
                .map(vo -> {
                    SimpleBotInfo simpleBot = new SimpleBotInfo();
                    simpleBot.setBotId(vo.getBotId());
                    simpleBot.setDeleted(false);
                    simpleBot.setName(vo.getName());
                    simpleBot.setDialogFlowId(botId2dialogFlowId.get(vo.getBotId()));
                    return simpleBot;
                })
                .collect(Collectors.toList());
        return PageResultObject.of(simpleBotList, request.getPageNum(), request.getPageSize(), (int) tmp.getTotalElements());
    }

    private boolean hasTransToHuman(List<DialogBaseNodePO> nodeList,
                                    List<KnowledgePO> knowledgeList,
                                    List<SpecialAnswerConfigPO> specialAnswerConfigList) {
        if (CollectionUtils.isNotEmpty(nodeList)) {
            for (DialogBaseNodePO node : nodeList) {
                if (node instanceof DialogJumpNodePO) {
                    DialogJumpNodePO jumpNode = (DialogJumpNodePO) node;
                    if (JumpTypeEnum.HUMAN_SERVICE.equals(jumpNode.getJumpType())) {
                        return true;
                    }
                }
            }
        }
        if (CollectionUtils.isNotEmpty(knowledgeList)) {
            for (KnowledgePO knowledge : knowledgeList) {
                if (CollectionUtils.isNotEmpty(knowledge.getAnswerList())) {
                    for (KnowledgeAnswer knowledgeAnswer : knowledge.getAnswerList()) {
                        if (PostActionTypeEnum.HUMAN_SERVICE.equals(knowledgeAnswer.getPostAction())) {
                            return true;
                        }
                    }
                }
            }
        }

        if (CollectionUtils.isNotEmpty(specialAnswerConfigList)) {
            for (SpecialAnswerConfigPO specialAnswerConfig : specialAnswerConfigList) {
                if (CollectionUtils.isNotEmpty(specialAnswerConfig.getAnswerList())) {
                    for (KnowledgeAnswer specialAnswerAnswer : specialAnswerConfig.getAnswerList()) {
                        if (PostActionTypeEnum.HUMAN_SERVICE.equals(specialAnswerAnswer.getPostAction())) {
                            return true;
                        }
                    }
                }
            }
        }

        return false;
    }

    private boolean containsSmsTemplateInJobConfig(List<DialogBaseNodePO> nodeList,
                                                   List<KnowledgePO> knowledgeList,
                                                   List<SpecialAnswerConfigPO> specialAnswerConfigList,
                                                   List<IntentRuleActionPO> ruleActionList) {

        List<RuleActionParam> allRuleActionList = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(nodeList)) {
            for (DialogBaseNodePO node : nodeList) {
                if (BooleanUtils.isTrue(node.getIsEnableAction()) && CollectionUtils.isNotEmpty(node.getActionList())) {
                    allRuleActionList.addAll(node.getActionList());
                }
            }
        }

        if (CollectionUtils.isNotEmpty(knowledgeList)) {
            for (KnowledgePO knowledge : knowledgeList) {
                if (BooleanUtils.isTrue(knowledge.getIsEnableAction()) && CollectionUtils.isNotEmpty(knowledge.getActionList())) {
                    allRuleActionList.addAll(knowledge.getActionList());
                }
            }
        }

        if (CollectionUtils.isNotEmpty(specialAnswerConfigList)) {
            for (SpecialAnswerConfigPO specialAnswerConfig : specialAnswerConfigList) {
                if (BooleanUtils.isTrue(specialAnswerConfig.getIsEnableAction()) && CollectionUtils.isNotEmpty(specialAnswerConfig.getActionList())) {
                    allRuleActionList.addAll(specialAnswerConfig.getActionList());
                }
            }
        }

        if (CollectionUtils.isNotEmpty(ruleActionList)) {
            for (IntentRuleActionPO rule : ruleActionList) {
                if (CollectionUtils.isNotEmpty(rule.getActionList())) {
                    allRuleActionList.addAll(rule.getActionList());
                }
            }
        }

        for (RuleActionParam ruleActionParam : allRuleActionList) {
            if (ActionCategoryEnum.SEND_SMS.equals(ruleActionParam.getActionType())
                    && SmsTemplateSourceEnum.CALL_JOB.equals(ruleActionParam.getSmsTemplateSource())) {
                return true;
            }
        }

        return false;
    }

    private String generateTimeSequence() {
        LocalDateTime now = LocalDateTime.now();
        return String.format("%s%s%s%s%s%s", now.getYear() % 100, now.getMonthValue(), now.getDayOfMonth(),
                now.getHour(), now.getMinute(), now.getSecond());
    }

    @Override
    public void batchUpdateVisibleStatus(BatchUpdateVisibleStatusRequestVO request, Long userId) {
        List<Long> botIdList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(request.getFolderIdList())) {
            List<Long> finalFolderIdList = new ArrayList<>(request.getFolderIdList());
            request.getFolderIdList().stream().map(folderService::subFolderIdList).forEach(finalFolderIdList::addAll);
            botIdList.addAll(folderService.getBotIdListByFolderIdList(finalFolderIdList));
        }
        if (CollectionUtils.isNotEmpty(request.getBotIdList())) {
            botIdList.addAll(request.getBotIdList());
        }
        if (CollectionUtils.isEmpty(botIdList)) {
            return;
        }
        EnabledStatusEnum visibleStatus = request.getVisibleStatus();
        for (Long botId : botIdList) {
            if (botPOMapper.updateVisibleStatus(botId, visibleStatus.getCode()) != 0) {
                syncAiccDialogflowVisibleStatus(botId, visibleStatus);
                operationLogService.save(botId, OperationLogTypeEnum.BOT_CONFIG, OperationLogResourceTypeEnum.BOT,
                        "任务" + (EnabledStatusEnum.ENABLE.equals(visibleStatus) ? "可见" : "不可见"), userId);
            }
        }
    }

    @Override
    public Boolean preCreateMagicActivityConfig(MagicActivityConfigCreateRequest request) {
        return magicActivityConfigService.preCreate(request);
    }

    @Override
    public String createMagicActivityConfig(MagicActivityConfigCreateRequest request) {
        return magicActivityConfigService.create(request);
    }

    @Override
    public Boolean checkEnableLLMChat(Long botId) {
        List<SpecialAnswerConfigPO> specialAnswerConfigList = specialAnswerConfigService.getByBotId(botId);
        if (CollectionUtils.isNotEmpty(specialAnswerConfigList)) {
            for (SpecialAnswerConfigPO po : specialAnswerConfigList) {
                if (SpecialAnswerConfigPO.LLM.equals(po.getName())) {
                    if (EnabledStatusEnum.ENABLE.equals(po.getEnabledStatus())) {
                        return true;
                    }
                }
            }
        }

        List<StepPO> stepList = stepService.getAllListByBotId(botId);

        if (CollectionUtils.isNotEmpty(stepList)) {
            for (StepPO step : stepList) {
                if (StepSubTypeEnum.isLlm(step.getSubType())) {
                    return true;
                }
            }
        }
        return false;
    }

    private void syncAiccDialogflowVisibleStatus(Long botId, EnabledStatusEnum visibleStatus) {
        try {
            Long dialogFlowId = botRefService.getDialogFlowId(botId);
            if (dialogFlowId != null) {
                log.info("同步aicc侧dialogFlow可见状态，botId:{},dialogFlowId:{},visibleStatus:{}", botId, dialogFlowId, visibleStatus);
                dialogFlowService.updateVisibleStatus(dialogFlowId, visibleStatus);
            } else {
                log.error("找不到bot对应的dialogFlowId，botId:{}", botId);
            }
        } catch (Exception e) {
            log.warn("[LogHub_Warn]同步aicc侧dialogFlow可见状态失败，botId:{}, 状态:{}", botId, visibleStatus, e);
        }
    }
}
