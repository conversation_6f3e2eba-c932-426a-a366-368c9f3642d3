package com.yiwise.dialogflow.utils;

import com.yiwise.base.common.text.TextPlaceholderElement;
import com.yiwise.base.common.text.TextPlaceholderTypeEnum;
import com.yiwise.dialogflow.engine.share.common.AnswerPlaceholderSplitter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.Map;
import java.util.stream.Collectors;

public class AnswerTextUtils {

    // 开头需要处理的符号
    private static final String PREFIX_SYMBOLS = "~-－/*'\"、“”‘’《》〈〉（）()［］[]＿.【】〔〕﹏，。？！：；?!,.:;\n ";

    // 结尾需要处理的符号
    private static final String SUFFIX_SYMBOLS = "，。,.\n ";

    public static boolean compareAnswerIsEqual(String answerText, String otherAnswerText) {
        if (StringUtils.equals(answerText, otherAnswerText)) {
            return true;
        }
        return StringUtils.equals(removeAnswerPrefixAndSuffixSymbols(answerText), removeAnswerPrefixAndSuffixSymbols(otherAnswerText));
    }

    public static String removeAnswerPrefixAndSuffixSymbols(String answerText) {
        if (StringUtils.isBlank(answerText)) {
            return answerText;
        }
        answerText = StringUtils.stripStart(answerText, PREFIX_SYMBOLS);
        answerText = StringUtils.stripEnd(answerText, SUFFIX_SYMBOLS);
        return answerText;
    }

    /**
     * 将答案模版中的变量渲染为真实值
     */
    public static String renderTemplate(String template, Map<String, String> varMap) {
        if (StringUtils.isBlank(template)) {
            return template;
        }
        if (varMap == null) {
            varMap = Collections.emptyMap();
        }
        AnswerPlaceholderSplitter splitter = new AnswerPlaceholderSplitter(template, false);
        StringBuilder sb = new StringBuilder();
        for (TextPlaceholderElement element : splitter.getTextPlaceholderList()) {
            if (TextPlaceholderTypeEnum.TEXT.equals(element.getType())) {
                sb.append(element.getValue());
            } else {
                sb.append(varMap.getOrDefault(element.getValue(), ""));
            }
        }
        return sb.toString();
    }

    public static String convertTemplateOnVariableRename(String template, String oldVariableName, String newVariableName) {
        if (StringUtils.isBlank(template) || StringUtils.isBlank(oldVariableName) || StringUtils.isBlank(newVariableName)) {
            return template;
        }

        AnswerPlaceholderSplitter splitter = new AnswerPlaceholderSplitter(template, false);
        if (CollectionUtils.isNotEmpty(splitter.getPlaceholderSet()) && splitter.getPlaceholderSet().contains(oldVariableName)) {
            StringBuilder sb = new StringBuilder();
            splitter.getTextPlaceholderList().forEach(placeholder -> {
                if (TextPlaceholderTypeEnum.PLACE_HOLDER.equals(placeholder.getType())) {
                    if (oldVariableName.equals(placeholder.getValue())) {
                        sb.append("${").append(newVariableName).append("}");
                    } else {
                        sb.append("${").append(placeholder.getValue()).append("}");
                    }
                } else {
                    sb.append(placeholder.getValue());
                }
            });
            return sb.toString();
        }
        return template;

    }

    public static boolean containsText(String srcText, String searchText) {
        return new AnswerPlaceholderSplitter(srcText, false).getTextList().stream().anyMatch(text -> text.contains(searchText));
    }

    public static String replaceText(String srcText, String searchText, String replaceText) {
        return new AnswerPlaceholderSplitter(srcText, false).getTextPlaceholderList().stream()
                .map(item -> {
                    TextPlaceholderTypeEnum type = item.getType();
                    if (TextPlaceholderTypeEnum.TEXT.equals(type)) {
                        return item.getValue().replace(searchText, replaceText);
                    }
                    if (TextPlaceholderTypeEnum.PLACE_HOLDER.equals(type)) {
                        return String.format("${%s}", item.getValue());
                    }
                    return item.getValue();
                }).collect(Collectors.joining());
    }
}
