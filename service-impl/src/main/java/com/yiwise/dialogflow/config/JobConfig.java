package com.yiwise.dialogflow.config;

import com.yiwise.batch.config.AbstractJobConfig;
import com.yiwise.dialogflow.batch.listener.MDCReplaceListener;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.AbstractJob;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2022/11/29
 */
@Configuration
public class JobConfig extends AbstractJobConfig {

    @Resource
    private MDCReplaceListener mdcReplaceListener;

    @Bean
    public Job semanticAnalysisExportJob(Step semanticAnalysisDetailExportStep) {
        Job job = exportJobWithOSSUpdate(springBatchJobTypeService.getByName("SEMANTIC_ANALYSIS_DETAIL_EXPORT_STEP"), semanticAnalysisDetailExportStep);
        if (job instanceof AbstractJob) {
            ((AbstractJob) job).registerJobExecutionListener(mdcReplaceListener);
        }
        return job;
    }
}
