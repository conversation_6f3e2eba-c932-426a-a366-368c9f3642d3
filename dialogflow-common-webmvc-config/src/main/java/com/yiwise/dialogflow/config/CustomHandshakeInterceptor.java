package com.yiwise.dialogflow.config;

import com.yiwise.base.common.utils.NetUtils;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.socket.WebSocketHandler;
import org.springframework.web.socket.server.HandshakeInterceptor;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 29/08/2018
 */
public class CustomHandshakeInterceptor implements HandshakeInterceptor {

    @Override
    public boolean beforeHandshake(ServerHttpRequest request, ServerHttpResponse response, WebSocketHandler wsHandler, Map<String, Object> attributes) throws Exception {
        attributes.put("endPoint", request.getURI());

        String ip = NetUtils.getClientRealIp(request);
        String host = request.getHeaders().getFirst("host");
        attributes.put("host", host);
        attributes.put("ip", ip);
        return true;
    }

    @Override
    public void afterHandshake(ServerHttpRequest request, ServerHttpResponse response, WebSocketHandler wsHandler, Exception exception) {
    }
}
